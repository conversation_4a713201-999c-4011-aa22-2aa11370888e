//
//  IMYHTTPInternal.h
//  IMYHTTP
//
//  Created by mario on 16/4/6.
//  Copyright © 2016年 Ivan Chua. All rights reserved.
//

#ifndef IMYHTTPInternal_h
#define IMYHTTPInternal_h

#import <Foundation/Foundation.h>

#if TARGET_IPHONE_SIMULATOR
#define IMYHTTPAssert(condition, desc) NSAssert(condition, desc)
#elif TARGET_OS_IPHONE
#define IMYHTTPAssert(condition, desc)
#endif

FOUNDATION_EXTERN NSString * const IMYNetworkingOriginalURLKey;
FOUNDATION_EXTERN NSString * const IMYNetworkingErrorKey;
FOUNDATION_EXTERN NSString * const IMYNetworkingUserInfoKey;
FOUNDATION_EXTERN NSString * const IMYNetworkingUserTokenKey;
FOUNDATION_EXTERN NSString * const IMYNetworkingVirtualTokenKey;

/// 通过 userInfo 获取 id<IMYNetworkingTaskMetricsProtocol> metrics;
FOUNDATION_EXTERN NSString * const IMYNetworkingTaskMetricsKey;

/// 新的网络性能数据key
FOUNDATION_EXTERN NSString * const IMYNetworkingPerformanceMetricsKey;

@protocol IMYNetworkingTaskMetricsProtocol

@property (nonatomic, copy, readonly) NSString *url;
@property (nonatomic, assign, readonly) int64_t tm_pnt_send;   // 请求发送时间（毫秒，时间戳）
@property (nonatomic, assign, readonly) NSInteger tm_dur_dns;    // DNS解析时间（毫秒，时长）
@property (nonatomic, assign, readonly) NSInteger tm_dur_conn;   // TCP建立连接时间（毫秒，时长）
@property (nonatomic, assign, readonly) NSInteger tm_dur_ssl;    // SSL握手时间（毫秒，时长）
@property (nonatomic, assign, readonly) NSInteger tm_dur_firstP; // 首包时间（毫秒，时长）
@property (nonatomic, assign, readonly) NSInteger tm_dur_end;    // 请求结束时间（毫秒，时长）
@property (nonatomic, assign, readonly) NSInteger status_code;    // HTTP状态码
@property (nonatomic, assign, readonly) NSInteger err_code;   // 错误码
@property (nonatomic, assign, readonly) NSInteger net_type;   // 网络类型
@property (nonatomic, copy, readonly) NSString *content_type;
@property (nonatomic, assign, readonly) NSInteger recv_bytes; // 接受的字节数
@property (nonatomic, assign, readonly) NSInteger sent_bytes; // 发送的字节数
@property (nonatomic, assign, readonly) NSInteger content_length;
@property (nonatomic, copy, readonly) NSString *ip;         // IP地址
@property (nonatomic, assign, readonly) NSInteger method;     // HTTP method
@property (nonatomic, copy, readonly) NSString *carrier;    // 运营商名称

@optional
- (id)_psd_http_element;

@end



#endif /* IMYHTTPInternal_h */
