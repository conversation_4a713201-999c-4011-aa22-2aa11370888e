//
//  AFHTTPSessionManager+IMYNetworkingMetrics.m
//  IMYNetworking
//

#import "AFHTTPSessionManager+IMYNetworkingMetrics.h"
#import "IMYConfigsCenter.h"
#import "NSURLSessionDataTask+IMYNetworkingPrivate.h"
#import "_PSDHTTPElement.h"
#import "NSURLSessionTask+Poseidon.h"

@implementation AFHTTPSessionManager (IMYNetworkingMetrics)

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics {
    // 根据配置开关决定是否启用网络性能数据收集
    BOOL enableNetworkMetrics = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.errors.meiyou_net_hook"];

    if (!enableNetworkMetrics) {
        return;
    }

    if (@available(iOS 10.0, *)) {
        _PSDHTTPElement *element = task._psd_http_element;
        if (element) {
            for (NSURLSessionTaskTransactionMetrics *m in metrics.transactionMetrics) {
                if (m.fetchStartDate) {
                    element.tm_pnt_send = [m.fetchStartDate timeIntervalSince1970] * 1000;
                }
                if (m.responseEndDate) {
                    element.tm_dur_end = [m.responseEndDate timeIntervalSince1970] * 1000 - element.tm_pnt_send;
                }
                if (m.domainLookupStartDate && m.domainLookupEndDate) {
                    element.tm_dur_dns = [m.domainLookupEndDate timeIntervalSinceDate:m.domainLookupStartDate] * 1000;
                }
                if (m.secureConnectionStartDate && m.secureConnectionEndDate) {
                    element.tm_dur_ssl = [m.secureConnectionEndDate timeIntervalSinceDate:m.secureConnectionStartDate] * 1000;
                }
                if (m.connectStartDate && m.connectEndDate) {
                    element.tm_dur_conn = [m.connectEndDate timeIntervalSinceDate:m.connectStartDate] * 1000;
                }
                if (m.responseStartDate) {
                    element.tm_dur_firstP = [m.responseStartDate timeIntervalSince1970] * 1000 - element.tm_pnt_send;
                }
                if ([m respondsToSelector:@selector(remoteAddress)]) {
                    element.ip = m.remoteAddress;
                }
            }
        }
    }
}

@end
