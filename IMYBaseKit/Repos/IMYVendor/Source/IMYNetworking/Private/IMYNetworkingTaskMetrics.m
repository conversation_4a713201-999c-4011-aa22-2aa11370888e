//
//  IMYNetworkingTaskMetrics.m
//  IMYNetworking
//

#import "IMYNetworkingTaskMetrics.h"
#import "IMYFoundation.h"
#import "_PSDConfig.h"
#import "NSObject+IMYPublicDataConvert.h"

@implementation IMYNetworkingTaskMetrics

@synthesize url = _url;
@synthesize tm_pnt_send = _tm_pnt_send;
@synthesize tm_dur_dns = _tm_dur_dns;
@synthesize tm_dur_conn = _tm_dur_conn;
@synthesize tm_dur_ssl = _tm_dur_ssl;
@synthesize tm_dur_firstP = _tm_dur_firstP;
@synthesize tm_dur_end = _tm_dur_end;
@synthesize status_code = _status_code;
@synthesize err_code = _err_code;
@synthesize net_type = _net_type;
@synthesize content_type = _content_type;
@synthesize recv_bytes = _recv_bytes;
@synthesize sent_bytes = _sent_bytes;
@synthesize content_length = _content_length;
@synthesize ip = _ip;
@synthesize method = _method;
@synthesize carrier = _carrier;


- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics API_AVAILABLE(ios(10.0)) {
    self = [super init];
    if (self) {
        if (metrics.transactionMetrics.count > 0) {
            NSURLSessionTaskTransactionMetrics *transactionMetrics = metrics.transactionMetrics.lastObject;
            
            if (transactionMetrics.fetchStartDate) {
                _tm_pnt_send = (int64_t)([transactionMetrics.fetchStartDate timeIntervalSince1970] * 1000);
            }
            
            if (transactionMetrics.domainLookupStartDate && transactionMetrics.domainLookupEndDate) {
                _tm_dur_dns = (NSInteger)([transactionMetrics.domainLookupEndDate timeIntervalSinceDate:transactionMetrics.domainLookupStartDate] * 1000);
            }
            
            if (transactionMetrics.connectStartDate && transactionMetrics.connectEndDate) {
                _tm_dur_conn = (NSInteger)([transactionMetrics.connectEndDate timeIntervalSinceDate:transactionMetrics.connectStartDate] * 1000);
            }
            
            if (transactionMetrics.secureConnectionStartDate && transactionMetrics.secureConnectionEndDate) {
                _tm_dur_ssl = (NSInteger)([transactionMetrics.secureConnectionEndDate timeIntervalSinceDate:transactionMetrics.secureConnectionStartDate] * 1000);
            }
            
            if (transactionMetrics.responseStartDate && transactionMetrics.requestEndDate) {
                _tm_dur_firstP = (NSInteger)([transactionMetrics.responseStartDate timeIntervalSinceDate:transactionMetrics.requestEndDate] * 1000);
            }
            
            if (transactionMetrics.responseEndDate && transactionMetrics.fetchStartDate) {
                _tm_dur_end = (NSInteger)([transactionMetrics.responseEndDate timeIntervalSinceDate:transactionMetrics.fetchStartDate] * 1000);
            }

            // 收集HTTP响应信息
            if ([transactionMetrics.response isKindOfClass:[NSHTTPURLResponse class]]) {
                NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)transactionMetrics.response;
                _status_code = httpResponse.statusCode;
                _content_type = httpResponse.allHeaderFields[@"Content-Type"];
                _content_length = [httpResponse.allHeaderFields[@"Content-Length"] integerValue];
            }

            // 收集字节数信息
            _recv_bytes = transactionMetrics.countOfResponseBodyBytesReceived;
            _sent_bytes = transactionMetrics.countOfRequestBodyBytesSent;

            // 收集IP地址
            _ip = transactionMetrics.remoteAddress;

            // 收集HTTP方法
            NSString *httpMethod = transactionMetrics.request.HTTPMethod;
            if ([httpMethod isEqualToString:@"GET"]) _method = 1;
            else if ([httpMethod isEqualToString:@"POST"]) _method = 2;
            else if ([httpMethod isEqualToString:@"PUT"]) _method = 3;
            else if ([httpMethod isEqualToString:@"DELETE"]) _method = 4;
            else if ([httpMethod isEqualToString:@"HEAD"]) _method = 5;
            else if ([httpMethod isEqualToString:@"PATCH"]) _method = 6;
            else _method = 0;
        }

        // 收集网络类型和运营商信息
        _net_type = [_PSDConfig defaultConfig].net_type;
        _carrier = [UIDevice imy_carrierName];
        _err_code = 0; // 默认无错误
    }
    return self;
}

@end


