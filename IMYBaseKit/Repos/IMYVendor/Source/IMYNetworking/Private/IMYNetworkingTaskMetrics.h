//
//  IMYNetworkingTaskMetrics.h
//  IMYNetworking
//

#import <Foundation/Foundation.h>
#import "IMYHTTPInternal.h"

NS_ASSUME_NONNULL_BEGIN

API_AVAILABLE(ios(10.0))
@interface IMYNetworkingTaskMetrics : NSObject <IMYNetworkingTaskMetricsProtocol>

/**
*  根据指标初始化类
*
*  @param metrics 指标。
*  @return 返回实例不是全部的指标都有数据，目前仅收集耗时相关的数据指标。
*/
- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics;

@end

NS_ASSUME_NONNULL_END
