//
//  IMYHTTPBuildable+IMYNetworkingPrivate.m
//  IMYVendor
//
//  Created by mario on 2017/9/7.
//  Copyright © 2017年 meiyou. All rights reserved.
//

#import "IMYHTTPBuildable+IMYNetworkingPrivate.h"
#import "IMYHTTPInternal.h"
#import "IMYHTTPResponse.h"
#import "IMYHTTPSessionManager.h"
#import "NSURLSessionDataTask+IMYNetworkingPrivate.h"
#import "IMYFoundation.h"
#import <CommonCrypto/NSData+CommonCrypto.h>
#import <CocoaSecurity/Base64.h>

@interface IMYHTTPBuildable (MeetyouPrivate)
/// 迁移到 IMYPublic 实现
+ (RACSignal *)racWithHttpBuilder:(IMYHTTPBuildable *)builder
                          manager:(AFHTTPSessionManager *)aManager;
@end

typedef void (^IMYHTTPSuccessCb)(NSURLSessionDataTask *, id);
typedef void (^IMYHTTPFailureCb)(NSURLSessionDataTask *, NSError *);

static NSURLRequestCachePolicy toNSURLCachePolicy(IMYHTTPCacheType cacheType) {
    switch (cacheType) {
        case IMYHTTPCacheTypeIgnoringCache:
            return NSURLRequestReloadIgnoringCacheData;

        case IMYHTTPCacheTypeCacheElseLoad:
            return NSURLRequestReturnCacheDataElseLoad;

        case IMYHTTPCacheTypeCacheDontLoad:
            return NSURLRequestReturnCacheDataDontLoad;

        case IMYHTTPCacheTypeCacheAndLoad:
        case IMYHTTPCacheTypeDefault:
        default:
            return NSURLRequestUseProtocolCachePolicy;
    }
}

static float toNSTaskPriority(IMYHTTPTaskPriority priority) {
    switch (priority) {
        case IMYHTTPTaskPriorityLow:
            return NSURLSessionTaskPriorityLow;

        case IMYHTTPTaskPriorityHigh:
            return NSURLSessionTaskPriorityHigh;

        case IMYHTTPTaskPriorityDefault:
        default:
            return NSURLSessionTaskPriorityDefault;
    }
}

@implementation IMYHTTPBuildable (RACSignalPrivate)

- (RACSignal *)buildRACSingal {
    return [[IMYHTTPBuildable racWithHttpBuilder:self manager:nil] replayLazily];
}

/// 真实组装请求（不携带业务逻辑）
+ (RACSignal *)real_racWithHttpBuilder:(IMYHTTPBuildable *)builder
                               manager:(AFHTTPSessionManager *)aManager {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        NSURL *baseURL = [NSURL URLWithString:builder.host ?: @""];
        
        NSString *urlString = nil;
        if (builder.userRequest) {
            urlString = builder.userRequest.URL.absoluteString;
        } else {
            urlString = [self urlStringWithBuilder:builder queriesHook:builder.queriesHook];
        }
        
        ///create session manager
        IMYHTTPSessionManager *manager = [self createSessionManagerWithBuilder:builder baseURL:baseURL urlString:urlString manager:aManager];

        ///config
        [self setupSessionManager:manager builder:builder baseURL:baseURL urlString:urlString];
        
        __block BOOL isCompleted = NO;
        __block dispatch_block_t timeoutBlock = nil;
        
        IMYHTTPSuccessCb success = ^(NSURLSessionDataTask *task, id responseObject) {
            if (isCompleted) {
                return;
            }
            isCompleted = YES;
            timeoutBlock = nil;
            if (builder.cacheType == IMYHTTPCacheTypeCacheAndLoad) {
                [task storeCacheWithResponseObject:responseObject];
            }
            NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:builder.userInfo];
            if ([task respondsToSelector:@selector(_psd_http_element)] && [(id)task _psd_http_element]) {
                userInfo[IMYNetworkingTaskMetricsKey] = [(id)task _psd_http_element];
            }
            IMYHTTPResponse *response = [IMYHTTPResponse new]
                                            .RESPONSE(task.response)
                                            .OBJECT(responseObject)
                                            .USERINFO(userInfo);
            if (builder.didSuccessHook) {
                builder.didSuccessHook(response);
            }
            [subscriber sendNext:response];
            [subscriber sendCompleted];
        };
        
        IMYHTTPFailureCb failure = ^(NSURLSessionDataTask *task, NSError *error) {
            if (isCompleted) {
                return;
            }
            isCompleted = YES;
            timeoutBlock = nil;
            NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:error.userInfo];
            NSURLRequest *originalRequest = task.originalRequest;
            [dict imy_setNonNilObject:originalRequest.URL forKey:IMYNetworkingOriginalURLKey];
            [dict imy_setNonNilObject:[originalRequest valueForHTTPHeaderField:@"Authorization"] forKey:IMYNetworkingUserTokenKey];
            [dict imy_setNonNilObject:[originalRequest valueForHTTPHeaderField:@"Authorization-Virtual"] forKey:IMYNetworkingVirtualTokenKey];
            [dict imy_setNonNilObject:builder.userInfo forKey:IMYNetworkingUserInfoKey];
            if ([task respondsToSelector:@selector(_psd_http_element)] && [(id)task _psd_http_element]) {
                [dict imy_setNonNilObject:[(id)task _psd_http_element] forKey:IMYNetworkingTaskMetricsKey];
                // 同时使用新的key存储相同的数据，保持兼容性
                [dict imy_setNonNilObject:[(id)task _psd_http_element] forKey:IMYNetworkingPerformanceMetricsKey];
            }

            NSError *newError = nil;
            if ([error.domain isEqualToString:AFURLResponseSerializationErrorDomain] && [task.response isKindOfClass:NSHTTPURLResponse.class]) {
                // 修复AF会在底层把对应 error code 修改的问题。
                NSInteger httpCode = [((NSHTTPURLResponse *)task.response) statusCode];
                newError = [NSError errorWithDomain:error.localizedDescription
                                               code:httpCode
                                           userInfo:dict];
            } else {
                newError = [NSError errorWithDomain:error.domain
                                               code:error.code
                                           userInfo:dict];
            }
            
            if (builder.didFailHook) {
                builder.didFailHook(newError);
            }
            [subscriber sendError:newError];
        };
        
        NSURLSessionDataTask *task = nil;
        if (builder.userRequest) {
            task = [self req_dataTaskWithBuilder:builder
                                         manager:manager
                                         success:success
                                         failure:failure];
        } else {
            task = [self dataTaskWithBuilder:builder
                                   urlString:urlString
                                     manager:manager
                                     success:success
                                     failure:failure];
        }

        if (@available(iOS 9.0, *)) {
            task.priority = toNSTaskPriority(builder.priority);
        }

        // get cache while task is ready.
        if (IMYHTTPCacheTypeCacheAndLoad == builder.cacheType) {
            IMYHTTPResponse *response = [task getCachedResponseWithSerializer:manager.responseSerializer];
            //NOTE: send immediately, ignoring completion queue...
            if (response) {
                [subscriber sendNext:response];
            }
        }

        if (builder.timeout > 0) {
            timeoutBlock = ^{
                // timeout cancel request
                if (task.state == NSURLSessionTaskStateRunning ||
                    task.state == NSURLSessionTaskStateSuspended) {
                    [manager cancel];
                }
                [task cancel];
                // callback error
                NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:8];
                NSURLRequest *originalRequest = task.originalRequest;
                [dict imy_setNonNilObject:originalRequest.URL forKey:IMYNetworkingOriginalURLKey];
                [dict imy_setNonNilObject:[originalRequest valueForHTTPHeaderField:@"Authorization"] forKey:IMYNetworkingUserTokenKey];
                [dict imy_setNonNilObject:[originalRequest valueForHTTPHeaderField:@"Authorization-Virtual"] forKey:IMYNetworkingVirtualTokenKey];
                [dict imy_setNonNilObject:builder.userInfo forKey:IMYNetworkingUserInfoKey];
                if ([task respondsToSelector:@selector(_psd_http_element)] && [(id)task _psd_http_element]) {
                    [dict imy_setNonNilObject:[(id)task _psd_http_element] forKey:IMYNetworkingTaskMetricsKey];
                    // 同时使用新的key存储相同的数据，保持兼容性
                    [dict imy_setNonNilObject:[(id)task _psd_http_element] forKey:IMYNetworkingPerformanceMetricsKey];
                }
                NSError *newError = [NSError errorWithDomain:@"request time out!"
                                                        code:NSURLErrorTimedOut
                                                    userInfo:dict];
                if (builder.didFailHook) {
                    builder.didFailHook(newError);
                }
                [subscriber sendError:newError];
            };
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(builder.timeout * NSEC_PER_SEC)), dispatch_get_global_queue(0, 0), ^{
                if (isCompleted) {
                    return;
                }
                isCompleted = YES;
                if (timeoutBlock) {
                    timeoutBlock();
                }
            });
        }
        
        @weakify(task, manager);
        return [RACDisposable disposableWithBlock:^{
            @strongify(task, manager);
            if (isCompleted) {
                return;
            }
            //NOTE: only remove delegate when task is running
            if (task.state == NSURLSessionTaskStateRunning ||
                task.state == NSURLSessionTaskStateSuspended) {
                [manager cancel];
            }
            [task cancel];
        }];
    }];
}

#pragma mark - Private

+ (NSURLSessionDataTask *)dataTaskWithBuilder:(IMYHTTPBuildable *)builder
                                    urlString:(NSString *)urlString
                                      manager:(IMYHTTPSessionManager *)manager
                                      success:(IMYHTTPSuccessCb)success
                                      failure:(IMYHTTPFailureCb)failure {

    void (^progressBlock)(NSProgress *) = ^(NSProgress *progress) {
        if (builder.progressBlock) {
            builder.progressBlock(progress.completedUnitCount, progress.totalUnitCount);
        }
    };

    NSURLSessionDataTask *task = nil;
    id parameters = builder.parameters;

    switch (builder.method) {
        case HTTPMethodHead: {
            task = [manager HEAD:urlString
                parameters:parameters
                success:^(NSURLSessionDataTask *_Nonnull task) {
                    success(task, nil);
                }
                failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                    failure(task, error);
                }];
        } break;

        case HTTPMethodDelete: {
            task = [manager DELETE:urlString
                parameters:parameters
                success:^(NSURLSessionDataTask *_Nonnull task, id _Nonnull responseObject) {
                    success(task, responseObject);
                }
                failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                    failure(task, error);
                }];
        } break;

        case HTTPMethodPost: {
            if (builder.formDataBlock) {
                task = [manager POST:urlString
                    parameters:parameters
                    constructingBodyWithBlock:builder.formDataBlock
                    progress:^(NSProgress *_Nonnull uploadProgress) {
                        // Upload Task Progress
                        progressBlock(uploadProgress);
                    }
                    success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
                        success(task, responseObject);
                    }
                    failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                        failure(task, error);
                    }];
            } else {
                task = [manager POST:urlString
                    parameters:parameters
                    progress:^(NSProgress *_Nonnull uploadProgress) {
                        // Upload Task Progress
                        progressBlock(uploadProgress);
                    }
                    success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
                        success(task, responseObject);
                    }
                    failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                        failure(task, error);
                    }];
            }
        } break;

        case HTTPMethodPut: {
            task = [manager PUT:urlString
                parameters:parameters
                success:^(NSURLSessionDataTask *_Nonnull task, id _Nonnull responseObject) {
                    success(task, responseObject);
                }
                failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                    failure(task, error);
                }];
        } break;

        case HTTPMethodPatch: {
            task = [manager PATCH:urlString
                parameters:parameters
                success:^(NSURLSessionDataTask *_Nonnull task, id _Nonnull responseObject) {
                    success(task, responseObject);
                }
                failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                    failure(task, error);
                }];
        } break;

        case HTTPMethodGet:
        default: {
            task = [manager GET:urlString
                parameters:parameters
                progress:^(NSProgress *_Nonnull downloadProgress) {
                    // Download Task Progress
                    progressBlock(downloadProgress);
                }
                success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
                    success(task, responseObject);
                }
                failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
                    failure(task, error);
                }];
        } break;
    }
    return task;
}

+ (NSURLSessionDataTask *)req_dataTaskWithBuilder:(IMYHTTPBuildable *)builder
                                          manager:(IMYHTTPSessionManager *)manager
                                          success:(IMYHTTPSuccessCb)success
                                          failure:(IMYHTTPFailureCb)failure {
    NSParameterAssert(builder.userRequest != nil);

    void (^progressBlock)(NSProgress *) = ^(NSProgress *progress) {
        if (builder.progressBlock) {
            builder.progressBlock(progress.completedUnitCount, progress.totalUnitCount);
        }
    };

    NSMutableURLRequest *mutableRequest = [builder.userRequest imy_mutableConverted];
    if (!mutableRequest.HTTPMethod.length) {
        mutableRequest.HTTPMethod = @"GET";
    }

    void (^uploadProgress)(NSProgress *) = nil;
    void (^downloadProgress)(NSProgress *) = nil;
    if ([mutableRequest.HTTPMethod.uppercaseString isEqualToString:@"POST"]) {
        uploadProgress = progressBlock;
    } else {
        downloadProgress = progressBlock;
    }

    return [manager dataTaskWithRequest:mutableRequest
        uploadProgress:uploadProgress
        downloadProgress:downloadProgress
        success:^(NSURLSessionDataTask *_Nonnull task, id _Nullable responseObject) {
            success(task, responseObject);
        }
        failure:^(NSURLSessionDataTask *_Nullable task, NSError *_Nonnull error) {
            failure(task, error);
        }];
}

+ (IMYHTTPSessionManager *)createSessionManagerWithBuilder:(IMYHTTPBuildable *)builder
                                                   baseURL:(NSURL *)baseURL
                                                 urlString:(NSString *)urlString
                                                   manager:(AFHTTPSessionManager *)aManager {
    NSURLRequestCachePolicy requestCachePolicy = toNSURLCachePolicy(builder.cacheType);
    
    IMYHTTPSessionManager *sessionManager = [[IMYHTTPSessionManager alloc] initWithBaseURL:baseURL];
    sessionManager.requestCachePolicy = requestCachePolicy;
    sessionManager.gzipEncoding = builder.gzipEncoding;
    if (builder.gzipEncodingHook) {
        NSString *tmpUrlString = [NSURL URLWithString:urlString relativeToURL:baseURL].absoluteString;
        sessionManager.gzipEncoding |= builder.gzipEncodingHook(tmpUrlString);
    }
    // 设置独立AFSessionManager
    [sessionManager setupInlineSessionManager:aManager];
    
    return sessionManager;
}

+ (void)setSessionRequestSerializer:(IMYHTTPSessionManager *)manager
                            builder:(IMYHTTPBuildable *)builder
                            baseURL:(NSURL *)baseURL
                          urlString:(NSString *)urlString {
    AFHTTPRequestSerializer<IMYRequestSerialization> *requestSerializer = [IMYHTTPSerializerHelper requestSerializerOfType:builder.requestSerializerType];
    requestSerializer.encrytion = builder.encryptBlock;

    NSMutableDictionary *URLHeaders = [NSMutableDictionary dictionary];
    // Headers
    if (builder.headersHook) {
        NSString *tmpUrlString = [NSURL URLWithString:urlString relativeToURL:baseURL].absoluteString;
        NSDictionary *additionalHeaders = builder.headersHook(tmpUrlString, builder.headers.copy);
        [URLHeaders addEntriesFromDictionary:additionalHeaders];
    }
    [URLHeaders addEntriesFromDictionary:builder.headers];
    [URLHeaders enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
        [requestSerializer setValue:[obj description] forHTTPHeaderField:[key description]];
    }];

    // set request serializer
    manager.requestSerializer = requestSerializer;
}

+ (void)setSessionResponseSerializer:(IMYHTTPSessionManager *)manager builder:(IMYHTTPBuildable *)builder {
    AFHTTPResponseSerializer *responseSerializer = [IMYHTTPSerializerHelper responseSerializerOfType:builder.responseSerializerType removeNSNull:builder.removeAllNSNull];
    if (builder.acceptableContentTypesHook) {
        NSSet *addtional = builder.acceptableContentTypesHook(builder.responseSerializerType);
        if (addtional.count) {
            NSMutableSet *set = [NSMutableSet setWithSet:responseSerializer.acceptableContentTypes];
            [set unionSet:addtional];
            responseSerializer.acceptableContentTypes = [NSSet setWithSet:set];
        }
    }
    // set response serializer
    manager.responseSerializer = responseSerializer;
}

+ (void)setupSessionManager:(IMYHTTPSessionManager *)manager
                    builder:(IMYHTTPBuildable *)builder
                    baseURL:(NSURL *)baseURL
                  urlString:(NSString *)urlString {
    // requestSerializer
    if (!builder.userRequest) {
        [self setSessionRequestSerializer:manager builder:builder baseURL:baseURL urlString:urlString];
    }

    // responseSerializer
    [self setSessionResponseSerializer:manager builder:builder];

    // completion queue
    if (builder.completionQueue) {
        manager.completionQueue = builder.completionQueue;
    } else {
        manager.completionQueue = [NSObject imy_defaultQualityQueue];
    }

    // security policy
    if (builder.securityPolicy) {
        manager.securityPolicy = (AFSecurityPolicy *)builder.securityPolicy;
    }

    // will request hook
    manager.willRequestHook = builder.willRequestHook;
    
    // receive data
    manager.receiveDataBlock = builder.receiveDataBlock;
    
    // run finish setup hook
    [manager finishSetupHook];
}

+ (NSString *)urlStringWithBuilder:(IMYHTTPBuildable *)builder queriesHook:(QueriesHook)queriesHook {
    NSString *urlString = builder.path ?: @"";

    NSMutableDictionary *URLQueries = [NSMutableDictionary dictionary];
    if (queriesHook) {
        NSString *tmpUrlString = builder.path ?: @"";
        if (builder.host.length > 0) {
            tmpUrlString = [NSURL URLWithString:tmpUrlString relativeToURL:[NSURL URLWithString:builder.host]].absoluteString;
        }
        NSDictionary *addtionalQueries = queriesHook(tmpUrlString);
        if (addtionalQueries) {
            [URLQueries addEntriesFromDictionary:addtionalQueries];
        }
    }
    if (builder.urlQueries) {
        [URLQueries addEntriesFromDictionary:builder.urlQueries];
    }

    ///如果参数中有跟url query相同的key , 则删除url query中的key  保证不重复
    if (builder.method == HTTPMethodGet && [builder.parameters isKindOfClass:[NSDictionary class]]) {
        [builder.parameters enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
            [URLQueries removeObjectForKey:key];
        }];
    }
    if (builder.signKey) {
        URLQueries = [self requestParamWithSign:URLQueries signKey:builder.signKey parameters:builder.parameters];
    }

    urlString = [self urlString:urlString appendingURLQueries:URLQueries];
    // 防止URL生成失败，提前做次生成
    NSURL *URL = [NSURL imy_URLWithString:urlString];
    urlString = URL.absoluteString;

    return urlString ?: @"";
}

+ (NSString *)urlString:(NSString *)urlString appendingURLQueries:(NSDictionary *)params {
    NSString *resultURLString = urlString;
    NSString *urlParamsString = AFQueryStringFromParameters(params);
    if (urlParamsString.length) {
        if ([urlString containsString:@"?"]) {
            if ([urlString hasSuffix:@"&"] == NO) {
                resultURLString = [urlString stringByAppendingFormat:@"&%@", urlParamsString];
            } else {
                resultURLString = [urlString stringByAppendingString:urlParamsString];
            }
        } else {
            resultURLString = [urlString stringByAppendingFormat:@"?%@", urlParamsString];
        }
    }
    return resultURLString;
}

/**
反爬虫签名
 tapd:https://www.tapd.cn/37050439/prong/stories/view/1137050439001059705
    nonce: 随机字符串
    timestamp:时间戳
    sign: 针对nonce,timestamp进行hmac_sha1加密后base64编码
*/
+ (NSDictionary *)requestParamWithSign:(NSDictionary *)params signKey:(NSString *)key parameters:(NSDictionary *)parameters{
    // 8.68.0 版本废弃
    // 【【安全】接口安全性签名验证公共组件】
    // https://www.tapd.meiyou.com/55703113/prong/stories/view/1155703113001207127
    return params;
}

@end
