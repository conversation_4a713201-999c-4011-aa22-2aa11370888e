//
//  IMYPhotoModel.h
//  IMY_ViewKit
//
//  Created by ponyo on 2019/1/22.
//

#import <Foundation/Foundation.h>
#import <Photos/Photos.h>
#import "IMYiCloudAssetModel.h"

typedef NS_ENUM(NSUInteger, IMYAssetType) {
    IMYAssetTypeImage = 0,
    IMYAssetTypeVideo = 1,
    IMYAssetTypeAudio = 2,
    IMYAssetTypeNone = 1001,
};

typedef NS_ENUM(NSUInteger, IMYAssetSubType) {
    IMYAssetSubTypeImage = 0,
    IMYAssetSubTypeGIF   = 1,
};

NS_ASSUME_NONNULL_BEGIN

@interface IMYAssetModel : NSObject

@property (nonatomic, strong, readonly) NSDate *creationDate;
@property (nonatomic, copy, readonly) NSString  *identifier;
@property (nonatomic, assign, readonly) IMYAssetType assetType;
@property (nonatomic, assign, readonly) IMYAssetSubType assetSubType;

@property (nonatomic, strong) IMYiCloudAssetModel *iCloudAssetModel;

/// 通过 iCloudAssetManager 自动赋值
@property (nonatomic, strong, readonly) AVAsset *avAsset;
/// 视频时长
@property (nonatomic, copy, readonly) NSString  *duration;

- (instancetype)initWithPHAsset:(PHAsset *)asset API_AVAILABLE(ios(8.0));
- (instancetype)initWithIdentifier:(NSString *)identifier;

/**
 同步返回缩略图

 @param size 指定缩略图大小
 @return 同步返回缩略图
 */
- (UIImage *)thumbnailImageWithSize:(CGSize)size;

/**
 同步请求原始图片
 
 @return 同步返回原始图片
 */
- (UIImage *)originImage;


/**
 异步请求缩略图

 @param size 指定缩略图大小
 @param completion 请求结果回调，在main线程调用
 @return 请求的id，可用于取消请求
 */
- (PHImageRequestID)requestThumbnailImageWithSize:(CGSize)size completion:(void (^)(UIImage *result, NSDictionary<NSString *, id> *info)) completion API_AVAILABLE(ios(8.0));

- (PHImageRequestID)requestFullScreenImageWithCompletion:(void (^)(UIImage *result, NSDictionary<NSString *, id> *info)) completion
                                      andProgressHandler:(nullable PHAssetImageProgressHandler)progressHandler API_AVAILABLE(ios(8.0));

/**
 异步请求原始图片
 
 @param completion 请求结果回调，在main线程调用
 @param progressHandler 请求进度回到
 @return 请求的id，可用于取消请求
 */
- (PHImageRequestID)requestOriginalImageWithCompletion:(void (^)(UIImage *result, NSDictionary<NSString *, id> *info))completion
                                   withProgressHandler:(nullable PHAssetImageProgressHandler)progressHandler API_AVAILABLE(ios(8.0));


/**
请求图片data，可用于GIF等

 @param completion 清酒结果回调
 */
- (void)requestImageData:(void (^)(NSData *imageData, NSDictionary<NSString *, id> *info, BOOL isGIF, BOOL isHEIC))completion;

/// 请求视频资源，如果通过 IMYAssetPickerController 则无需再自己调用
- (void)requestVideoAVAsset:(void (^)(AVAsset *avAsset, NSDictionary *info))completion;

/// 获取PHAsset，初始化后就有值
- (PHAsset *)fetchPhAsset;

@end

NS_ASSUME_NONNULL_END
