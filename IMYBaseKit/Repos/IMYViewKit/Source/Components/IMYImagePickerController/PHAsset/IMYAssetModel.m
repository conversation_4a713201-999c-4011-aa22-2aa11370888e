//
//  IMYPhotoModel.m
//  IMY_ViewKit
//
//  Created by ponyo on 2019/1/22.
//

#import "IMYAssetModel.h"
#import "IMYAssetsManager.h"
#import "IMYFoundation.h"
#import <MobileCoreServices/UTCoreTypes.h>

NSString * const IMYAssetInfoImageData = @"imageData";
NSString * const IMYAssetInfoOrientation = @"orientation";
NSString * const IMYAssetInfoDataLength = @"dataLength";
NSString * const IMYAssetInfoOriginInfo = @"originInfo";
NSString * const IMYAssetInfoDataUTI = @"dataUTI";


@interface IMYAssetModel ()
@property (nonatomic, copy) NSString  *identifier;
@property (nonatomic, strong) PHAsset *phAsset;
@property (nonatomic, copy) NSDictionary  *assetInfo;
@property (nonatomic, copy) NSString  *duration;
@property (nonatomic, strong) AVAsset *avAsset;
@end

@implementation IMYAssetModel

- (instancetype)initWithPHAsset:(PHAsset *)asset {
    if (self = [super init]) {
        _phAsset = asset;
        _identifier = _phAsset.localIdentifier;
        switch (asset.mediaType) {
            case PHAssetMediaTypeImage: {
                 _assetType = IMYAssetTypeImage;
                if ([[asset valueForKey:@"uniformTypeIdentifier"] isEqualToString:(__bridge NSString *)kUTTypeGIF]) {
                    _assetSubType = IMYAssetSubTypeGIF;
                } else {
                    _assetSubType = IMYAssetSubTypeImage;
                }
            }
                break;
            case PHAssetMediaTypeVideo: {
                _assetType = IMYAssetTypeVideo;
                _duration = [self getNewTimeFromDurationSecond:asset.duration];
                break;
            }
            case PHAssetMediaTypeAudio: {
                _assetType = IMYAssetTypeAudio;
                break;
            }
            default:
                break;
        }
        _iCloudAssetModel = [IMYAssetModel iCloudAssetModelForIdentifier:_identifier];
    }
    return self;
}

+ (IMYiCloudAssetModel *)iCloudAssetModelForIdentifier:(NSString *)identifier {
    if (!identifier.length) {
        return nil;
    }
    static NSMutableDictionary *maps = nil;
    static dispatch_once_t onceToken;
    static pthread_mutex_t locker;
    dispatch_once(&onceToken, ^{
        maps = [NSMutableDictionary dictionary];
        pthread_mutex_init(&locker, NULL);
    });
    pthread_mutex_lock(&locker);
    IMYiCloudAssetModel *iCloudAssetModel = [maps objectForKey:identifier];
    if (!iCloudAssetModel) {
        iCloudAssetModel = [[IMYiCloudAssetModel alloc] init];
        iCloudAssetModel.identifier = identifier;
        iCloudAssetModel.status = IMYiCloudAssetModel_Status_unknown;
        iCloudAssetModel.progress = 0;
        [maps setObject:iCloudAssetModel forKey:identifier];
    }
    pthread_mutex_unlock(&locker);
    return iCloudAssetModel;
}

- (instancetype)initWithIdentifier:(NSString *)identifier {
    PHAsset *asset = [PHAsset fetchAssetsWithLocalIdentifiers:@[identifier] options:nil].firstObject;
    IMYAssetModel *model = [self initWithPHAsset:asset];
    if (!model.identifier) {
        model.identifier = identifier;
    }
    return model;
}

- (NSDate *)creationDate{
    return _phAsset.creationDate;
}

- (NSString *)identifier {
    return _phAsset.localIdentifier;
}

- (BOOL)isEqual:(id)object {
    if (!object ||
        ![object isKindOfClass:[self class]]) {
        return NO;
    }
    if (self == object) {
        return YES;
    }
    return [self.identifier isEqualToString:((IMYAssetModel *)object).identifier];
}

- (NSUInteger)hash {
    return [self.identifier hash];
}

- (UIImage *)thumbnailImageWithSize:(CGSize)size {
    __block UIImage *image = nil;
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.resizeMode = PHImageRequestOptionsResizeModeFast;
    requestOptions.networkAccessAllowed = YES;
    requestOptions.synchronous = YES;
    // 如果图片在本地，image会同步拿到
    [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageForAsset:_phAsset targetSize:CGSizeMake(size.width * SCREEN_SCALE, size.height * SCREEN_SCALE) contentMode:PHImageContentModeAspectFill options:requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        image = result;
    }];
    return image;
}

- (UIImage *)originImage {
    __block UIImage *image = nil;
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    requestOptions.networkAccessAllowed = YES;
    requestOptions.synchronous = YES;
    [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageForAsset:_phAsset targetSize:PHImageManagerMaximumSize contentMode:PHImageContentModeDefault options:requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        image = result;
    }];
    return image;
}

- (PHImageRequestID)requestThumbnailImageWithSize:(CGSize)size completion:(void (^)(UIImage *result, NSDictionary<NSString *, id> *info)) completion {
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.resizeMode = PHImageRequestOptionsResizeModeFast;
    requestOptions.networkAccessAllowed = YES;
    return [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageForAsset:_phAsset targetSize:CGSizeMake(size.width * SCREEN_SCALE, size.height * SCREEN_SCALE) contentMode:PHImageContentModeAspectFill options:requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        if (completion) {
            completion(result, info);
        }
    }];
}

- (PHImageRequestID)requestFullScreenImageWithCompletion:(void (^)(UIImage *result, NSDictionary<NSString *, id> *info)) completion
                                      andProgressHandler:(nullable PHAssetImageProgressHandler)progressHandler {
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    requestOptions.resizeMode = PHImageRequestOptionsResizeModeNone;
    requestOptions.networkAccessAllowed = YES;
    requestOptions.progressHandler = progressHandler;
    CGSize targetSize = CGSizeMake(SCREEN_WIDTH * SCREEN_SCALE, SCREEN_HEIGHT * SCREEN_SCALE);
    if (_phAsset.pixelWidth > 0 && _phAsset.pixelHeight > 0) {
        CGFloat aspectRatio = _phAsset.pixelWidth / (CGFloat)_phAsset.pixelHeight;
        CGFloat pixelHeight = targetSize.width / aspectRatio;
        targetSize.height = pixelHeight;
    }
    return [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageForAsset:_phAsset targetSize:targetSize contentMode:PHImageContentModeDefault options:requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        if (completion) {
            completion(result, info);
        }
    }];
}

- (PHImageRequestID)requestOriginalImageWithCompletion:(void (^)(UIImage *result, NSDictionary *info))completion
                                   withProgressHandler:(nullable PHAssetImageProgressHandler)progressHandler {
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    requestOptions.resizeMode = PHImageRequestOptionsResizeModeNone;
    requestOptions.networkAccessAllowed = YES;// 默认NO,如果图片在icloud上，会去icloud下载
    requestOptions.progressHandler = progressHandler;
    return [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageForAsset:_phAsset targetSize:PHImageManagerMaximumSize contentMode:PHImageContentModeDefault options:requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        if (completion) {
            completion(result, info);
        }
    }];
}



- (void)requestImageData:(void (^)(NSData *imageData, NSDictionary<NSString *, id> *info, BOOL isGIF, BOOL isHEIC))completion {
    __weak typeof(self)weakSelf = self;
    if (!self.assetInfo) {// 异步，回调在主线程
        [self requestImageAssetInfo:^(NSDictionary *info) {
            __strong typeof (weakSelf)strongSelf = weakSelf;
            strongSelf.assetInfo = info;
            if (completion) {
                NSString *dataUTI = info[IMYAssetInfoDataUTI];
                BOOL isGIF = self.assetSubType == IMYAssetSubTypeGIF;
                BOOL isHEIC = [dataUTI isEqualToString:@"public.heic"];
                NSDictionary *originInfo = info[IMYAssetInfoOriginInfo];
                completion(info[IMYAssetInfoImageData], originInfo, isGIF, isHEIC);
            }
        } synchronous:NO];
    } else {// 同步
        if (completion) {
            NSString *dataUTI = self.assetInfo[IMYAssetInfoDataUTI];
            BOOL isGIF = self.assetSubType == IMYAssetSubTypeGIF;
            BOOL isHEIC = [dataUTI isEqualToString:@"public.heic"];
            NSDictionary *originInfo = self.assetInfo[IMYAssetInfoOriginInfo];
            completion(self.assetInfo[IMYAssetInfoImageData], originInfo, isGIF, isHEIC);
        }
    }
}


- (void)requestImageAssetInfo:(void (^)(NSDictionary *))completion synchronous:(BOOL)synchronus {
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.synchronous = synchronus;
    requestOptions.networkAccessAllowed = YES;
    [[[IMYAssetsManager sharedInstance] cachingImageManager] requestImageDataForAsset:self.phAsset options:requestOptions resultHandler:^(NSData * _Nullable imageData, NSString * _Nullable dataUTI, UIImageOrientation orientation, NSDictionary * _Nullable info) {
        if (info) {
            NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
            if (imageData) {
                dic[IMYAssetInfoImageData] = imageData;
                dic[IMYAssetInfoDataLength] = @(imageData.length);
            }
            dic[IMYAssetInfoOriginInfo] = info;
            if (dataUTI) {
                dic[IMYAssetInfoDataUTI] = dataUTI;
            }
            dic[IMYAssetInfoOrientation] = @(orientation);
            if (completion) {
                completion([dic copy]);
            }
        }
    }];
}

- (void)requestVideoAVAsset:(void (^)(AVAsset * _Nonnull, NSError * _Nonnull))completion {
    if (self.avAsset) {
        // 已有AVAsset
        imy_asyncMainExecuteBlock(^{
            if (completion) {
                completion(self.avAsset, nil);
            }
        });
    } else {
        PHVideoRequestOptions *ops = [PHVideoRequestOptions new];
        ops.version = PHVideoRequestOptionsVersionOriginal;
        ops.deliveryMode = PHVideoRequestOptionsDeliveryModeHighQualityFormat;
        ops.networkAccessAllowed = YES;
        __weak IMYAssetModel *weakSelf = self;
        [[IMYAssetsManager sharedInstance].cachingImageManager requestAVAssetForVideo:self.phAsset options:ops resultHandler:^(AVAsset * _Nullable avAsset, AVAudioMix * _Nullable audioMix, NSDictionary * _Nullable info) {
            imy_asyncMainExecuteBlock(^{
                __strong IMYAssetModel *self = weakSelf;
                if (avAsset && !self.avAsset) {
                    self.avAsset = avAsset;
                }
                if (completion) {
                    completion(self.avAsset, info);
                }
            });
        }];
    }
}

- (PHAsset *)fetchPhAsset {
    return self.phAsset;
}

- (NSString *)getNewTimeFromDurationSecond:(NSInteger)duration {
    NSString *newTime;
    if (duration < 10) {
        if(duration == 0){
            duration = 1;
        }
        newTime = [NSString stringWithFormat:@"0:0%zd", duration];
    } else if (duration < 60) {
        newTime = [NSString stringWithFormat:@"0:%zd", duration];
    } else {
        NSInteger min = duration / 60;
        NSInteger sec = duration - (min * 60);
        if (sec < 10) {
            newTime = [NSString stringWithFormat:@"%zd:0%zd", min, sec];
        } else {
            newTime = [NSString stringWithFormat:@"%zd:%zd", min, sec];
        }
    }
    return newTime;
}


@end
