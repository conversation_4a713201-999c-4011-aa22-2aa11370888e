//
//  IMYSubGuideVipInfo.h
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/31.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class IMYSubGuidePricingGiftInfoListItem, IMYSubGuidePricingGiftInfoModel, IMYSubGuidePricingPromotionInfoModel;
@class IMYSubGuidePricingBtnTagModel;


/// 订阅引导-支付-价格
@interface IMYSubGuidePricingListItem : NSObject

@property (nonatomic, assign) NSInteger id;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *tag;
@property (nonatomic, copy) NSString *price;
@property (nonatomic, copy) NSString *discount_price;

/// 会根据是否有优惠劵，返回 promotion_amount 或者 discount_price
@property (nonatomic, readonly) NSString *real_discount_price;

@property (nonatomic, copy) NSString *desc; ///< 价格包的说明文案
@property (nonatomic, assign) NSInteger type; ///< 1连续订阅，2非连续订阅
/// 订阅类型：0：无订阅 1：订阅-月卡 2：订阅-季卡 3：连续包月 4：连续包季 5：连续包年 6：过期 7：订阅-年卡
@property (nonatomic, assign) NSInteger sub_type;
@property (nonatomic, assign) BOOL checked; ///< 标记自动锚定
@property (nonatomic, copy) NSString *desc2; ///< 卡片内的价格描述文案
@property (nonatomic, assign) BOOL animation; ///< 动效开关
@property (nonatomic, assign) NSInteger animation_limit; ///< x 次
@property (nonatomic, assign) NSInteger animation_limit_duration; ///< x 天
@property (nonatomic, copy) NSString *sub_btn_text;///< 开通按钮文案：立即开通（已节省29元）

/// 赠礼信息
@property (nonatomic, strong) IMYSubGuidePricingGiftInfoModel *gift_info;

/// 优惠信息
@property (nonatomic, strong) IMYSubGuidePricingPromotionInfoModel *promotion_info;
/// 倒计时(秒)
@property (nonatomic, assign, readonly) NSInteger countdownTime;

/// 按钮标签
@property (nonatomic, strong) IMYSubGuidePricingBtnTagModel *sub_btn_tag;

/// 选择的赠礼
@property (nonatomic, copy, readonly) NSString *getAllGiftIDs;

/// （893新增）价格下方文案
@property (nonatomic, copy) NSString *below_price_text;

/// 说明文案的 #文案# 高亮显示
- (NSAttributedString *)getAttributedStringByDesc;

@end


@interface IMYSubGuidePricingPromotionInfoModel : NSObject

@property (nonatomic, assign) NSInteger user_promotion_id;
@property (nonatomic, copy) NSString *promotion_amount;
@property (nonatomic, assign) NSInteger count_down_seconds;

@end

/// 订阅引导-支付-赠礼
@interface IMYSubGuidePricingGiftInfoModel : NSObject
/// 赠礼项
@property (nonatomic, copy) NSArray<IMYSubGuidePricingGiftInfoListItem *> *gift_list;
/// 选择类型，1单选，2多选
@property (nonatomic, assign) NSInteger type;
/// 赠礼模块标题
@property (nonatomic, copy) NSString *title;
@end

/// 订阅引导-按钮-标签
@interface IMYSubGuidePricingBtnTagModel : NSObject

/// 889新增按钮标签上的倒计时：不为0直接展示，为0采用客户端本地计时
@property (nonatomic, copy) NSString *text;
/// 889新增按钮标签文案
@property (nonatomic, assign) NSInteger count_down_seconds;

@end

/// 订阅引导-支付-赠礼项
@interface IMYSubGuidePricingGiftInfoListItem : NSObject
///赠礼id
@property (nonatomic, assign) NSInteger id;
///促销id
@property (nonatomic, assign) NSInteger user_promotion_id;
///赠礼名称
@property (nonatomic, copy) NSString *name;
///赠礼图标
@property (nonatomic, copy) NSString *icon;
///赠礼标签
@property (nonatomic, copy) NSString *tag;
///是否展示价格（￥0）
@property (nonatomic, assign) BOOL show_price;
///是否展示赠礼价值
@property (nonatomic, assign) BOOL show_worth;
///赠礼价值
@property (nonatomic, copy) NSString *worth;
///介绍说明
@property (nonatomic, copy) NSString *desc;
///介绍说明跳转地址
@property (nonatomic, copy) NSString *desc_uri;
///赠礼倒计时
@property (nonatomic, assign) NSInteger countdown_seconds;

/// 是否有倒计时（只要有倒计时，就一定有促销id）
@property (nonatomic, assign, readonly) BOOL hasCountdown;

@end

/// 订阅引导-支付-权益信息
@interface IMYSubGuideRightsInfoItem : NSObject

@property (nonatomic, copy) NSString *text2;
@property (nonatomic, copy) NSString *text1;
@property (nonatomic, copy) NSString *group_title;
@property (nonatomic, copy) NSString *group_key;

@end

/// 订阅引导-支付-权益列表
@interface IMYSubGuideRightsListItem : NSObject

@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *images;
@property (nonatomic, copy) NSString *detail_url;

@end

@interface IMYSubGuideImageModel : NSObject

@property (nonatomic, copy) NSString *key;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSString *jump_url;
@property (nonatomic, assign) CGFloat aspect_ratio;

@end

@interface IMYSubGuideRightsGroupListItem : NSObject

@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *image;
@property (nonatomic, assign) NSInteger style;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSArray<IMYSubGuideImageModel *> *images;
@property (nonatomic, assign) NSInteger type;

@end

@interface IMYSubGuideBackPopupModel : NSObject

@property (nonatomic, copy) NSString *stay_btn_text;
@property (nonatomic, copy) NSString *back_btn_text;
@property (nonatomic, copy) NSString *image;
@property (nonatomic, copy) NSString *id;
@property (nonatomic, assign) NSInteger show_limit;
@property (nonatomic, assign) NSInteger show_limit_duration;

@end

/// 订阅引导-支付
/// https://apidoc.seeyouyima.com/doc/65a7698a5f42b61df8a3b1d2
@interface IMYSubGuideVipInfo : NSObject

@property (nonatomic, copy) NSArray<IMYSubGuidePricingListItem *> *pricing_list;
@property (nonatomic, copy) NSArray<IMYSubGuideRightsGroupListItem *> *rights_group_list;

/// 支付半弹窗展示列表
@property (nonatomic, copy) NSArray<IMYSubGuideRightsGroupListItem *> *dialog_group_list;
/// 轮播banner列表
@property (nonatomic, copy) NSArray<NSDictionary *> *carousel_banners;

@property (nonatomic, copy) NSString *sub_btn_url;
@property (nonatomic, copy) NSString *sub_btn_title;

@property (nonatomic, copy) NSString *user_avatar;
@property (nonatomic, copy) NSString *user_nick;
@property (nonatomic, copy) NSString *page_title;
@property (nonatomic, copy) NSString *sub_info_text;
@property (nonatomic, assign) NSInteger sub_plan_id;

@property (nonatomic, strong) IMYSubGuideBackPopupModel *back_popup;

@end

NS_ASSUME_NONNULL_END
