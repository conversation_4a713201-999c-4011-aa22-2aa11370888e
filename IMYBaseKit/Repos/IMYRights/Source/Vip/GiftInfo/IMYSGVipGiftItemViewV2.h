//
//  IMYSGVipGiftItemViewV2.h
//  IMYBaseKit
//
//  Created by ljh on 2025/9/8.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 会员中心页、支付成功
/// https://pixso.cn/app/design/ZjvMcwRHaC4ukmxFzCZlBg?page-id=27%3A42477&item-id=27%3A74440
@interface IMYSGVipGiftItemViewV2 : UIView

/// 卡片宽度
@property (nonatomic, assign) NSInteger cardWidth;

/// 样式：1：灰色背景（会员中心），2：白色背景（支付成功）
@property (nonatomic, assign) NSInteger cardStyle;

/// 赠礼项
@property (nonatomic, strong) NSDictionary *rawData;

@end

NS_ASSUME_NONNULL_END
