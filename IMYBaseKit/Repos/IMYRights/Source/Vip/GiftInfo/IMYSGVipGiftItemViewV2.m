//
//  IMYSGVipGiftItemViewV2.m
//  IMYBaseKit
//
//  Created by ljh on 2025/9/8.
//

#import "IMYSGVipGiftItemViewV2.h"
#import "IMYBaseKit.h"

@interface IMYSGVipGiftItemViewV2 ()

@property (nonatomic, strong) UIImageView *iconView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *gotoView;

@end

@implementation IMYSGVipGiftItemViewV2

- (void)setRawData:(NSDictionary *)rawData {
    _rawData = rawData;
    [self setupUI];
}

- (void)setupUI {
    // 基础样式
    BOOL const isStyleOne = (self.cardStyle == 1);
    if (isStyleOne) {
        self.imy_size = CGSizeMake(self.cardWidth, 44);
        [self imy_setBackgroundColor:kCK_Black_H];
    } else {
        self.imy_size = CGSizeMake(self.cardWidth, 64);
        [self imy_setBackgroundColor:kCK_White_AN];
    }
    [self imy_drawAllCornerRadius:12];
    
    // 图标
    [self addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isStyleOne) {
            make.width.height.equalTo(@28);
            make.leading.equalTo(@8);
        } else {
            make.width.height.equalTo(@32);
            make.leading.equalTo(@12);
        }
        make.centerY.equalTo(self.mas_centerY);
    }];
    [self.iconView imy_setImageURL:self.rawData[@"icon"]];
    
    // 标题
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        if (isStyleOne) {
            make.leading.equalTo(self.iconView.mas_trailing).offset(4);
        } else {
            make.leading.equalTo(self.iconView.mas_trailing).offset(8);
        }
        make.centerY.equalTo(self.iconView.mas_centerY);
    }];
    self.titleLabel.text = self.rawData[@"name"];
    
    // 按钮
    NSString * const btn_text = self.rawData[@"btn_text"];
    CGFloat gotoWidth = 0;
    if (btn_text.length > 0) {
        [self addSubview:self.gotoView];
        self.gotoView.text = btn_text;
        [self.gotoView imy_sizeToFit];
        gotoWidth = self.gotoView.imy_width + 24;
        
        [self.gotoView mas_makeConstraints:^(MASConstraintMaker *make) {
            if (isStyleOne) {
                make.trailing.equalTo(@-8);
            } else {
                make.trailing.equalTo(@-12);
            }
            make.width.equalTo(@(gotoWidth));
            make.height.equalTo(@24);
            make.centerY.equalTo(self.iconView.mas_centerY);
        }];
    }
    
    // 不同样式下的标题最大宽度
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        if (isStyleOne) {
            make.width.lessThanOrEqualTo(@(self.cardWidth - 8 - 28 - 4 - gotoWidth - 8));
        } else {
            make.width.lessThanOrEqualTo(@(self.cardWidth - 12 - 32 - 8 - gotoWidth - 12));
        }
    }];
}

#pragma mark - Lazy Loading

- (UIImageView *)iconView {
    if (!_iconView) {
        _iconView = [[UIImageView alloc] init];
        _iconView.imy_size = CGSizeMake(32, 32);
        _iconView.layer.cornerRadius = 8;
        _iconView.layer.masksToBounds = YES;
        _iconView.layer.borderColor = IMY_COLOR_KEY(@"#E8E8E8").CGColor;
        _iconView.layer.borderWidth = (SCREEN_SCALE > 2 ? 2 / SCREEN_SCALE : 0.5);
    }
    return _iconView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        [_titleLabel imy_setTextColor:kCK_Black_A];
    }
    return _titleLabel;
}

- (UILabel *)gotoView {
    if (!_gotoView) {
        _gotoView = [[UILabel alloc] init];
        _gotoView.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _gotoView.textColor = IMY_COLOR_KEY(@"#FF4D88");
        [_gotoView imy_drawAllCornerRadius:12];
        _gotoView.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        _gotoView.layer.borderWidth =  1 / SCREEN_SCALE;
        _gotoView.textAlignment = NSTextAlignmentCenter;
    }
    return _gotoView;
}

@end
