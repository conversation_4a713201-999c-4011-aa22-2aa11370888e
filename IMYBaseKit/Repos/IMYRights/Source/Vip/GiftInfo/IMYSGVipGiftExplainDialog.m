//
//  IMYSGVipGiftExplainDialog.m
//  IMYBaseKit
//
//  Created by ljh on 2025/8/29.
//

#import "IMYSGVipGiftExplainDialog.h"
#import "IMYBaseKit.h"
#import "NSString+IMYFoundation.h"
#import <Masonry/Masonry.h>

@interface IMYSGVipGiftExplainDialog () <UIScrollViewDelegate>

@property (nonatomic, strong) UIView *cardView;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIView *separatorLine;

@property (nonatomic, strong) MASConstraint *cardViewHeightConstraint;
@property (nonatomic, strong) MASConstraint *cardViewBottomConstraint;

// 标题约束引用，用于动态调整布局
@property (nonatomic, strong) MASConstraint *titleLabelLeftConstraint;

// 保存原始HTML字符串，用于主题变化时重新处理
@property (nonatomic, copy) NSString *originalHTMLString;

// 是否启用分割线逻辑（仅在达到最大高度时启用）
@property (nonatomic, assign) BOOL shouldShowSeparatorOnScroll;

@end

@implementation IMYSGVipGiftExplainDialog

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    
    [self addSubview:self.cardView];
    
    [self.cardView addSubview:self.iconImageView];
    [self.cardView addSubview:self.titleLabel];

    [self.cardView addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentLabel];
    
    [self.cardView addSubview:self.separatorLine];
    self.separatorLine.hidden = YES;
    
    [self.cardView addSubview:self.closeButton];
    
    self.scrollView.delegate = self;
    
    [self setupConstraints];
    [self setupGestures];
}

- (void)setupConstraints {
    [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(self);
        make.height.mas_lessThanOrEqualTo([UIScreen mainScreen].bounds.size.height - 44);
        self.cardViewHeightConstraint = make.height.mas_greaterThanOrEqualTo(144).priorityHigh();
        self.cardViewBottomConstraint = make.top.mas_equalTo(self.mas_bottom);
    }];

    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(12);
        make.right.mas_equalTo(self.cardView).offset(-12);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];

    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(24);
        make.left.mas_equalTo(self.cardView).offset(20);
        make.size.mas_equalTo(CGSizeMake(44, 44));
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(24 + (44.0 - 29.0) / 2.0);
        self.titleLabelLeftConstraint = make.left.equalTo(self.iconImageView.mas_right).offset(12);
        make.height.mas_equalTo(29);
        make.right.equalTo(self.cardView).offset(-20);
    }];

    [self.separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.cardView);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(20);
        make.height.mas_equalTo(1.0 / [UIScreen mainScreen].scale);
    }];

    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.separatorLine.mas_bottom);
        make.left.right.bottom.equalTo(self.cardView);
    }];

    CGFloat bottomMargin = SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 24;
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.scrollView);
        make.left.mas_equalTo(self.scrollView).offset(20);
        make.right.mas_equalTo(self.scrollView).offset(-20);
        make.width.mas_equalTo(self.scrollView).offset(-40);
        make.bottom.mas_equalTo(self.scrollView).offset(-bottomMargin);
    }];
}

- (void)setupGestures {
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped:)];
    [self addGestureRecognizer:backgroundTap];
}

- (void)setGiftItem:(IMYSubGuidePricingGiftInfoListItem *)giftItem {
    _giftItem = giftItem;
    [self updateContent];
}

- (void)updateContent {
    if (!_giftItem) {
        return;
    }

    BOOL hasIcon = (_giftItem.icon.length > 0);
    
    if (hasIcon) {
        [self.iconImageView imy_setImageURL:_giftItem.icon];
        self.iconImageView.hidden = NO;
    } else {
        self.iconImageView.hidden = YES;
    }

    // 设置标题
    self.titleLabel.text = _giftItem.name;
    
    // 根据是否有图标调整布局
    [self adjustLayoutForIcon:hasIcon];

    // 保存原始HTML字符串
    self.originalHTMLString = _giftItem.desc;

    // 设置内容富文本并配置主题变化处理
    [self setupContentLabelWithThemeChange];
    
    [self adjustCardViewHeight];
}

// 设置内容标签富文本并配置主题变化处理
- (void)setupContentLabelWithThemeChange {
    if (!self.originalHTMLString.length) {
        return;
    }
    
    @weakify(self)
    [self.contentLabel imy_addThemeChangedBlock:^(UILabel *weakObject) {
        @strongify(self)
        [self updateContentLabelAttributedText:weakObject];
    }];
}

// 更新内容标签的富文本样式
- (void)updateContentLabelAttributedText:(UILabel *)contentLabel {
    if (!self.originalHTMLString.length) {
        return;
    }
    
    NSMutableAttributedString *attributedString = [self.originalHTMLString imy_toHTMLAttributedString];
    if (!attributedString) {
        return;
    }
    
    // 如果最后是换行符，移除换行符
    if ([attributedString.string hasSuffix:@"\n"]) {
        [attributedString replaceCharactersInRange:NSMakeRange(attributedString.string.length - 1, 1) withString:@""];
    }
    
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 6;
    paragraphStyle.alignment = NSTextAlignmentLeft;

    [attributedString enumerateAttributesInRange:NSMakeRange(0, attributedString.length)
                                         options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                                      usingBlock:^(NSDictionary<NSAttributedStringKey,id> * _Nonnull attrs, NSRange range, BOOL * _Nonnull stop) {
        NSMutableDictionary *adjustedAttrs = [NSMutableDictionary dictionaryWithDictionary:attrs];

        // 修正字体大小
        UIFont *font = adjustedAttrs[NSFontAttributeName];
        if ([font.fontName containsString:@"Bold"] || [font.fontName containsString:@"Medium"]) {
            font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        } else {
            font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        }
        
        UIColor *textColor = adjustedAttrs[NSForegroundColorAttributeName];
        if (attrs[NSLinkAttributeName] != nil) {
            // 链接颜色
            textColor = [UIColor imy_colorForKey:kCK_Colour_A];
        } else {
            if ([font.fontName containsString:@"Bold"] || [font.fontName containsString:@"Medium"]) {
                // 粗体色值
                textColor = [UIColor imy_colorForKey:kCK_Black_A];
            } else {
                // 普通文字颜色
                textColor = [UIColor imy_colorForKey:kCK_Black_M];
            }
        }
        
        // 移除下划线
        [adjustedAttrs removeObjectForKey:NSUnderlineStyleAttributeName];
        
        // 设置最终属性
        adjustedAttrs[NSFontAttributeName] = font;
        adjustedAttrs[NSForegroundColorAttributeName] = textColor;
        adjustedAttrs[NSParagraphStyleAttributeName] = paragraphStyle;

        [attributedString setAttributes:adjustedAttrs range:range];
    }];

    contentLabel.attributedText = [attributedString copy];
}

- (void)adjustLayoutForIcon:(BOOL)hasIcon {
    [self.titleLabelLeftConstraint uninstall];
    
    if (hasIcon) {
        // 有图标时的布局
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            self.titleLabelLeftConstraint = make.left.equalTo(self.iconImageView.mas_right).offset(12);
        }];
    } else {
        // 无图标时的布局
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            self.titleLabelLeftConstraint = make.left.equalTo(self.cardView).offset(20);
        }];
    }
}

- (void)adjustCardViewHeight {
    [self.scrollView setNeedsLayout];
    [self.scrollView layoutIfNeeded];
    
    // 计算内容的实际高度
    CGFloat contentHeight = [self calculateContentHeight];
    CGFloat maxHeight = SCREEN_HEIGHT - 44;
    
    CGFloat cardViewHeight = MIN(contentHeight, maxHeight);
    
    // 判断是否启用分割线逻辑：当内容高度超过最大高度时启用
    self.shouldShowSeparatorOnScroll = (contentHeight > maxHeight);
    self.scrollView.scrollEnabled = (contentHeight > maxHeight);
    [self.cardViewHeightConstraint uninstall];
    [self.cardView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.cardViewHeightConstraint = make.height.mas_equalTo(cardViewHeight);
    }];

    CGFloat scrollViewContentHeight = [self calculateScrollViewContentHeight];
    self.scrollView.contentSize = CGSizeMake(0, scrollViewContentHeight);
    
    // 重置分割线状态
    if (!self.shouldShowSeparatorOnScroll) {
        self.separatorLine.hidden = YES;
    }
    
    [self setNeedsLayout];
    [self layoutIfNeeded];
}


// 计算整个cardView需要的总高度
- (CGFloat)calculateContentHeight {
    CGFloat titleAreaHeight = 88; // 固定的标题区域高度（24px顶部边距 + 44px图标高度 + 20px间距）
    CGFloat scrollViewContentHeight = [self calculateScrollViewContentHeight];
    
    // 总高度 = 标题区域高度 + scrollView内容高度
    CGFloat totalHeight = titleAreaHeight + scrollViewContentHeight;
    
    return totalHeight;
}

// 计算scrollView内容区域的高度
- (CGFloat)calculateScrollViewContentHeight {
    CGFloat bottomMargin = SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 20; // 底部边距
    CGFloat horizontalMargin = 20; // 水平边距
    
    // 计算contentLabel的高度
    CGFloat availableWidth = SCREEN_WIDTH - 2 * horizontalMargin;
    CGFloat contentLabelHeight = 0;
    
    if (self.contentLabel.attributedText) {
        CGSize constraintSize = CGSizeMake(availableWidth, CGFLOAT_MAX);
        CGRect textRect = [self.contentLabel.attributedText boundingRectWithSize:constraintSize
                                                                         options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                                                         context:nil];
        contentLabelHeight = ceil(textRect.size.height);
    }
    
    // scrollView内容高度 = 顶部边距 + 内容高度 + 底部边距
    CGFloat scrollViewHeight = contentLabelHeight + bottomMargin;
    
    return scrollViewHeight;
}

- (void)backgroundTapped:(UITapGestureRecognizer *)gesture {
    CGPoint touchPoint = [gesture locationInView:self];
    if (CGRectContainsPoint(self.cardView.frame, touchPoint)) {
        return;
    }
    [self dismiss];
}

- (void)closeButtonTapped {
    [self dismiss];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (!self.shouldShowSeparatorOnScroll) {
        return;
    }
    
    // 获取滚动偏移量
    CGFloat offsetY = scrollView.contentOffset.y;
    
    // 当向上滚动时显示分割线，回到顶部时隐藏
    BOOL shouldShowSeparator = offsetY > 0;
    
    if (self.separatorLine.hidden == shouldShowSeparator) {
        self.separatorLine.hidden = !shouldShowSeparator;
    }
}

- (void)show {
    if (self.superview) {
        return;
    }
    
    UIView *rootView = [UIApplication sharedApplication].delegate.window;
    [rootView addSubview:self];
    
    self.frame = rootView.bounds;
    
    [self adjustCardViewHeight];

    // 初始状态：背景透明，cardView在屏幕底部外
    self.alpha = 1;
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
    [self.cardViewBottomConstraint uninstall];
    [self.cardView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.cardViewBottomConstraint = make.bottom.mas_equalTo(self);
    }];
    [UIView animateWithDuration:0.15 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
        [self layoutIfNeeded];
    } completion:nil];
}

- (void)dismiss {
    if (!self.superview) {
        return;
    }
    [self.cardViewBottomConstraint uninstall];
    [self.cardView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.cardViewBottomConstraint = make.top.mas_equalTo(self.mas_bottom);
    }];
    [UIView animateWithDuration:0.15 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.0];
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.alpha = 0;
        [self removeFromSuperview];
    }];
}

#pragma mark - Lazy Loading

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [[UIView alloc] init];
        [_cardView imy_setBackgroundColorForKey:kCK_White_ANQ];
        _cardView.layer.cornerRadius = 16;
        _cardView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _cardView.layer.masksToBounds = YES;
    }
    return _cardView;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
    }
    return _scrollView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFill;
        _iconImageView.layer.cornerRadius = 12;
        _iconImageView.layer.masksToBounds = YES;
        _iconImageView.layer.borderColor = IMY_COLOR_KEY(@"#E8E8E8").CGColor;
        _iconImageView.layer.borderWidth = (SCREEN_SCALE > 2 ? 2 / SCREEN_SCALE : 0.5);
    }
    return _iconImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:21 weight:UIFontWeightMedium];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectZero];
        [_closeButton imy_addThemeChangedBlock:^(IMYTouchEXButton *weakObject) {
            UIImage *image = [UIImage imy_imageForKey:@"dyzf_icon_close"];
            [weakObject imy_setImage:image.imy_getNightStyleBottomBarImage];
        }];
        [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        _contentLabel.textColor = IMY_COLOR_KEY(kCK_Black_M);
        _contentLabel.numberOfLines = 0;
        _contentLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _contentLabel;
}

- (UIView *)separatorLine {
    if (!_separatorLine) {
        _separatorLine = [[UIView alloc] init];
        [_separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
    }
    return _separatorLine;
}

@end
