//
//  IMYSubGuideVipGiftInfoView.m
//  IMYBaseKit
//
//  Created by ljh on 2024/7/1.
//

#import "IMYSubGuideVipGiftInfoView.h"
#import "IMYSubGuideManager.h"
#import "IMYSGVipGiftItemView.h"

@interface IMYSubGuideVipGiftInfoView ()

@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, strong) IMYSubGuidePricingGiftInfoModel *giftInfo;

@property (nonatomic, copy) NSArray<IMYSubGuidePricingGiftInfoListItem *> *selectedItems;

@property (nonatomic, strong) UIView *arrowBoxView;
@property (nonatomic, strong) UIImageView *arrowIconView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIScrollView *contentView;

@end

@implementation IMYSubGuideVipGiftInfoView

+ (instancetype)giftInfoViewWithBoxWidth:(CGFloat)width {
    return [[self alloc] initWithFrame:CGRectMake(0, 0, width, 0)];
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.alpha = 0;
        self.imy_height = 0;
        self.clipsToBounds = YES;
    }
    return self;
}

#define kArrowCornerRadius 12

- (void)setAlpha:(CGFloat)alpha {
    [super setAlpha:alpha];
    if (alpha < 0.01) {
        self.arrowBoxView.hidden = YES;
    } else {
        self.arrowBoxView.hidden = NO;
    }
}

- (void)setupSubviews {
    if (self.arrowBoxView != nil) {
        return;
    }
    self.arrowBoxView = [[UIView alloc] initWithFrame:CGRectMake(kArrowCornerRadius, 0, self.imy_width - 2 * kArrowCornerRadius, 7)];
    self.arrowBoxView.clipsToBounds = YES;
    [self addSubview:self.arrowBoxView];
    
    self.arrowIconView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 14, 7)];
    [self.arrowBoxView addSubview:self.arrowIconView];
    
    self.contentView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, self.arrowBoxView.imy_bottom - 0.5, self.imy_width, 96)];
    self.contentView.showsHorizontalScrollIndicator = NO;
    [self.contentView imy_drawAllCornerRadius:12];
    [self addSubview:self.contentView];
    
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(8, self.contentView.imy_top + 6, self.imy_width - 16, 16)];
    self.titleLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
    self.titleLabel.numberOfLines = 1;
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    [self addSubview:self.titleLabel];
    
    // #E8E8E8 40% 叠加 白底 #FFFFFF ，生成的无透明度的色值 #F5F5F5
    // 大卡片底色是 #F8F8F9
    NSString * const lightColorKey = (self.showStyle == 1 ? @"#F9F9FA" : @"#F5F5F5");
    [self imy_addThemeChangedBlock:^(IMYSubGuideVipGiftInfoView *weakObject) {
        BOOL const isNight =  (IMYPublicAppHelper.shareAppHelper.isNight && labs(weakObject.imy_disableThemeAction) != 1);
        UIColor * const bgColor = IMY_COLOR_KEY(isNight ? @"#333333" : lightColorKey);
        weakObject.arrowIconView.image = [[UIImage imy_imageForKey:@"sub_guide_gift_arrow"] imy_imageWithTintColor:bgColor];
        weakObject.contentView.backgroundColor = bgColor;
        weakObject.titleLabel.textColor = IMY_COLOR_KEY(kCK_Black_B);
    }];
}

- (void)updateWithCurrentPricing:(IMYSubGuidePricingListItem * const)currentPricing {
    if (self.currentPricing != currentPricing) {
        // 初始化子视图
        [self setupSubviews];
        // 复制价格包
        self.currentPricing = currentPricing;
        self.giftInfo = currentPricing.gift_info;
        // 标题
        self.titleLabel.text = self.giftInfo.title;
        // 刷新赠礼区
        [self resetContentViews];
    }
}

- (void)onlyUpdateSelectedItems:(IMYSubGuidePricingListItem * const)currentPricing {
    IMYSubGuidePricingGiftInfoModel * const gift_info = currentPricing.gift_info;
    if (!gift_info.gift_list.count) {
        self.selectedItems = @[];
        return;
    }
    if (gift_info.type == 2) {
        // 多选（全选，不可取消）
        self.selectedItems = gift_info.gift_list;
    } else {
        // 单选
        NSMutableArray *selectedItems = [NSMutableArray array];
        BOOL isFirstSelected = NO;
        for (IMYSubGuidePricingGiftInfoListItem *item in gift_info.gift_list) {
            if (item.hasCountdown) {
                // 促销赠礼一定选中
                [selectedItems addObject:item];
            } else if (!isFirstSelected) {
                // 默认选中第一个普通赠礼
                isFirstSelected = YES;
                [selectedItems addObject:item];
            }
        }
        self.selectedItems = selectedItems;
    }
}

- (void)updateWithArrowX:(CGFloat const)itemCenterX {
    // 角标位置修正，外部传入的是价格cell的center（外部已经有做 gift view 偏移修正，内部还需要做 arrowBoxView 偏移修正）
    CGFloat const arrowX = itemCenterX - kArrowCornerRadius;
    CGFloat const maxRightX = self.arrowBoxView.imy_width - kArrowCornerRadius;
    if (arrowX < kArrowCornerRadius) {
        self.arrowIconView.imy_centerX = MIN(kArrowCornerRadius, arrowX + _arrowItemWidth);
    } else if (arrowX > maxRightX) {
        self.arrowIconView.imy_centerX = MAX(maxRightX, arrowX - _arrowItemWidth);
    } else {
        self.arrowIconView.imy_centerX = arrowX;
    }
}

- (void)setArrowItemWidth:(CGFloat const)arrowItemWidth {
    _arrowItemWidth = arrowItemWidth / 2.0 - 7;
}

- (void)resetContentViews {
    [self.contentView imy_removeAllSubviews];
    
    NSInteger const allCount = self.giftInfo.gift_list.count;
    if (allCount == 0 || IMYRightsSDK.isSubAuditReview) {
        // 审核模式，不展示赠礼
        self.selectedItems = @[];
        self.alpha = 0;
        self.imy_height = 0;
        return;
    }
    if (self.giftInfo.type == 2) {
        // 多选（全选，不可取消）
        self.selectedItems = self.giftInfo.gift_list;
    } else {
        // 单选
        NSMutableArray *selectedItems = [NSMutableArray array];
        BOOL isFirstSelected = NO;
        for (IMYSubGuidePricingGiftInfoListItem *item in self.giftInfo.gift_list) {
            if (item.hasCountdown) {
                // 促销赠礼一定选中
                [selectedItems addObject:item];
            } else if (!isFirstSelected) {
                // 默认选中第一个普通赠礼
                isFirstSelected = YES;
                [selectedItems addObject:item];
            }
        }
        self.selectedItems = selectedItems;
    }
    // 不执行动画
    [UIView performWithoutAnimation:^{
        [self resetContentSubviews];
    }];
    
    // 显示框架
    self.alpha = 1;
    self.imy_height = self.contentView.imy_bottom;
}

- (void)resetContentSubviews {
    // 赠礼数量
    NSInteger const allCount = self.giftInfo.gift_list.count;
    // 是否全选
    BOOL const isAllSelected = (allCount < 2 || self.giftInfo.type == 2 || self.selectedItems.count == allCount);
    // 图片样式
    CGFloat lastLeftX = 8;
    CGFloat maxBottomY = 0;
    CGFloat cardWidth = 0;
    
    /// 单卡、双卡、多卡
    NSInteger const cardStyle = (allCount == 1 ? 1 : allCount == 2 ? 2 : 3);
    if (cardStyle == 1) {
        cardWidth = ceil(self.contentView.imy_width - 8 - 8);
    } else if (cardStyle == 2) {
        cardWidth = ceil((self.contentView.imy_width - 8 - 8 - 4) / 2.0);
    } else {
        cardWidth = ceil((self.contentView.imy_width - 8 - 8 - 4 - 4) / 2.2);
    }
    
    // 生成赠礼View
    @weakify(self);
    for (NSInteger index = 0; index < allCount; index ++) {
        
        IMYSubGuidePricingGiftInfoListItem * const item = self.giftInfo.gift_list[index];
        
        IMYSGVipGiftItemView *itemView = [IMYSGVipGiftItemView new];
        itemView.cardWidth = cardWidth;
        itemView.cardStyle = cardStyle;
        itemView.isSelected = (isAllSelected ? 0 : [self.selectedItems containsObject:item] ? 1 : 2);
        itemView.giftItem = item;
        
        itemView.imy_left = lastLeftX;
        itemView.imy_top = 26;
        itemView.tag = 100 + index;
        [self.contentView addSubview:itemView];
        
        lastLeftX = itemView.imy_right + 4;
        maxBottomY = MAX(maxBottomY, itemView.imy_bottom);
        
        // 曝光埋点
        itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_kazlmk_%ld_%ld", self.currentPricing.id, item.id];
        itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"event"] = @"dy_kazlmk";
            gaParams[@"action"] = @1;
            gaParams[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
            gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.sceneKey];
            gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
            gaParams[@"subscribe_price"] = self.currentPricing.real_discount_price ?: @"0";
            gaParams[@"sub_price_id"] = @(self.currentPricing.id);
            gaParams[@"info_id"] = @(item.id);
            gaParams[@"index"] = @(index + 1);
            [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
        };
        
        // 点击事件
        @weakify(self);
        itemView.userInteractionEnabled = YES;
        [itemView bk_whenTapped:^{
            @strongify(self);
            if (![self.giftInfo.gift_list containsObject:item]) {
                // 数据源被刷新，找不到源item
                return;
            }
            
            // 全选，不可取消
            if (isAllSelected) {
                return;
            }
            
            if (item.hasCountdown) {
                // 促销赠礼不可取消
                return;
            }
            
            // 单选(已经选中)
            if ([self.selectedItems containsObject:item]) {
                return;
            }
            
            // 获取上一个选中的普通赠礼
            IMYSubGuidePricingGiftInfoListItem * const oldItem = [self.selectedItems bk_match:^BOOL(IMYSubGuidePricingGiftInfoListItem *obj) {
                return !obj.hasCountdown;
            }];
            
            // 增加最新选中的赠礼
            NSMutableArray *selectedItems = [NSMutableArray array];
            for (IMYSubGuidePricingGiftInfoListItem *item in self.giftInfo.gift_list) {
                if (item.hasCountdown) { // 促销赠礼一定选中
                    [selectedItems addObject:item];
                }
            }
            [selectedItems addObject:item];
            
            self.selectedItems = selectedItems;
            [self refreshContentSelectedItems];
            if (self.onSelectItemDidChanged) {
                self.onSelectItemDidChanged();
            }
            
            // 取消选中埋点
            if (oldItem) {
                NSUInteger const oldIndex = [self.giftInfo.gift_list indexOfObject:oldItem];
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"event"] = @"dy_kazlmk_qxxzzl";
                gaParams[@"action"] = @2;
                gaParams[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
                gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.sceneKey];
                gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
                gaParams[@"subscribe_price"] = self.currentPricing.real_discount_price ?: @"0";
                gaParams[@"sub_price_id"] = @(self.currentPricing.id);
                gaParams[@"info_id"] = @(oldItem.id);
                gaParams[@"index"] = @(oldIndex + 1);
                [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            }
            
            // 选中埋点
            {
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"event"] = @"dy_kazlmk";
                gaParams[@"action"] = @2;
                gaParams[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
                gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.sceneKey];
                gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
                gaParams[@"subscribe_price"] = self.currentPricing.real_discount_price ?: @"0";
                gaParams[@"sub_price_id"] = @(self.currentPricing.id);
                gaParams[@"info_id"] = @(item.id);
                gaParams[@"index"] = @(index + 1);
                [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            }
        }];
    }
    // 滚动区域设置
    self.contentView.contentSize = CGSizeMake(lastLeftX + 4, 0);
    self.contentView.scrollEnabled = (lastLeftX - 5 > self.contentView.imy_width);
    self.contentView.contentOffset = CGPointZero;
    
    // 刷新选中状态
    self.contentView.imy_height = ceil(maxBottomY + 8);
}

- (void)refreshContentSelectedItems {
    // 刷新选中状态
    NSInteger const allCount = self.giftInfo.gift_list.count;
    if (allCount < 2 || self.giftInfo.type == 2) { // 全选
        return;
    }
    
    for (NSInteger index = 0; index < allCount; index ++) {
        IMYSGVipGiftItemView *itemView = [self.contentView viewWithTag:100 + index];
        itemView.isSelected = [self.selectedItems containsObject:itemView.giftItem] ? 1 : 2;
    }
}

@end
