//
//  IMYSGPayRetSuccessViewV1.m
//  IMYBaseKit
//
//  Created by ljh on 2025/4/11.
//

#import "IMYSGPayRetSuccessViewV1.h"
#import "IMYBaseKit.h"
#import "IMYSubGuideManager.h"
#import "IMYSGVipGiftItemViewV2.h"

#pragma mark - V1 版本

@interface IMYSGPayRetSuccessViewV1 ()

@property (nonatomic, copy) NSDictionary *rawData;

@property (nonatomic, strong) UIView *blankView;

@property (nonatomic, strong) UIView *cardView;

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *topImageView;

@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@property (nonatomic, strong) UIImageView *rightsImageView;

@property (nonatomic, strong) UIScrollView *giftInfoView;

@property (nonatomic, strong) IMYTouchEXButton *gotoButton;

@property (nonatomic, copy) void(^onBlankClickBlock)(NSInteger type);

@end

@implementation IMYSGPayRetSuccessViewV1

- (instancetype)initWithRawData:(NSDictionary *)rawData {
    self = [self initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    if (self) {
        self.rawData = rawData;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    
    self.blankView = [UIView new];
    self.blankView.userInteractionEnabled = NO;
    @weakify(self);
    [self.blankView bk_whenTapped:^{
        @strongify(self);
        if (self.onBlankClickBlock) {
            self.onBlankClickBlock(0);
        }
    }];
    [self addSubview:self.blankView];
    
    self.cardView = [UIView new];
    self.cardView.frame = CGRectMake(0, 0, SCREEN_WIDTH, 200);
    [self addSubview:self.cardView];
    
    {
        // 背景
        self.edgeBoxView = [UIView new];
        self.edgeBoxView.frame = self.cardView.bounds;
        self.edgeBoxView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        self.edgeBoxView.backgroundColor = IMY_COLOR_KEY(@"#FFF6F9");
        [self.edgeBoxView imy_drawTopCornerRadius:12];
        [self.cardView addSubview:self.edgeBoxView];
        
        UIImageView *topBgView = [UIImageView new];
        topBgView.frame = IMYFrameBy375Design(0, 0, 375, 160);
        [topBgView imy_setImage:@"sub_guide_sheet_topbg"];
        [self.edgeBoxView addSubview:topBgView];
    }
    
    {
        // 标题栏
        self.topImageView = [UIImageView new];
        CGFloat aspect_ratio = [self.rawData[@"title_img_aspect_ratio"] doubleValue];
        if (aspect_ratio < 0.1) {
            aspect_ratio = 2.678;
        }
        CGFloat imgWidth = SCREEN_WIDTH;
        CGFloat imgHeight = ceil(imgWidth / aspect_ratio);
        self.topImageView.frame = CGRectMake(0, 70 - imgHeight, imgWidth, imgHeight);
        [self.cardView addSubview:self.topImageView];
        
        [self.topImageView imy_setImageURL:self.rawData[@"title_img"]];
    }
    
    {
        // 内容栏
        self.rightsImageView = [UIImageView new];
        CGFloat aspect_ratio = [self.rawData[@"rights_img_aspect_ratio"] doubleValue];
        if (aspect_ratio < 0.1) {
            aspect_ratio = 2.678;
        }
        CGFloat imgWidth = SCREEN_WIDTH - 24;
        CGFloat imgHeight = ceil(imgWidth / aspect_ratio);
        self.rightsImageView.frame = CGRectMake(12, self.topImageView.imy_bottom + 12, imgWidth, imgHeight);
        [self.cardView addSubview:self.rightsImageView];
        
        [self.rightsImageView imy_setImageURL:self.rawData[@"rights_img"]];
        
        NSString *rights_jump_url = self.rawData[@"rights_jump_url"];
        self.rightsImageView.userInteractionEnabled = YES;
        [self.rightsImageView bk_whenTapped:^{
            @strongify(self);
            // 中间权益点击埋点
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"event"] = @"dy_ktcgtc";
            gaParams[@"action"] = @2;
            gaParams[@"index"] = @1;
            gaParams[@"subscribe_type"] = @(self.session.sub_type);
            gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
            gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
            [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            
            // 关闭弹窗
            if ([rights_jump_url isEqualToString:@"meiyou:///close_me"]) {
                [self dismiss];
                return;
            }
            if (rights_jump_url.length > 0) {
                // 无赠礼，直接关闭
                if (self.giftInfoView.imy_height < 1) {
                    [self dismiss];
                }
                // 跳转协议
                [[IMYURIManager shareURIManager] runActionWithString:rights_jump_url];
            }
        }];
    }
    
    {
        // 赠礼
        self.giftInfoView = [UIScrollView new];
        self.giftInfoView.frame = CGRectMake(0, self.rightsImageView.imy_bottom + 8, SCREEN_WIDTH, 0);
        self.giftInfoView.showsHorizontalScrollIndicator = NO;
        [self.cardView addSubview:self.giftInfoView];
        
        NSArray * const giftInfos = self.rawData[@"gift_info"];
        if (giftInfos.count > 0) {
            CGFloat imgWidth = giftInfos.count == 1 ? IMYIntegerBy375Design(351) : IMYIntegerBy375Design(236);;
            CGFloat imgHeight = 64;
            
            [giftInfos enumerateObjectsUsingBlock:^(NSDictionary *giftData, NSUInteger idx, BOOL * _Nonnull stop) {
                IMYSGVipGiftItemViewV2 *itemView = [IMYSGVipGiftItemViewV2 new];
                itemView.cardWidth = imgWidth;
                itemView.cardStyle = 2;
                itemView.rawData = giftData;
                
                itemView.imy_left = 12 + idx * (imgWidth + 8);
                
                NSString * const itemId = giftData[@"id"];
                NSString * const itemJump = giftData[@"jump_url"];
                itemView.userInteractionEnabled = YES;
                [itemView bk_whenTapped:^{
                    @strongify(self);
                    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                    gaParams[@"event"] = @"dy_ktcgtc_qyw";
                    gaParams[@"action"] = @2;
                    gaParams[@"subscribe_type"] = @(self.session.sub_type);
                    gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
                    gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
                    gaParams[@"info_id"] = itemId;
                    gaParams[@"index"] = @(idx + 1);
                    [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
                    // 关闭弹窗
                    if ([itemJump isEqualToString:@"meiyou:///close_me"]) {
                        [self dismiss];
                        return;
                    }
                    if (itemJump.length > 0) {
                        [[IMYURIManager shareURIManager] runActionWithString:itemJump];
                    }
                }];
                [self.giftInfoView addSubview:itemView];
                
                // 曝光埋点
                itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_ktcgtc_qyw-%@", itemId];
                itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                    @strongify(self);
                    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                    gaParams[@"event"] = @"dy_ktcgtc_qyw";
                    gaParams[@"action"] = @1;
                    gaParams[@"subscribe_type"] = @(self.session.sub_type);
                    gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
                    gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
                    gaParams[@"info_id"] = itemId;
                    gaParams[@"index"] = @(idx + 1);
                    [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
                };
            }];
            
            // 数量大于2，则需要滚动
            CGFloat const allWidth = (12 + giftInfos.count * (imgWidth + 8) - 8 + 12);
            self.giftInfoView.contentSize = CGSizeMake(allWidth, 0);
            self.giftInfoView.scrollEnabled = (allWidth - 5 > self.giftInfoView.imy_width);
            self.giftInfoView.contentOffset = CGPointZero;
            self.giftInfoView.imy_height = imgHeight;
        }
    }
    
    {
        // 按钮
        self.gotoButton = [IMYTouchEXButton new];
        self.gotoButton.backgroundColor = IMY_COLOR_KEY(@"#FF4D88");
        self.gotoButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [self.gotoButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        [self.gotoButton setTitle:self.rawData[@"btn_text"] forState:UIControlStateNormal];
        self.gotoButton.frame = CGRectMake(12, 0, self.cardView.imy_width - 24, 48);
        if (self.giftInfoView.imy_height > 1) {
            self.gotoButton.imy_top = self.giftInfoView.imy_bottom + 20;
        } else {
            self.gotoButton.imy_top = self.rightsImageView.imy_bottom + 20;
        }
        [self.gotoButton imy_drawAllCornerRadius:24];
        
        {
            // 渐变背景色
            CAGradientLayer *gradientLayer = [CAGradientLayer layer];
            gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                     (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                     (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
            gradientLayer.locations = @[@0.0, @0.5, @1.0];
            gradientLayer.startPoint = CGPointMake(0, 0);
            gradientLayer.endPoint = CGPointMake(1, 0);
            gradientLayer.frame = self.gotoButton.bounds;
            [self.gotoButton.layer insertSublayer:gradientLayer atIndex:0];
        }
        
        [self.cardView addSubview:self.gotoButton];
        
        NSString *btn_jump_url = self.rawData[@"btn_jump_url"];
        [self.gotoButton bk_whenTapped:^{
            @strongify(self);
            // 底部按钮点击埋点
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"event"] = @"dy_ktcgtc";
            gaParams[@"action"] = @2;
            gaParams[@"index"] = @0;
            gaParams[@"subscribe_type"] = @(self.session.sub_type);
            gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
            gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
            [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            
            // 无赠礼，直接关闭
            if ([btn_jump_url isEqualToString:@"meiyou:///close_me"]) {
                [self dismiss];
                return;
            }
            if (self.giftInfoView.imy_height < 1) {
                [self dismiss];
            }
            // 跳转协议
            if (btn_jump_url.length > 0) {
                [[IMYURIManager shareURIManager] runActionWithString:btn_jump_url];
            }
        }];
    }
    
    {
        // 关闭按钮
        self.closeButton = [IMYTouchEXButton new];
        [self.closeButton imy_setImage:@"dyzf_icon_close"];
        self.closeButton.imy_size = CGSizeMake(20, 20);
        self.closeButton.imy_right = self.cardView.imy_width - 12;
        self.closeButton.imy_top = 12;
        [self.cardView addSubview:self.closeButton];
        
        NSString *close_jump_url = self.rawData[@"close_jump_url"];
        self.onBlankClickBlock = ^(NSInteger type) {
            @strongify(self);
            // 关闭自己
            [self dismiss];
            // 关闭按钮协议
            if (close_jump_url.length > 0) {
                [[IMYURIManager shareURIManager] runActionWithString:close_jump_url];
            }
        };
        [self.closeButton bk_whenTapped:^{
            @strongify(self);
            if (self.onBlankClickBlock) {
                self.onBlankClickBlock(1);
            }
        }];
    }
    
    self.cardView.imy_height = self.gotoButton.imy_bottom + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    
    // [dy_ktcgtc]：订阅_开通成功弹窗
    self.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_ktcgtc-%p", self];
    [self.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"event"] = @"dy_ktcgtc";
        gaParams[@"action"] = @1;
        gaParams[@"subscribe_type"] = @(self.session.sub_type);
        gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
        gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
        [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
    }];
}

- (void)show {
    [super show];
    
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    {
        // 保证全局只有一个弹窗
        IMYSubGuidePayRetSuccessView *oldView = [window.subviews bk_match:^BOOL(id obj) {
            return [obj isKindOfClass:IMYSubGuidePayRetSuccessView.class];
        }];
        if (oldView) {
            [oldView dismiss];
        }
    }
    
    self.frame = window.bounds;
    [window addSubview:self];
    
    self.alpha = 0;
    self.cardView.imy_top = self.imy_height;
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 1;
        self.cardView.imy_top = self.imy_height - self.cardView.imy_height;
    } completion:^(BOOL finished) {
        self.blankView.frame = CGRectMake(0, 0, self.imy_width, self.cardView.imy_top + self.topImageView.imy_top);
        self.blankView.userInteractionEnabled = YES;
    }];
    
    if (self.giftInfoView.imy_height < 1) {
        // 无赠礼
        return;
    }
    //close_me
    
    // 有赠礼，当回到当前页面时，会继续显示该弹窗
    IMYPublicBaseViewController *currentVC = [window.rootViewController imy_currentShowViewController];
    @weakify(self, currentVC);
    [[NSNotificationCenter defaultCenter] addObserverForName:IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull notification) {
        @strongify(self, currentVC);
        if (!self) {
            return;
        }
        IMYPublicBaseViewController *nowVC = notification.object;
        if (!nowVC.imy_isShowVC) {
            return;
        }
        if ((nowVC.isViewActived && nowVC != currentVC) || !currentVC.isViewActived) {
            self.userInteractionEnabled = NO;
            if (self.alpha != 0) {
                [UIView animateWithDuration:0.15 animations:^{
                    self.alpha = 0;
                }];
            }
        } else if (currentVC.isViewActived) {
            if (self.alpha != 1) {
                [UIView animateWithDuration:0.15 animations:^{
                    self.alpha = 1;
                } completion:^(BOOL finished) {
                    self.userInteractionEnabled = YES;
                }];
            }
        }
    }];
    
    // 跟随当前页面一起销毁
    [currentVC.rac_willDeallocSignal subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (!self) {
            return;
        }
        [self dismiss];
    }];
}

- (void)dismiss {
    // 关闭动画
    [super dismiss];
    
    self.userInteractionEnabled = NO;
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
        self.cardView.imy_top = self.imy_height;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
    
    // 执行关闭弹窗回调（只有一次）
    if (self.onClosedBlock) {
        self.onClosedBlock();
        self.onClosedBlock = nil;
    }
}

@end

