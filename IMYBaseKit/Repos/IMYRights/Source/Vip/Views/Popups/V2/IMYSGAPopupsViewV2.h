//
//  IMYSGAPopupsViewV2.h
//  IMYBaseKit
//
//  Created by ljh on 2025/2/24.
//

#import <UIKit/UIKit.h>
#import "IMYSGAPopupsViewProtocol.h"

NS_ASSUME_NONNULL_BEGIN
 
/// https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001255522
/// https://pixso.cn/app/design/WrMg5mSxRUb2uSqA1x3Yzg?page-id=2%3A1
/// 有开红包流程
@interface IMYSGAPopupsViewV2 : UIView <IMYSGAPopupsViewProtocol>

/// 对应配置
@property (nonatomic, strong) IMYSubGuideSession *session;
@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, weak) IMYSubGuideVipPriceCell *currentPriceCell;

/// 价格包位置
@property (nonatomic, assign) CGRect currentPriceBoxFrame;

/// 触发场景：1：进入支付页 2：退出支付页 3：取消支付
@property (nonatomic, assign) IMYSGRPopupsTriggerScene userTriggerScene;

/// 弹窗关闭回调，0：确认按钮，1：关闭按钮，2：离开按钮
@property (nonatomic, copy) void(^onDismissedBlock)(NSInteger actionType);

/// 弹窗数据
@property (nonatomic, copy) NSDictionary *popupInfo;
@property (nonatomic, assign) NSInteger popupPricingId;
@property (nonatomic, copy) NSArray<NSDictionary *> *popupValidList;
@property (nonatomic, copy) NSInteger (^onTimeChangeBlock)(void);

- (void)show;
- (void)dismiss;

@end

NS_ASSUME_NONNULL_END
