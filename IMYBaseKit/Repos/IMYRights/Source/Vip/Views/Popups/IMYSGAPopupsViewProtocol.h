//
//  IMYSGAPopupsViewProtocol.h
//  IMYBaseKit
//
//  Created by ljh on 2025/2/24.
//

#import "IMYBaseKit.h"
#import "IMYSubGuideSession.h"
#import "IMYSubGuideVipPriceCell.h"

NS_ASSUME_NONNULL_BEGIN

/// 弹窗组件
@protocol IMYSGAPopupsViewProtocol <NSObject>

/// 对应配置
@property (nonatomic, strong) IMYSubGuideSession *session;
@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, weak) IMYSubGuideVipPriceCell *currentPriceCell;

/// 价格包位置
@property (nonatomic, assign) CGRect currentPriceBoxFrame;

/// 触发场景：1：进入支付页 2：退出支付页 3：取消支付
@property (nonatomic, assign) IMYSGRPopupsTriggerScene userTriggerScene;

/// 弹窗关闭回调，0：确认按钮，1：关闭按钮，2：离开按钮
@property (nonatomic, copy) void(^onDismissedBlock)(NSInteger actionType);

/// 弹窗数据
@property (nonatomic, copy) NSDictionary *popupInfo;
@property (nonatomic, assign) NSInteger popupPricingId;
@property (nonatomic, copy) NSArray<NSDictionary *> *popupValidList;
@property (nonatomic, copy) NSInteger (^onTimeChangeBlock)(void);

- (void)show;
- (void)dismiss;

@end

#pragma mark - 弹窗内容详情View

/// 弹窗内容详情View
@protocol IMYSGAPopupsViewDetailViewProtocol <NSObject>

/// 图片（IMYSGAPopupsViewV5DetailView）
@property (nonatomic, copy) NSString *rawImage;

/// 数据源
@property (nonatomic, copy) NSDictionary *rawDatas;

/// 初始化UI
- (void)setupSubviews;

@end


NS_ASSUME_NONNULL_END
