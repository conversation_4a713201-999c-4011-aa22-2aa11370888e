//
//  IMYSGAPopupsViewV5.m
//  IMYBaseKit
//
//  Created by ljh on 2025/9/5.
//

#import "IMYSGAPopupsViewV5.h"
#import "IMYSubGuideCountdownView.h"
#import "IMYSubGuideManager.h"
#import "IMYSubGuideRPAnimationView.h"
#import <SDWebImage/SDWebImageDecoder.h>
#import "IMYSGAPopupsViewV5DetailView.h"

@interface IMYSGAPopupsViewV5 ()

@property (nonatomic, strong) UIView *bgView;

@property (nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;

@property (nonatomic, strong) UIView<IMYSGAPopupsViewDetailViewProtocol> *detailView;

@property (nonatomic, strong) UIImageView *detailShadowView;

@property (nonatomic, strong) IMYSubGuideCountdownView *countdownView;

@property (nonatomic, strong) IMYTouchEXButton *leftBtn;
@property (nonatomic, strong) IMYTouchEXButton *rightBtn;
@property (nonatomic, strong) IMYTouchEXButton *closeBtn;

@property (nonatomic, strong) YYImage *flyingAnimImage;

@property (nonatomic, strong) NSString *popupTargetPrice;

@property (nonatomic, assign) BOOL isAnimStarted;
@property (nonatomic, assign) BOOL isDismissing;

@end

@implementation IMYSGAPopupsViewV5

#pragma mark - UI

- (void)setupSubviews {
    self.bgView.frame = self.bounds;
    [self addSubview:self.bgView];
    
    self.contentView.imy_size = CGSizeMake(320, 320);
    [self addSubview:self.contentView];
    
    // 主标题
    self.titleLabel.frame = CGRectMake(12, 20, self.contentView.imy_width - 24, 24);
    [self.contentView addSubview:self.titleLabel];
    NSString * const title = self.popupInfo[@"promotion"][@"title"];
    self.titleLabel.text = title;
    
    // 副标题
    self.subTitleLabel.frame = CGRectMake(12, self.titleLabel.imy_bottom + 4, self.contentView.imy_width - 24, 21);
    [self.contentView addSubview:self.subTitleLabel];
    NSString * const subtitle = self.popupInfo[@"promotion"][@"desc"];
    if (subtitle.length > 0) {
        self.subTitleLabel.text = subtitle;
        self.subTitleLabel.hidden = NO;
    } else {
        self.subTitleLabel.text = nil;
        self.subTitleLabel.hidden = YES;
    }
    
    // 投影区
    self.detailShadowView.imy_centerX = self.titleLabel.imy_centerX;
    self.detailShadowView.imy_top = 94;
    [self.contentView addSubview:self.detailShadowView];
    
    // 主内容区 （目前暂不支持其他样式）
    self.detailView = [IMYSGAPopupsViewV5DetailView new];
    [self.detailView setupSubviews];
    self.detailView.imy_centerX = self.titleLabel.imy_centerX;
    if (self.subTitleLabel.hidden) {
        self.detailView.imy_top = self.titleLabel.imy_bottom + 28;
    } else {
        self.detailView.imy_top = self.subTitleLabel.imy_bottom + 28;
    }
    [self.contentView addSubview:self.detailView];
    
    // 倒计时
    BOOL const hasShowCountDown = ([self.popupInfo[@"promotion"][@"expose_config"][@"show_countdown"] integerValue] == 1);
    if (hasShowCountDown) {
        [self.countdownView setupWithStyle:3];
        self.countdownView.text = @"限时特惠结束仅剩";
        self.countdownView.onTimeChangeBlock = self.onTimeChangeBlock;
        [self.countdownView startTimer];
    } else {
        self.countdownView.hidden = YES;
    }
    self.countdownView.imy_centerX = self.detailView.imy_centerX;
    self.countdownView.imy_top = self.detailView.imy_bottom + 30;
    [self.contentView addSubview:self.countdownView];
    
    if (self.userTriggerScene == IMYSGRPopupsTriggerScenePaymentOut) {
        // 返回按钮
        self.leftBtn.hidden =  NO;
        self.leftBtn.imy_top = self.countdownView.imy_bottom + 20;
        self.leftBtn.imy_left = 16;
        [self.contentView addSubview:self.leftBtn];
        
        [self.leftBtn setTitle:@"忍痛离开" forState:UIControlStateNormal];
        
        // 确认按钮
        self.rightBtn.imy_top = self.countdownView.imy_bottom + 20;
        self.rightBtn.imy_right = self.contentView.imy_width - 16;
        [self.contentView addSubview:self.rightBtn];
        
        [self.rightBtn setTitle:@"立即收下" forState:UIControlStateNormal];
    } else {
        // 确认按钮
        self.rightBtn.imy_top = self.countdownView.imy_bottom + 20;
        self.rightBtn.imy_width = self.contentView.imy_width - 32;
        self.rightBtn.imy_left = 16;
        [self.contentView addSubview:self.rightBtn];
        
        [self.rightBtn setTitle:@"立即收下" forState:UIControlStateNormal];
        
        // 修正按钮渐变CALayer
        for (CAGradientLayer *gradientLayer in self.rightBtn.layer.sublayers) {
            if ([gradientLayer isKindOfClass:CAGradientLayer.class]) {
                gradientLayer.frame = self.rightBtn.bounds;
            }
        }
    }
    
    // 修正容器高度
    self.contentView.imy_height = self.rightBtn.imy_bottom + 20;
    self.contentView.center = self.imy_selfcenter;
    
    // 关闭按钮
    self.closeBtn.imy_centerX = self.contentView.imy_centerX;
    self.closeBtn.imy_top = self.contentView.imy_bottom + 16;
    [self addSubview:self.closeBtn];
    
    // 寻找对应价格包的实际售价
    NSDictionary *validListMap = nil;
    
    // 赠礼弹窗，直接获取第一个价格包配置信息即可
    self.popupTargetPrice = self.popupValidList.firstObject[@"price"];
    validListMap = self.popupValidList.firstObject;
    
    // 赋值内容区
    NSInteger const show_type = [self.popupInfo[@"promotion"][@"show_type"] integerValue];
    if (show_type == 7) {
        // 自定义图片样式
        self.detailView.rawImage = self.popupInfo[@"promotion"][@"image"];
        self.detailShadowView.hidden = YES;
    }
    self.detailView.rawDatas = validListMap;
    
    // 取消支付，优惠价格包id 跟 当前支付一致
    if (self.userTriggerScene == IMYSGRPopupsTriggerScenePaymentCancel) {
        [self.rightBtn setTitle:@"继续支付" forState:UIControlStateNormal];
    }
    
    // 预加载动画资源
    [self asyncPrefetchAnimImages];
}

- (void)asyncPrefetchAnimImages {
    @weakify(self);
    imy_asyncBlock(0.5, ^{
        @strongify(self);
        NSString *animPath = [[NSBundle mainBundle] pathForResource:@"vip_sga_step3_flying_2" ofType:@"apng"];
        if (animPath.length > 0) {
            self.flyingAnimImage = [YYImage imageWithContentsOfFile:animPath];
            self.flyingAnimImage.preloadAllAnimatedImageFrames = YES;
        }
    });
}

- (void)startAnimations {
    // 缩放动画
    self.contentView.userInteractionEnabled = NO;
    self.contentView.transform = CGAffineTransformMakeScale(0.5, 0.5);
    self.closeBtn.alpha = 0;
    [UIView animateWithDuration:0.33 animations:^{
        self.contentView.transform = CGAffineTransformMakeScale(1.1, 1.1);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.15 animations:^{
            self.contentView.transform = CGAffineTransformMakeScale(1.0, 1.0);
            self.closeBtn.alpha = 1;
        } completion:^(BOOL finished) {
            self.contentView.userInteractionEnabled = YES;
        }];
    }];
    
    self.bgView.alpha = 0;
    [UIView animateWithDuration:0.05 animations:^{
        self.bgView.alpha = 1;
    }];
}

- (void)onRightButtonPressed:(id)sender {
    if (_isAnimStarted) {
        return;
    }
    _isAnimStarted = YES;
    // 执行飞入动效
    [self startConfirmFlipAnimation];
    // 确认按钮埋点
    [self biReportWithAction:2];
}

- (void)onLeftButtonPressed:(id)sender {
    // 取消按钮
    [self dismissWithAction:2];
}

- (void)onCloseButtonPressed:(id)sender {
    // 不执行动效，直接关闭
    [self dismissWithAction:1];
}

- (void)startConfirmFlipAnimation {
    // 无飞入动效，直接关闭
    [self dismissWithAction:0];
}

- (void)biReportWithAction:(NSInteger)action {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"event"] = @"dy_yhhbtc";
    gaParams[@"action"] = @(action);
    gaParams[@"subscribe_type"] = @([IMYRightsSDK sharedInstance].currentSubscribeType);
    gaParams[@"public_type"] = [IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey];
    gaParams[@"public_info"] = [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否";
    gaParams[@"subscribe_price"] = self.popupTargetPrice ?: self.currentPricing.real_discount_price ?: @"0";
    gaParams[@"sub_price_id"] = @(self.currentPricing.id);
    gaParams[@"info_id"] = [self.popupInfo[@"id"] stringValue] ?: @"0";
    [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
}

- (void)show {
    // 显示UI
    [self setupSubviews];
    
    // 执行显示动画
    [self startAnimations];
    
    // 埋点
    [self biReportWithAction:1];
}

- (void)dismiss {
    [self dismissWithAction:0];
}

/// 弹窗关闭回调，0：确认按钮，1：关闭按钮，2：离开按钮
- (void)dismissWithAction:(NSInteger const)action {
    if (_isDismissing) {
        return;
    }
    _isDismissing = YES;
    if (self.hidden || self.contentView.hidden) {
        [self removeFromSuperview];
        if (self.onDismissedBlock) {
            self.onDismissedBlock(action);
        }
    } else {
        self.alpha = 1;
        [UIView animateWithDuration:0.3 animations:^{
            self.alpha = 0;
        } completion:^(BOOL finished) {
            self.hidden = YES;
            [self removeFromSuperview];
            if (self.onDismissedBlock) {
                self.onDismissedBlock(action);
            }
        }];
    }
}

#pragma mark - Propertys

- (IMYTouchEXButton *)leftBtn {
    if (!_leftBtn) {
        _leftBtn = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 140, 40)];
        _leftBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_leftBtn setTitleColor:IMY_COLOR_KEY(@"#FF4D88") forState:UIControlStateNormal];
        _leftBtn.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        _leftBtn.layer.borderWidth = 1;
        _leftBtn.backgroundColor = UIColor.whiteColor;
        [_leftBtn imy_drawAllCornerRadius:20];
        [_leftBtn addTarget:self action:@selector(onLeftButtonPressed:) forControlEvents:UIControlEventTouchUpInside];
        _leftBtn.hidden = YES;
    }
    return _leftBtn;
}

- (IMYTouchEXButton *)rightBtn {
    if (!_rightBtn) {
        _rightBtn = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 140, 40)];
        _rightBtn.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_rightBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        [_rightBtn imy_drawAllCornerRadius:20];
        [_rightBtn addTarget:self action:@selector(onRightButtonPressed:) forControlEvents:UIControlEventTouchUpInside];
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _rightBtn.bounds;
        [_rightBtn.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _rightBtn;
}

- (IMYTouchEXButton *)closeBtn {
    if (!_closeBtn) {
        _closeBtn = [IMYTouchEXButton new];
        _closeBtn.imy_size = CGSizeMake(16, 16);
        [_closeBtn setExtendTouchAllValue:12];
        [_closeBtn imy_setImage:@"wltc_icon_close"];
        [_closeBtn addTarget:self action:@selector(onCloseButtonPressed:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeBtn;
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
        _bgView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.5];
    }
    return _bgView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [UIView new];
        [_contentView imy_setBackgroundColor:kCK_White_AN];
        [_contentView imy_drawAllCornerRadius:12];
    }
    return _contentView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.minimumScaleFactor = 0.5;
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [UILabel new];
        _subTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [_subTitleLabel imy_setTextColor:kCK_Black_M];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _subTitleLabel;
}

- (IMYSubGuideCountdownView *)countdownView {
    if (!_countdownView) {
        _countdownView = [[IMYSubGuideCountdownView alloc] initWithFrame:CGRectMake(0, 0, 320, 18)];
    }
    return _countdownView;
}

- (UIImageView *)detailShadowView {
    if (!_detailShadowView) {
        _detailShadowView = [UIImageView new];
        _detailShadowView.image = [UIImage imy_imageForKey:@"vip_sga_v4_detail_shadow"];
        _detailShadowView.imy_size = CGSizeMake(299, 158);
    }
    return _detailShadowView;
}

@end
