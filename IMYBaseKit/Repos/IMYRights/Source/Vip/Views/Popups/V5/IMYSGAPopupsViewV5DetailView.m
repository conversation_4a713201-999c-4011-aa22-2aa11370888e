//
//  IMYSGAPopupsViewV5DetailView.m
//  IMYBaseKit
//
//  Created by ljh on 2025/9/5.
//

#import "IMYSGAPopupsViewV5DetailView.h"
#import "IMYBaseKit.h"

@interface IMYSGAPopupsViewV5DetailView ()

@property (nonatomic, strong) UIImageView *bgView;

@property (nonatomic, strong) UILabel *rightTitle;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@end

@implementation IMYSGAPopupsViewV5DetailView

- (void)setupSubviews {
    self.imy_size = CGSizeMake(200, 106);
    
    self.bgView.frame = self.bounds;
    [self addSubview:self.bgView];
        
    // 设计是34的高度，但是为了避免被系统截断改为 50， 底部距离 12 需要调整为 4
    self.rightTitle.imy_size = CGSizeMake(100, 50);
    [self addSubview:self.rightTitle];
    
    // 用于现实渐变色
    self.gradientLayer = [CAGradientLayer layer];
    self.gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                  (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                  (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
    self.gradientLayer.locations = @[@0.0, @0.5, @1.0];
    self.gradientLayer.startPoint = CGPointMake(0, 0);
    self.gradientLayer.endPoint = CGPointMake(1, 0);
    
    // 使用 text.layer 作为蒙层
    self.gradientLayer.mask = self.rightTitle.layer;
    self.gradientLayer.frame = self.rightTitle.frame;
    self.gradientLayer.hidden = YES;
    
    [self.layer addSublayer:self.gradientLayer];
}

- (void)setRawDatas:(NSDictionary *)rawDatas {
    _rawDatas = [rawDatas copy];
    [self setupDatas];
}

- (void)setupDatas {
    // 判断是否图片样式
    if (self.rawImage.length > 0) {
        self.bgView.image = nil;
        [self.bgView imy_setOriginalImageURL:self.rawImage];
        return;
    }
    
    // 文本样式
    NSInteger const days = [_rawDatas[@"duration_value"] integerValue];
    if (days <= 0) {
        self.rightTitle.text = nil;
        self.gradientLayer.hidden = YES;
        return;
    }
    NSString * const unit = _rawDatas[@"duration_unit_text"] ?: @"";
    // 动态字体
    NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld%@", days, unit]];
    [attrs addAttributes:@{
        NSFontAttributeName : [UIFont systemFontOfSize:15 weight:UIFontWeightMedium],
        NSBaselineOffsetAttributeName : @1, // 底部偏移量，尽量数字跟文字对齐
    } range:NSMakeRange(attrs.length - unit.length, unit.length)];
    
    // 左右间隙减少
    [attrs addAttributes:@{
        NSKernAttributeName : @-1,
    } range:NSMakeRange(0, attrs.length)];
    
    self.rightTitle.attributedText = attrs;
    [self.rightTitle imy_sizeToFitWidth];
    
    // 距离底部 12（由于增高了需要改为 4），右边 14
    self.rightTitle.imy_bottom = self.imy_height - 4;
    self.rightTitle.imy_right = self.imy_width - 14;
    
    // 修正渐变层位置（text.layer 必须按 gradientLayer 的子layer来设置）
    self.gradientLayer.hidden = NO;
    self.gradientLayer.frame = self.rightTitle.frame;
    self.rightTitle.frame = self.gradientLayer.bounds;
}

- (UILabel *)rightTitle {
    if (!_rightTitle) {
        _rightTitle = [UILabel new];
        _rightTitle.font = [UIFont systemFontOfSize:35 weight:UIFontWeightSemibold];
        _rightTitle.textColor = IMY_COLOR_KEY(@"#FF4D88");
    }
    return _rightTitle;
}

- (UIImageView *)bgView {
    if (!_bgView) {
        _bgView = [UIImageView new];
        _bgView.image = [UIImage imageNamed:@"vip_sga_v4_detail_bg"];
    }
    return _bgView;
}

@end
