//
//  IMYSubGuideVipInfoVC.m
//  demo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/19.
//

#import "IMYSubGuideVipInfoVC.h"
#import "IMYBaseKit.h"

#import "IMYSubGuideManager.h"
#import "IMYSubGuideVipInfoCellModel.h"
#import "IMYSubGuideVipRightInfoCell.h"
#import "IMYSubGuideVipPriceListCell.h"
#import "IMYSubGuideVipRightsCardCell.h"
#import "IMYSubGuideImagesV2Cell.h"
#import "IMYSubGuideImageBoxCell.h"
#import "IMYSubGuideRestoreVIPCell.h"
#import "IMYSubGuideUnlockView.h"
#import "IMYSubGuideProtocolReadView.h"

#import "IMYSubGuideVipRightVC.h"
#import "IMYSubGuideBringBackManager.h"
#import "IMYSubGuideRPAnimationView.h"
#import "IMYSubGuideVIPBannerView.h"
#import "IMYSubGuideCheckPrivateView.h"
#import "IMYSubGuideCountdownView.h"
#import "IMYSubGuidePopupsInfoView.h"

#import "IMYSubGuidePayRetView.h"

#import <IOC-Protocols/IOCAppMainTabVC.h>
#import <IOC-Protocols/IOCAppInfo.h>

@interface IMYSubGuideVipInfoVC () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) IMYSubGuideSession *session;
@property (nonatomic, strong) IMYSubGuideVipInfo *vipInfo;
@property (nonatomic, assign) IMYSubGuideLimitType backPopupLimitType;

@property (nonatomic, strong) IMYSubGuidePricingListItem *currentPricing;
@property (nonatomic, assign) NSInteger lastSelectedPricingType;

@property (nonatomic, strong) IMYSubGuideVIPBannerView *topBannerView;

@property (nonatomic, strong) NSMutableArray<IMYSubGuideVipInfoCellModel *> *cellModels;
@property (nonatomic, strong, readonly) IMYSubGuideVipPriceListCell *priceListCell;

@property (nonatomic, copy) NSArray<IMYSubGuidePricingGiftInfoListItem *> *selectedGiftItems;

@property (nonatomic, strong) UIImageView *topBGImageView;

/// 吸顶状态下头部的背景色
@property (nonatomic, strong) UIView *bigNavBarBGView;

// 导航栏
@property (nonatomic, strong) UIView *navBar;
@property (nonatomic, strong) UILabel *navTitleLabel;
@property (nonatomic, strong) UIView *navBarBottomLine;

@property (nonatomic, strong) UITableView *tableView;

// 悬浮开通按钮
@property (nonatomic, strong) IMYTouchEXView *openView;
@property (nonatomic, strong) IMYCapsuleButton *openBtn;
@property (nonatomic, strong) IMYSubGuideCheckPrivateView *openCheckPrivateView;
@property (nonatomic, strong) IMYSubGuideCountdownView *openCountdownView;

@property (nonatomic, weak) UIButton *payBtn;

@property (nonatomic, strong) IMYSubGuidePopupsInfoView *popupInfoView;

@property (nonatomic, strong) IMYCaptionViewV2 *captionView;

/// 0：未操作过 1: 有返回弹窗数据，2：已显示
@property (nonatomic, assign) NSInteger popupGobackStep;

/// 是否处于继续支付状态
@property (nonatomic, assign) BOOL isResumePaying;

@end


@interface IMYSubGuideVipInfoHitView : UIView
@property (nonatomic, weak) UIView *mFirstHitView;
@property (nonatomic, weak) UIView *mSecondHitView;
@end

@implementation IMYSubGuideVipInfoHitView

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    {
        // 返回按钮点击区
        UIView *mFirstHitView = self.mFirstHitView;
        if (!mFirstHitView.hidden && mFirstHitView.alpha > 0.01 && CGRectContainsPoint(mFirstHitView.frame, point)) {
            CGPoint hitPoint = CGPointMake(point.x, point.y - mFirstHitView.frame.origin.y);
            UIView *hitView = [mFirstHitView hitTest:hitPoint withEvent:event];
            if (hitView != nil) {
                return hitView;
            }
        }
    }
    
    {
        // 头部banner滚动区
        UIView *mSecondHitView = self.mSecondHitView;
        if (!mSecondHitView.hidden && mSecondHitView.alpha > 0.01 && CGRectContainsPoint(mSecondHitView.frame, point)) {
            CGPoint hitPoint = CGPointMake(point.x, point.y - mSecondHitView.frame.origin.y);
            UIView *hitView = [mSecondHitView hitTest:hitPoint withEvent:event];
            if (hitView != nil) {
                return hitView;
            }
        }
    }
    return [super hitTest:point withEvent:event];
}

@end

@implementation IMYSubGuideVipInfoVC

- (IMYSubGuideVipInfoVC *)initWithSession:(IMYSubGuideSession *)session {
    self = [super init];
    if (self) {
        self.session = session;
    }
    return self;
}

- (void)loadView {
    self.view = [IMYSubGuideVipInfoHitView new];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    if (!self.session) {
        self.session = [IMYSubGuideSession new];
    }
#ifdef DEBUG
    if (imy_isEmptyString(self.session.sceneKey)) {
        imy_asyncMainBlock(^{
            [UIAlertController imy_quickAlert:@"警告 sceneKey 没传，请联系开发"];
        });
    }
#endif
    
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    [self disableThemeActionToAutoNightMask];
    
    // 顶部背景
    self.topBannerView = [IMYSubGuideVIPBannerView new];
    [self.view addSubview:self.topBannerView];
    
    self.topBGImageView = [UIImageView new];
    self.topBGImageView.frame = self.topBannerView.frame;
    [self.topBGImageView imy_setImage:@"img_topbg_vipcard_v2"];
    [self.view addSubview:self.topBGImageView];
    
    /// 吸顶下的背景色
    self.bigNavBarBGView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 100)];
    [self.bigNavBarBGView imy_setBackgroundColor:kIMY_BG];
    self.bigNavBarBGView.alpha = 0;
    [self.view addSubview:self.bigNavBarBGView];
    
    [self setupNavBar];
    
    [self setupPriceListCell];
    
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.openView];
    [self.openView addSubview:self.openBtn];
    [self.openView addSubview:self.openCheckPrivateView];
    
    [self.view addSubview:self.captionView];
    
    self.popupInfoView = [IMYSubGuidePopupsInfoView new];
    self.popupInfoView.pageEntryType = 1;
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).offset(SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
    }];
    
    [self.openView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
    }];
    
    [self.openBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.openView.mas_top).offset(12);
        make.leading.mas_equalTo(self.openView.mas_leading).offset(12);
        make.bottom.mas_equalTo(self.openView.mas_safeAreaLayoutGuideBottom).offset(-12 - 16);
        make.trailing.mas_equalTo(self.openView.mas_trailing).offset(-12);
        make.height.mas_equalTo(48);
    }];
    
    [self.openCheckPrivateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.openBtn.mas_bottom).offset(8);
        make.leading.mas_equalTo(self.openView.mas_leading).offset(12);
        make.trailing.mas_equalTo(self.openView.mas_trailing).offset(-12);
        make.height.mas_equalTo(16);
    }];
    
    [self.captionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).offset(SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
    }];
    
    [self requestData];
    
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        // 下一个主线程 showUnlock 避免 layout 警告
        [self showUnlock:NO];
    });
    
    RACSignal *uidChangedSignal = [[IMYPublicAppHelper shareAppHelper].useridChangedSignal skip:1];
    RACSignal *rightsChangedSignal = [IMYRightsSDK sharedInstance].loadedSignal;
    [[[[RACSignal merge:@[uidChangedSignal, rightsChangedSignal]] takeUntil:self.rac_willDeallocSignal] throttle:0.2] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self requestData];
    }];
    
    RACSignal *promotionChangedSingle = [[NSNotificationCenter defaultCenter] rac_addObserverForName:KRightsSubGuidePromotionChangedNotification object:nil];
    [[[promotionChangedSingle takeUntil:self.rac_willDeallocSignal] throttle:0.2] subscribeNext:^(IMYSubGuidePricingListItem * const x) {
        @strongify(self);
        // 如果是试用价格包，刷新后会消失，需要重新滚到新锚定区域
        if (x == self.currentPricing && x.real_discount_price.doubleValue <= 0) {
            self.lastSelectedPricingType = 0;
            self.priceListCell.hasChecked = NO;
        }
        [self requestData];
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:KRightsSubGuideRaymentResultNotification object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            NSDictionary *obj = x.object;
            if (obj && [obj isKindOfClass:[NSDictionary class]]) {
                BOOL isSuccess = [obj[@"isSuccess"] boolValue];
                if (isSuccess) {
                    if (self.session.fromVC) {
                        [self.navigationController popToViewController:self.session.fromVC animated:NO];
                    } else {
                        [self imy_pop:NO];
                    }
                } else if ([obj[@"isOfferExpires"] boolValue]) {
                    // 优惠券过期，强制刷新支付半弹窗
                    [self requestData];
                }
            }
        });
    }];
    
    RACSignal *animStartSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Animation_Start object:nil];
    [[[animStartSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        // 有红包动画，显示折扣前的价格
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        [self refreshPayBtnShowText:self.currentPricing.price];
    }];
    
    RACSignal *pngOverSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_AnimationPNG_Over object:nil];
    [[[pngOverSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        NSString *priceText = self.currentPricing.price;
        NSString *discountPriceText = self.currentPricing.real_discount_price;
        if ([priceText imy_isPureInt] && [discountPriceText imy_isPureNumber]) {
            NSInteger priceNum = priceText.integerValue;
            NSInteger discountPriceNum = discountPriceText.integerValue + ([discountPriceText imy_isPureInt] ? 0 : 1);
            if (priceNum > discountPriceNum) {
                // 有价格变动动画，先显示原价
                [self refreshPayBtnShowText:priceText];
                [self resetPayBtnShowTextWithAnimationEnd];
                return;
            }
        }
        // 无价格变动动画
        [self refreshPayBtnShowText:nil];
    }];
    
    RACSignal *animPricingSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Animation_Pricing object:nil];
    [[[animPricingSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        NSString *priceText = x.object;
        [self refreshPayBtnShowText:priceText];
        [self resetPayBtnShowTextWithAnimationEnd];
    }];
    
    RACSignal *pricingChangedSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:K_Noti_SubGuid_RedPacket_Pricing_Changed object:nil];
    [[[pricingChangedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        NSInteger const priceId = [x.userInfo[@"priceId"] integerValue];
        if (priceId != self.currentPricing.id) {
            return;
        }
        [self refreshPayBtnShowText:nil];
    }];
    
    // 触控优先级
    ((IMYSubGuideVipInfoHitView *)self.view).mFirstHitView = self.imy_topLeftButton;
    ((IMYSubGuideVipInfoHitView *)self.view).mSecondHitView = self.topBannerView;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (self.imy_isPop) {
        // 发送支付页面返回通知
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMYVIPPaymentViewGobackNotificationName
                                                            object:nil];
    }
}

- (void)resetPayBtnShowTextWithAnimationEnd {
    @weakify(self);
    // 兜底：价格动画结束后，显示原始文案，1秒限流器
    imy_throttle(1, ^{
        @strongify(self);
        [self refreshPayBtnShowText:nil];
    });
}

- (void)imy_topLeftButtonTouchupInside {
    if ([self needBackPopup]) {
        [self handleBackEvent:nil];
        return;
    }
    
    [super imy_topLeftButtonTouchupInside];
}

- (void)setupNavBar {
    self.navBar = [UIView new];
    self.navBar.imy_width = SCREEN_WIDTH;
    self.navBar.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    [self.view addSubview:self.navBar];
    
    // 标题
    self.navTitleLabel = [UILabel new];
    self.navTitleLabel.font = [UIFont boldSystemFontOfSize:17];
    [self.navTitleLabel imy_setTextColor:kCK_Black_AT];
    self.navTitleLabel.textAlignment = NSTextAlignmentCenter;
    self.navTitleLabel.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.navTitleLabel.imy_width = SCREEN_WIDTH;
    self.navTitleLabel.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    [self.navBar addSubview:self.navTitleLabel];
    
    // 返回按钮
    self.imy_topLeftButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.imy_topLeftButton.imy_left = 16;
    [self imy_topLeftButtonIsBack];
    [self.navBar addSubview:self.imy_topLeftButton];
    
    // 底部分割线
    self.navBarBottomLine = [UIView new];
    self.navBarBottomLine.imy_width = SCREEN_WIDTH;
    self.navBarBottomLine.imy_height = 1.0/SCREEN_SCALE;
    self.navBarBottomLine.imy_bottom = self.navBar.imy_height;
    [self.navBarBottomLine imy_setBackgroundColorForKey:kCK_Black_J];
    self.navBarBottomLine.alpha = 0;
    [self.navBar addSubview:self.navBarBottomLine];
}

#pragma mark - Data

- (void)requestData {
    if (!self.vipInfo) {
        self.topBGImageView.hidden = YES;
        [self.captionView setState:IMYCaptionViewStateLoading];
    }
    
    NSMutableDictionary *options = [NSMutableDictionary dictionary];
    // 来源页面
    options[@"page_key"] = @"PAY";
    // 锚点参数
    NSInteger const sub_type_anchor = self.session.sub_type_anchor;
    if (sub_type_anchor > 0) {
        options[@"sub_type_anchor"] = @(sub_type_anchor);
    }
    // 当前孕周
    NSInteger const currentPregnenyWeek = IMYHIVE_BINDER(IOCAppInfo).currentPregnenyWeek;
    if (currentPregnenyWeek >= 0) {
        options[@"pregnancy_week"] = @(currentPregnenyWeek);
    }
    // 当前最小宝宝出生日
    NSString * const birthdayString = [IMYHIVE_BINDER(IOCAppInfo).currentLatestBabyBirthday imy_getOnlyDateString];
    if (birthdayString.length > 0) {
        options[@"min_baby_birthday"] = birthdayString;
    }
    
    @weakify(self);
    [[IMYSubGuideManager sharedInstance] loadVipInfoWithSceneKey:self.session.sceneKey
                                                         options:options
                                                         success:^(IMYSubGuideVipInfo * _Nonnull vipInfo) {
        imy_asyncMainBlock(^{
            @strongify(self);
            [self handleVipInfo:vipInfo];
            self.title = vipInfo.page_title;
            self.navTitleLabel.text = self.title;
            [self.tableView reloadData];
            [self refreshPayBtnShowText:nil];
            [self.captionView setState:IMYCaptionViewStateHidden];
            // 继续支付
            if (self.isResumePaying) {
                imy_asyncMainBlock(0.1, ^{
                    @strongify(self);
                    [self doPayAction];
                });
            }
            self.isResumePaying = NO;
            // 0.1秒后更新显示的弹窗
            imy_asyncMainBlock(0.1, ^{
                [self updateShowingPopupInfoView];
            });
        });
    } error:^(NSError * _Nonnull error) {
        imy_asyncMainBlock(^{
            @strongify(self);
            if (!self.vipInfo) {
                self.title = IMYString(@"美柚会员");
                self.navTitleLabel.text = self.title;
                [self.captionView setState:IMYCaptionViewStateRetry];
            }
            self.isResumePaying = NO;
        });
    }];
}

- (void)loadPopupsInfoWithRefresh:(BOOL const)isRefresh {
    // 刷新用户选中的价格包
    if (isRefresh || self.popupInfoView.currentPricing.id == self.currentPricing.id) {
        self.popupInfoView.currentPricing = self.currentPricing;
    }
    // 判断是否需要初始化
    if (!isRefresh && !self.popupInfoView.onConfirmCompletedBlock) {
        // 初始化
        @weakify(self);
        self.popupInfoView.onConfirmCompletedBlock = ^(BOOL const hasPopupInfo) {
            @strongify(self);
            if (!hasPopupInfo && self.popupInfoView.userTriggerScene == IMYSGRPopupsTriggerScenePaymentIn) {
                // 执行旧的红包动效
                self.priceListCell.canShowRpAnimation = YES;
                [self.priceListCell handleRpAnimation];
                return;
            }
            // 数据返回弹窗，当前不能立即显示
            if (self.popupInfoView.userTriggerScene != IMYSGRPopupsTriggerScenePaymentIn) {
                return;
            }
            // 显示优惠弹窗
            [self realShowPopupInfoView];
            // 如果当前无价格包，则需要刷新vip_info
            BOOL const containPricingID = [self.popupInfoView.popupValidPriceIds bk_any:^BOOL(NSNumber *pid) {
                for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                    if (pid.integerValue == pricing.id) {
                        return YES;
                    }
                }
                return NO;
            }];
            if (!containPricingID) {
                self.lastSelectedPricingType = 0;
                self.priceListCell.hasChecked = NO;
                [self requestData];
            }
        };
        // 弹窗价格包是否存在
        self.popupInfoView.onShouldConfirmBlock = ^BOOL(NSInteger const popupPricingId) {
            @strongify(self);
            if (popupPricingId > 0) {
                for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                    if (popupPricingId == pricing.id) {
                        return YES;
                    }
                }
            }
            return NO;
        };
        // 弹窗价格包类型存在
        self.popupInfoView.onShouldSubTypeToPriceIdBlock = ^NSInteger(const NSInteger popupSubType) {
            @strongify(self);
            if (popupSubType > 0) {
                for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                    if (popupSubType == pricing.sub_type) {
                        return pricing.id;
                    }
                }
            }
            return 0;
        };
        
        // 弹窗动效结束，刷新接口
        self.popupInfoView.onDismissedBlock = ^(const NSInteger actionType) {
            @strongify(self);
            if (actionType == 2) {
                // 退出当前页面
                [self imy_pop:YES];
            } else {
                // 取消支付弹窗，用户点击继续支付
                if (self.popupInfoView.userTriggerScene == IMYSGRPopupsTriggerScenePaymentCancel && actionType == 0) {
                    // 弹窗价格包 跟 支付的价格包一致
                    if (self.popupInfoView.popupPricingId == self.session.priceId || self.popupInfoView.popupValidPriceIds.firstObject.integerValue == self.session.priceId) {
                        // 刷新后继续支付
                        self.isResumePaying = YES;
                    }
                }
                // 刷新当前页面
                [self requestData];
            }
        };
        // 请求所有弹窗配置
        self.popupInfoView.session = self.session;
        self.popupInfoView.currentPricing = self.currentPricing;
        [self.popupInfoView startLoading];
    }
}

- (void)updateShowingPopupInfoView {
    if (self.popupInfoView.isShowing && self.popupInfoView.popupPricingId == 0) {
        [self updatePopupInfoViewPriceCell];
        [self.popupInfoView updatePricingToAlertView];
    }
}

- (void)realShowPopupInfoView {
    // 领劵成功，需要显示优惠弹窗
    // 如果弹窗优惠卷跟当前选中的价格包不一致，则刷新当前价格选中框
    NSInteger const popupPricingId = self.popupInfoView.popupPricingId;
    if (popupPricingId > 0) {
        IMYSubGuidePricingListItem *currentPricing = nil;
        for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
            if (popupPricingId == pricing.id) {
                currentPricing = pricing;
                break;
            }
        }
        if (self.currentPricing != currentPricing) {
            [self.priceListCell getPriceCellWithPricing:currentPricing isRefresh:YES];
            [self handleSelectedPricing:currentPricing isUserAction:NO];
        }
        // 获取当前价格包Cell，等框架动画结束后 再弹优惠券
        BOOL hasPriceCell = [self updatePopupInfoViewPriceCell];
        if (hasPriceCell) {
            [self.popupInfoView show];
        } else {
            @weakify(self);
            imy_asyncMainBlock(0.3, ^{
                @strongify(self);
                [self updatePopupInfoViewPriceCell];
                [self.popupInfoView show];
            });
        }
    } else {
        // 无指定价格包
        [self updatePopupInfoViewPriceCell];
        [self.popupInfoView show];
    }
}

- (BOOL)updatePopupInfoViewPriceCell {
    // 获取当前价格包Cell
    IMYSubGuideVipPriceCell *cell = nil;
    IMYSubGuidePricingListItem *targetPricing = nil;
    BOOL isRefresh = NO;
    
    // 存在锚定，则直接使用当前价格包
    if (self.popupInfoView.popupPricingId > 0) {
        targetPricing = self.currentPricing;
        isRefresh = YES;
    } else {
        // 无锚定，优先使用锚定的价格包，无才取第一个价格包
        NSArray<NSNumber *> * const popupValidPriceIds = self.popupInfoView.popupValidPriceIds;
        BOOL const containCurrentPricing = [popupValidPriceIds bk_any:^BOOL(NSNumber *pid) {
            return pid.integerValue == self.currentPricing.id;
        }];
        if (containCurrentPricing) {
            targetPricing = self.currentPricing;
        } else {
            NSInteger firstPricingId = popupValidPriceIds.firstObject.integerValue;
            for (IMYSubGuidePricingListItem *pricing in self.vipInfo.pricing_list) {
                if (firstPricingId == pricing.id) {
                    targetPricing = pricing;
                    break;
                }
            }
        }
    }
    
    // 获取目标价格包的Cell
    if (targetPricing != nil) {
        cell = [self.priceListCell getPriceCellWithPricing:targetPricing isRefresh:isRefresh];
    }
    
    // 赋值给红包动画框架
    self.popupInfoView.currentPricing = targetPricing;
    self.popupInfoView.currentPriceCell = cell;
    
    // 获取价格列表视图位置
    UIView * const priceBoxView = self.priceListCell.priceCollectionView;
    self.popupInfoView.currentPriceBoxFrame = [priceBoxView convertRect:priceBoxView.bounds toView:nil];
    
    // 如果价格包View不可见，需要刷新并重新锚定
    return (cell != nil);
}

- (void)handleVipInfo:(IMYSubGuideVipInfo * const)vipInfo {
    self.vipInfo = vipInfo;
    self.session.planId = vipInfo.sub_plan_id;
    
    self.backPopupLimitType  = [IMYSubGuideManager isLimitWithPopup:vipInfo.back_popup];
    
    self.cellModels = [NSMutableArray array];
    
    {
        IMYSubGuideVipInfoCellModel *cellModel = [[IMYSubGuideVipInfoCellModel alloc] init];
        cellModel.vipInfo = vipInfo;
        cellModel.cellType = IMYSubGuideVipInfoCellType_VipRightInfo;
        [self.cellModels addObject:cellModel];
    }
    
    {
        IMYSubGuideVipInfoCellModel *cellModel = [[IMYSubGuideVipInfoCellModel alloc] init];
        cellModel.vipInfo = vipInfo;
        cellModel.cellType = IMYSubGuideVipInfoCellType_PriceList;
        [self.cellModels addObject:cellModel];
    }
    
    for (IMYSubGuideRightsGroupListItem *item in vipInfo.rights_group_list) {
        IMYSubGuideVipInfoCellModel *cellModel = [[IMYSubGuideVipInfoCellModel alloc] init];
        cellModel.vipInfo = vipInfo;
        cellModel.cellType = IMYSubGuideVipInfoCellType_RightsCard;
        if (item.images && item.images.count > 0) {
            if (item.type == 2) {
                cellModel.cellType = IMYSubGuideVipInfoCellType_Image_Box;
            } else {
                cellModel.cellType = IMYSubGuideVipInfoCellType_Images;
            }
        }
        cellModel.rightsItem = item;
        [self.cellModels addObject:cellModel];
    }
    
    {
        IMYSubGuideVipInfoCellModel *cellModel = [[IMYSubGuideVipInfoCellModel alloc] init];
        cellModel.vipInfo = vipInfo;
        cellModel.cellType = IMYSubGuideVipInfoCellType_RestoreVIP;
        [self.cellModels addObject:cellModel];
    }
    
    // [自动锚定价格] 有 checked 选 checked
    IMYSubGuidePricingListItem *currentPricing = nil;
    for (IMYSubGuidePricingListItem *pricing in vipInfo.pricing_list) {
        // 有历史选中价格包，在刷新数据时，需要保留当前价格包选中状态
        if (self.lastSelectedPricingType > 0) {
            if (self.lastSelectedPricingType == pricing.sub_type) {
                currentPricing = pricing;
                break;
            }
        } else if (pricing.checked) {
            currentPricing = pricing;
            break;
        }
    }
    // [自动锚定价格] 没有就选第一个
    if (!currentPricing) {
        currentPricing = [self.vipInfo.pricing_list firstObject];
    }
    
    self.currentPricing = currentPricing;
    self.session.currentPricing = currentPricing;
    self.session.priceId = currentPricing.id;
    self.session.sub_type = currentPricing.sub_type;
    self.lastSelectedPricingType = currentPricing.sub_type;
    
    // 初始化弹窗数据
    [self loadPopupsInfoWithRefresh:NO];
    
    // 刷新顶部banner
    [self.topBannerView refreshWithData:self.vipInfo.carousel_banners];
    self.topBGImageView.hidden = !self.topBannerView.hidden;
    
    CGFloat headerSpaceHeight = 0;
    if (!self.topBannerView.hidden) {
        headerSpaceHeight = IMYIntegerBy375Design(330) - self.navBar.imy_bottom - [IMYSubGuideVipRightInfoCell cellHeight:nil] - 12;
    }
    UIView *headerSpaceView = [UIView new];
    headerSpaceView.frame = CGRectMake(0, 0, SCREEN_WIDTH, headerSpaceHeight);
    self.tableView.tableHeaderView = headerSpaceView;
    
    // 刷新底部
    [self onCurrentPricingDidChanged];
    
    // 预加载返回弹窗图片
    if (self.vipInfo.back_popup.image.length > 0) {
        [[SDWebImageManager sharedManager] prefetchImageWithURL:[NSURL imy_URLWithString:self.vipInfo.back_popup.image]];
    }
}

/// 是否需要页面返回挽回
- (BOOL)needBackPopup {
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核状态下无需弹窗
        return NO;
    }
    // 新增的优惠弹窗返回流程
    if (self.popupGobackStep == 0) {
        if ([self.popupInfoView hasPopupsInfoWithTriggerScene:IMYSGRPopupsTriggerScenePaymentOut]) {
            self.popupGobackStep = 1;
        }
    }
    if (self.popupGobackStep > 0 && self.popupGobackStep < 10) {
        return YES;
    }
    // 旧的返回挽留弹窗
    if (self.backPopupLimitType == IMYSubGuideLimitType_pass ||
        self.backPopupLimitType == IMYSubGuideLimitType_pass_reset ||
        self.backPopupLimitType == IMYSubGuideLimitType_Pass_ignore) {
        return YES;
    }
    return NO;
}

/// 开通按钮的后缀
- (NSString *)buttonSuffixWithPricing:(IMYSubGuidePricingListItem *)pricing
                              vipInfo:(IMYSubGuideVipInfo *)vipInfo {
    NSString *suffix = pricing.sub_btn_text;
    if (imy_isEmptyString(suffix)) {
        suffix = vipInfo.sub_btn_title;
    }
    return suffix;
}

#pragma mark - 拦截返回

- (void)handleBackEvent:(id)sender {
    if (self.popupGobackStep > 0 && self.popupGobackStep < 10) {
        if (self.popupGobackStep == 2) {
            // 弹窗已经显示过，则直接页面退出
            [self imy_pop:YES];
        } else {
            // 执行弹窗显示判断，标记为已显示
            self.popupGobackStep = 2;
            @weakify(self);
            UIWindow *rootView = self.view.window;
            rootView.userInteractionEnabled = NO;
            [self.popupInfoView pageGoBackAction:^(BOOL needShow) {
                @strongify(self);
                rootView.userInteractionEnabled = YES;
                if (!needShow) {
                    // 无popups接口弹窗，再走一次返回按钮事件（老本地老退出弹窗）
                    self.popupGobackStep = 11;
                    [self imy_topLeftButtonTouchupInside];
                } else {
                    [self realShowPopupInfoView];
                }
            }];
        }
        return;
    }
    @weakify(self);
    [[IMYSubGuideBringBackManager sharedInstance] showWithVipInfo:self.vipInfo
                                                          session:self.session
                                                       leftAction:^{
        @strongify(self);
        [self imy_pop:YES];
    } rightAction:^{
        @strongify(self);
        IMYSubGuidePricingListItem *promotionPricingItem = nil;
        if (self.session.currentPricing.countdownTime > 0) {
            promotionPricingItem = self.session.currentPricing;
        } else {
            // 寻找全局第一个价格包Item
            for (IMYSubGuidePricingListItem *item in self.vipInfo.pricing_list) {
                if (item.countdownTime > 0) {
                    promotionPricingItem = item;
                    break;
                }
            }
        }
        // 切换到对应优惠价格包
        if (promotionPricingItem != nil && self.currentPricing != promotionPricingItem) {
            [self.priceListCell getPriceCellWithPricing:promotionPricingItem isRefresh:YES];
            [self handleSelectedPricing:promotionPricingItem isUserAction:NO];
        }
    } closeAction:^{
        
    }];
    
    if (self.backPopupLimitType == IMYSubGuideLimitType_pass) {
        [IMYSubGuideManager addLimitCountWithPopup:self.vipInfo.back_popup];
    } else if (self.backPopupLimitType == IMYSubGuideLimitType_pass_reset) {
        [IMYSubGuideManager resetLimitCountWithPopup:self.vipInfo.back_popup];
    }
    
    // 更改过本地数据，重新获取一下是否拦截返回
    self.backPopupLimitType = [IMYSubGuideManager isLimitWithPopup:self.vipInfo.back_popup];
}

#pragma mark - 解锁组件

- (void)showUnlock:(BOOL)isShow {
    [self.openView.superview layoutIfNeeded];
    [self.openView.superview setNeedsUpdateConstraints];
    
    @weakify(self);
    [UIView animateWithDuration:0.3 animations:^{
        @strongify(self);
        
        if (isShow) {
            [self.openView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.view.mas_leading).offset(0);
                make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
                make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
            }];
            self.openView.alpha = 1;
            [self.openBtn setNeedsRefreshLayoutStyle];
            
            [self.openView.superview layoutIfNeeded];
        } else {
            [self.openView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.view.mas_leading).offset(0);
                make.top.mas_equalTo(self.view.mas_bottom).offset(0);
                make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
            }];
            self.openView.alpha = 0;
            [self.openBtn setNeedsRefreshLayoutStyle];
            
            [self.openView.superview layoutIfNeeded];
        }
    } completion:^(BOOL finished) {
        @strongify(self);
        if (isShow) {
            self.openView.hidden = NO;
            [self refreshPayBtnShowText:nil];
        }
    }];
}

#pragma mark -  PricingList Cell

- (void)handleClickPay {
    // [dy_qrxybzfan]：订阅_确认协议并支付按钮
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict imy_setNonNilObject:@"dy_qrxybzfan" forKey:@"event"];
    [dict imy_setNonNilObject:@2 forKey:@"action"];
    [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
    [dict imy_setNonNilObject:self.currentPricing.getAllGiftIDs forKey:@"subscribe_gift"];
    [dict imy_setNonNilObject:self.currentPricing.real_discount_price forKey:@"subscribe_price"];
    [dict imy_setNonNilObject:@(self.currentPricing.id) forKey:@"sub_price_id"];
    [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey] forKey:@"public_type"];
    [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
    
    [self doPayAction];
}

- (void)doPayAction {
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
        return;
    }
    
    if (![self.priceListCell hasAgreeProto]) {
        IMYSubGuideProtocolReadView *readView = [IMYSubGuideProtocolReadView readViewWithSceneKey:self.session.sceneKey];
        readView.currentPricing = self.currentPricing;
        @weakify(self);
        [readView setAgreeBlock:^{
            @strongify(self);
            [self.priceListCell setAgreeProto:YES];
            self.openCheckPrivateView.checkSeleted = YES;
            [self doPayAction];
        }];
        [readView show];
        return;
    }
    self.session.currentPricing = self.currentPricing;
    self.session.priceId = self.currentPricing.id;
    self.session.sub_type = self.currentPricing.sub_type;
    self.session.currentPricing = self.currentPricing;
    // 普通赠礼id
    NSMutableArray *gift_ids = [NSMutableArray array];
    // 限时促销赠礼id
    NSMutableArray *gift_promotion_ids = [NSMutableArray array];
    // 遍历用户选中的赠礼
    [self.selectedGiftItems bk_each:^(IMYSubGuidePricingGiftInfoListItem *element) {
        if (element.user_promotion_id > 0 && element.hasCountdown) {
            [gift_promotion_ids addObject:@(element.user_promotion_id)];
        } else {
            [gift_ids addObject:@(element.id)];
        }
    }];
    self.session.gift_info = [gift_ids imy_jsonString];
    self.session.gift_promotion_ids = gift_promotion_ids;
    
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        [self doPayShouldLoginAction];
    } else {
        // 存在取消支付优惠弹窗
        if ([self.popupInfoView hasPopupsInfoWithTriggerScene:IMYSGRPopupsTriggerScenePaymentCancel]) {
            // 一定要强持有self，支付过程会把当前 dialog dismiss 掉
            // 这边是故意不用 weak 的
            self.session.onPayUserCancelBlock = ^(UIView<IMYAlertShowViewProtocol> * const payRetView) {
                UIWindow *rootView = self.view.window;
                rootView.userInteractionEnabled = NO;
                [self.popupInfoView payCancelledAction:^(BOOL needShow) {
                    rootView.userInteractionEnabled = YES;
                    if (!needShow) {
                        [payRetView show];
                    } else {
                        [self realShowPopupInfoView];
                    }
                }];
            };
        }
        
        // 开始支付
        [[IMYSubGuideManager sharedInstance] payWithSession:self.session];
    }
}

- (void)doPayShouldLoginAction {
    // 防止登录页面多次回调
    __block BOOL isRunnning = NO;
    @weakify(self);
    void (^loginFinishedBlock)(UIViewController *) = ^(UIViewController *loginVC) {
        [loginVC dismissViewControllerAnimated:YES completion:^{
            @strongify(self);
            if (isRunnning) {
                return;
            }
            isRunnning = YES;
            // 判断当前页面是否被释放
            if (!self.imy_isShowVC || !self.view.window) {
                // 页面被退出，啥也不用干
            } else {
                if (IMYRightsSDK.sharedInstance.currentRightsType != IMYRightsTypeNone) {
                    // 把业务页面全部回到首页
                    [self.imy_navigationController popToRootViewControllerAnimated:NO];
                    // 先切到首页
                    IMYHIVE_BINDER(IOCAppMainTabVC).selectedTabIndexType = SYTabBarIndexTypeHome;
                    // 再切到会员tab
                    [[IMYURIManager sharedInstance] runActionWithPath:@"myrights/home" params:nil info:nil];
                } else {
                    // 无会员，继续支付流程
                    // 保留当前页面即可
                }
            }
        }];
    };
    NSDictionary *loginMap = @{
        @"finishedBlock" : loginFinishedBlock,
    };
    // 未登录，唤起登录VC
    [[IMYURIManager sharedInstance] runActionWithPath:@"login" params:loginMap info:nil];
}

- (void)handleSelectedPricing:(IMYSubGuidePricingListItem *)currentPricing isUserAction:(BOOL)isUserAction {
    self.currentPricing = currentPricing;
    self.session.currentPricing = currentPricing;
    self.session.priceId = currentPricing.id;
    self.session.sub_type = currentPricing.sub_type;
    self.lastSelectedPricingType = currentPricing.sub_type;
    
    // 快速领劵
    if (isUserAction) {
        [self loadPopupsInfoWithRefresh:YES];
    }
    
    [self onCurrentPricingDidChanged];
    
    [self.tableView performBatchUpdates:^{
        
    } completion:^(BOOL finished) {
        
    }];
    
    [self refreshPayBtnShowText:nil];
}

- (void)onCurrentPricingDidChanged {
    [self.openCheckPrivateView refreshWithType:self.currentPricing.type];
    CGSize checkPrivateSize = self.openCheckPrivateView.imy_size;
    [self.openCheckPrivateView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(checkPrivateSize.height);
    }];
    
    if (self.currentPricing.countdownTime > 0) {
        @weakify(self);
        self.openCountdownView.onTimeChangeBlock = ^NSInteger{
            @strongify(self);
            return self.currentPricing.countdownTime;
        };
        self.openCountdownView.onTimerStopBlock = ^{
            @strongify(self);
            [self onCurrentPricingDidChanged];
        };
        self.openCountdownView.frame = CGRectMake(0, -33, SCREEN_WIDTH, 34);
        [self.openCountdownView startTimer];
        [self.openView addSubview:self.openCountdownView];
        [self.openView setExtendTouchInsets:UIEdgeInsetsMake(34, 0, 0, 0)];
        self.tableView.tableFooterView.imy_height = 8 + 12 + 48 + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN + 34;
    } else {
        [self.openCountdownView stopTimer];
        [self.openCountdownView removeFromSuperview];
        [self.openView setExtendTouchInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
        self.tableView.tableFooterView.imy_height = 8 + 12 + 48 + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    }
}

/// 刷新支付按钮文案
- (void)refreshPayBtnShowText:(NSString *)showPriceText {
    // ¥x 立即开通
    NSString *suffix = self.currentPricing.sub_btn_text;
    if (imy_isEmptyString(suffix)) {
        suffix = self.vipInfo.sub_btn_title;
    }
    if (!showPriceText) {
        showPriceText = self.currentPricing.real_discount_price;
    }
    
    // 优惠金额 = MAX(0, 原价 - 当前折扣价) + 赠礼价值
    __block CGFloat allWorth = MAX(0, [self.currentPricing.price doubleValue] - [self.currentPricing.real_discount_price doubleValue]);
    [self.selectedGiftItems enumerateObjectsUsingBlock:^(IMYSubGuidePricingGiftInfoListItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        allWorth += obj.worth.doubleValue;
    }];
    NSString *allWorthText = [NSString imy_getPriceNoneString:allWorth];
    
    NSString *btnTitle = [NSString stringWithFormat:@"¥%@%@", showPriceText, suffix];
    btnTitle = [btnTitle stringByReplacingOccurrencesOfString:@"{y}" withString:allWorthText];
    [self.payBtn setTitle:btnTitle forState:UIControlStateNormal];
    [self.openBtn setTitle:btnTitle forState:UIControlStateNormal];
    
    // 对括号内的字体变小
    NSCharacterSet *beginCharSet = [NSCharacterSet characterSetWithCharactersInString:@"(（"];
    NSCharacterSet *endCharSet = [NSCharacterSet characterSetWithCharactersInString:@")）"];
    
    NSRange regularBegin = [btnTitle rangeOfCharacterFromSet:beginCharSet];
    NSRange regularEnd = [btnTitle rangeOfCharacterFromSet:endCharSet options:NSBackwardsSearch];
    
    if (regularBegin.location != NSNotFound &&
        regularEnd.location != NSNotFound &&
        regularEnd.location > regularBegin.location) {
        // 设置括号内的小字体
        NSMutableAttributedString *atts = [[NSMutableAttributedString alloc] initWithString:btnTitle];
        [atts addAttributes:@{
            NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightRegular],
        } range:NSMakeRange(regularBegin.location, regularEnd.location - regularBegin.location + 1)];
        
        self.payBtn.titleLabel.attributedText = atts;
        self.payBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        
        self.openBtn.titleLabel.attributedText = atts;
        self.openBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    }
}

#pragma mark - UITableViewDelegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.cellModels.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYSubGuideVipInfoCellModel *cellModel = self.cellModels[indexPath.row];
    switch (cellModel.cellType) {
        case IMYSubGuideVipInfoCellType_VipRightInfo: {
            return [IMYSubGuideVipRightInfoCell cellHeight:cellModel];
        } break;

        case IMYSubGuideVipInfoCellType_PriceList: {
            return [self.priceListCell cellHeight:cellModel
                                   currentPricing:self.currentPricing
                                         sceneKey:self.session.sceneKey];
        } break;

        case IMYSubGuideVipInfoCellType_RightsCard: {
            return [IMYSubGuideVipRightsCardCell cellHeight:cellModel];
        } break;
            
        case IMYSubGuideVipInfoCellType_Images: {
            return [IMYSubGuideImagesV2Cell cellHeight:cellModel];
        } break;
            
        case IMYSubGuideVipInfoCellType_Image_Box: {
            return [IMYSubGuideImageBoxCell cellHeight:cellModel.rightsItem];
        } break;
            
        case IMYSubGuideVipInfoCellType_RestoreVIP: {
            return 48;
        } break;
            
        default: {
            return 100;
        } break;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @weakify(self);
    
    IMYSubGuideVipInfoCellModel *cellModel = self.cellModels[indexPath.row];
    
    switch (cellModel.cellType) {
        case IMYSubGuideVipInfoCellType_VipRightInfo: {
            IMYSubGuideVipRightInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSubGuideVipRightInfoCell"
                                                                                forIndexPath:indexPath];
            
            // 存在 top banner 则需要隐藏用户信息Cell
            cell.contentView.hidden = !self.topBannerView.hidden;

            // 点击用户信息，未登录的要登录
            [cell setClickUserInfoBlock:^{
                NSLog(@"点击用户信息");
                if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
                    // 点击埋点
                    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
                        @"action" : @2,
                        @"position" : @148,
                        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                        @"floor" : @1,
                        @"info_tag" : @"会员卡",
                        @"index" : @0,
                    } headers:nil completed:nil];
                    // 换肤登录页面
                    [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                    return;
                }
            }];

            [cell setCellModel:cellModel];

            return cell;
        } break;

        case IMYSubGuideVipInfoCellType_PriceList: {
            [self.priceListCell setCellModel:cellModel
                              currentPricing:self.currentPricing
                                    sceneKey:self.session.sceneKey];
            
            self.payBtn = self.priceListCell.payBtn;
            
            return self.priceListCell;
        } break;

        case IMYSubGuideVipInfoCellType_RightsCard: {
            IMYSubGuideVipRightsCardCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSubGuideVipRightsCardCell"
                                                                                 forIndexPath:indexPath];
            
            [cell setCellModel:cellModel];

            return cell;
        } break;
            
        case IMYSubGuideVipInfoCellType_Images: {
            IMYSubGuideImagesV2Cell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSubGuideImagesV2Cell"
                                                                            forIndexPath:indexPath];
            
            cell.currentPricing = self.currentPricing;
            [cell setCellModel:cellModel floor:indexPath.row];

            return cell;
        } break;
            
        case IMYSubGuideVipInfoCellType_Image_Box: {
            IMYSubGuideImageBoxCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSubGuideImageBoxCell"
                                                                            forIndexPath:indexPath];
            cell.bi_floor = indexPath.row;
            cell.bi_position = 148;
            cell.bi_key = [NSString stringWithFormat:@"%p", self];
            [cell setupWithData:cellModel.rightsItem];
            
            return cell;
        } break;
            
        case IMYSubGuideVipInfoCellType_RestoreVIP: {
            static NSString *identifier = @"IMYSubGuideRestoreVIPCell";
            IMYSubGuideRestoreVIPCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier forIndexPath:indexPath];
            
            @weakify(self);
            [cell setButtonClickBlock:^{
                @strongify(self);
                [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/restore/page" params:@{
                    @"SubGuideSession" : self.session
                } info:nil];
            }];
            
            return cell;
        } break;
            
        default: {
            UITableViewCell *cell = [UITableViewCell new];
            NSAssert(NO, @"vip info vc 还未实现的Cell %@", cellModel);
            return cell;
        } break;
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYSubGuideVipInfoCellModel *cellModel = self.cellModels[indexPath.row];
    if (cellModel.cellType == IMYSubGuideVipInfoCellType_RightsCard &&
        imy_isNotEmptyString(cellModel.rightsItem.url)) {
        [[IMYURIManager shareURIManager] runActionWithString:cellModel.rightsItem.url];
    } else if (cellModel.cellType == IMYSubGuideVipInfoCellType_RestoreVIP) {
        [[IMYSubGuideManager sharedInstance] restoreWithSession:self.session];
    }
}

/// 初始化价格卡片列表
- (void)setupPriceListCell  {
    // 无需复用，不用由 UITableView 来初始化
    _priceListCell = [[IMYSubGuideVipPriceListCell alloc] initWithStyle:UITableViewCellStyleDefault
                                                        reuseIdentifier:@"IMYSubGuideVipPriceListCell"];
    
    @weakify(self);
    [_priceListCell setClickPriceBlock:^(IMYSubGuidePricingListItem * _Nonnull currentItem) {
        @strongify(self);
        [self handleSelectedPricing:currentItem isUserAction:YES];
    }];
    
    [_priceListCell setClickPayBlock:^{
        @strongify(self);
        [self handleClickPay];
    }];
    
    [_priceListCell setOnGiftItemsSelectedBlock:^(NSArray<IMYSubGuidePricingGiftInfoListItem *> * _Nonnull selectedItems) {
        @strongify(self);
        self.selectedGiftItems = selectedItems;
        [self refreshPayBtnShowText:nil];
    }];
    
    _priceListCell.onAgreeProtoDidChangedBlock = ^(BOOL isAgree) {
        @strongify(self);
        self.openCheckPrivateView.checkSeleted = isAgree;
    };
    
    // [dy_qrxybzfan]：订阅_确认协议并支付按钮
    _priceListCell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"dy_qrxybzfan-%p", self];
    [_priceListCell.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        [dict imy_setNonNilObject:@"dy_qrxybzfan" forKey:@"event"];
        [dict imy_setNonNilObject:@1 forKey:@"action"];
        [dict imy_setNonNilObject:@([IMYRightsSDK sharedInstance].currentSubscribeType) forKey:@"subscribe_type"];
        [dict imy_setNonNilObject:self.currentPricing.getAllGiftIDs forKey:@"subscribe_gift"];
        [dict imy_setNonNilObject:self.currentPricing.real_discount_price forKey:@"subscribe_price"];
        [dict imy_setNonNilObject:@(self.currentPricing.id) forKey:@"sub_price_id"];
        [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:self.session.sceneKey] forKey:@"public_type"];
        [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
        [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
    }];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 当前滚动位置
    CGFloat offsetY = scrollView.contentOffset.y;
    
    if (!self.topBannerView.hidden && offsetY < 0) {
        // 有 banner view 的时候，不允许下拉
        scrollView.contentOffset = CGPointZero;
        return;
    }
    
    // 吸顶状态下的背景渐变
    CGFloat topUserInfoY = [IMYSubGuideVipRightInfoCell cellHeight:nil];
    CGFloat topTabOffsetY = topUserInfoY + 162;
    if (offsetY > topTabOffsetY) {
        // 不做渐变了
        self.bigNavBarBGView.alpha = 1;
        self.topBGImageView.alpha = 0;
        self.navBarBottomLine.alpha = 1;
    } else {
        if (offsetY > topUserInfoY) {
            CGFloat diffY = offsetY - topUserInfoY;
            CGFloat diffRatio = diffY / (topTabOffsetY - topUserInfoY);
            self.topBGImageView.alpha = 1 - diffRatio;
        } else {
            self.topBGImageView.alpha = 1;
        }
        self.navBarBottomLine.alpha = 0;
        self.bigNavBarBGView.alpha = 0;
    }
    
    if (offsetY > self.topBGImageView.imy_height) {
        self.topBGImageView.imy_top = -self.topBGImageView.imy_height;
    } else if (offsetY < 0) {
        self.topBGImageView.imy_top = 0;
    } else {
        self.topBGImageView.imy_top = -offsetY;
    }
    self.topBannerView.imy_top = self.topBGImageView.imy_top;
    self.topBannerView.alpha = self.topBGImageView.alpha;
    
    if (self.payBtn) {
        // 按钮消失时机调整为，支付按钮和导航栏相交
        CGRect rect = [self.payBtn.superview convertRect:CGRectMake(0, self.payBtn.imy_top, SCREEN_WIDTH, 1) toView:[UIApplication sharedApplication].delegate.window];
        CGRect visiableRect = CGRectMake(0, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        
        if (CGRectIntersectsRect(rect, visiableRect)) {
            // 可见
            [self showUnlock:NO];
        } else {
            // 不可见
            [self showUnlock:YES];
        }
    }
}

#pragma mark - Get

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        [_tableView imy_setBackgroundColorForKey:kIMYClearColorKey];
        
        [_tableView registerClass:[IMYSubGuideVipRightInfoCell class] forCellReuseIdentifier:@"IMYSubGuideVipRightInfoCell"];
        [_tableView registerClass:[IMYSubGuideVipPriceListCell class] forCellReuseIdentifier:@"IMYSubGuideVipPriceListCell"];
        [_tableView registerClass:[IMYSubGuideVipRightsCardCell class] forCellReuseIdentifier:@"IMYSubGuideVipRightsCardCell"];
        [_tableView registerClass:[IMYSubGuideImagesV2Cell class] forCellReuseIdentifier:@"IMYSubGuideImagesV2Cell"];
        [_tableView registerClass:[IMYSubGuideImageBoxCell class] forCellReuseIdentifier:@"IMYSubGuideImageBoxCell"];
        [_tableView registerClass:[IMYSubGuideRestoreVIPCell class] forCellReuseIdentifier:@"IMYSubGuideRestoreVIPCell"];
        
        _tableView.tableHeaderView = [UIView new];
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 8 + 12 + 48 + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    }
    return _tableView;
}

- (IMYTouchEXView *)openView {
    if (!_openView) {
        _openView = [IMYTouchEXView new];
        [_openView setExtendTouchInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
        [_openView imy_setBackgroundColor:kCK_White_AN];
        [_openView imy_showLineForDirection:IMYDirectionUp];
    }
    return _openView;
}

- (IMYCapsuleButton *)openBtn {
    if (!_openBtn) {
        _openBtn = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(12, 12, SCREEN_WIDTH - 24, 48)];
        _openBtn.type = IMYButtonTypeFillLightRed;
        _openBtn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        [_openBtn setTitle:IMYString(@"立即开通") forState:UIControlStateNormal];
        [_openBtn addTarget:self action:@selector(handleClickPay) forControlEvents:UIControlEventTouchUpInside];
        [_openBtn imy_drawAllCornerRadius:24];
        
        _openBtn.titleAtDirection = IMYDirectionCenterX | IMYDirectionCenterY;
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _openBtn.bounds;
        [_openBtn.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _openBtn;
}

- (IMYSubGuideCheckPrivateView *)openCheckPrivateView {
    if (!_openCheckPrivateView) {
        _openCheckPrivateView = [[IMYSubGuideCheckPrivateView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 0)];
        _openCheckPrivateView.checkSeleted = NO;
        @weakify(self);
        [_openCheckPrivateView setDidChangedCheckState:^(BOOL seleted) {
            @strongify(self);
            [self.priceListCell setAgreeProto:seleted];
        }];
    }
    return _openCheckPrivateView;
}

- (IMYSubGuideCountdownView *)openCountdownView {
    if (!_openCountdownView) {
        _openCountdownView = [IMYSubGuideCountdownView new];
    }
    return _openCountdownView;
}

- (IMYCaptionViewV2 *)captionView {
    if (!_captionView) {
        _captionView = [[IMYCaptionViewV2 alloc] initWithFrame:self.view.bounds];
        [_captionView setState:IMYCaptionViewStateLoading];
        @weakify(self);
        [_captionView setRetryBlock:^{
            @strongify(self);
            [self requestData];
        }];
    }
    return _captionView;
}

@end
