//
//  IMYMeURIRegister.m
//  IMYMe
//
//  Created by 施东苗 on 2021/7/28.
//

#import "IMYMeURIRegister.h"
#import <IMYBaseKit/IMYPublic.h>
#import <IMYBaseKit/IMY_ViewKit.h>
#import <IMYBaseKit/IMYVKWebViewController+IMYPrivate.h>
#import <IMYAccount/IMYAccount.h>
#import <IMYAccount/IMYAccountUnionLoginVC.h>
#import <IMYAccount/IMYAccountOneKeyLoginService.h>
#import <IMYAccount/IMYAccountHelper.h>
#import <IMYAccount/IMYAccountMailLoginVC.h>
#import <IMYAccount/IMYAccountProtocolReadView.h>
#import <IMYAccount/IMYAccountServerURL.h>

#import "IMYMeGlobalMacros.h"
#import "SYUserWelcomeViewControllerProtocol.h"
#import "IMYMeUserWelcomeViewController.h"
#import "IMYMeAccountVC.h"
#import "SYUserInfoVC_V2.h"
#import "SYUserHelper.h"
#import "SYUserModeChangeAction.h"
#import "SYModeDetailViewController.h"
#import "IMYMeLoginManager.h"
#import "SYAddressVC.h"
#import "SYUnregistComfirmVC.h"

@implementation IMYMeURIRegister

IMY_KYLIN_FUNC(IMY_KYLIN_STAGE_PREMAIN, 2, IMY_KYLIN_QUEUE_ASYNC) {
    [IMYMeURIRegister registerUserAction];
}

+ (Class)welcomeAccountPageClass {
    // 新的登录流程
    // https://www.tapd.cn/********/prong/stories/view/11********001087781
    IMYUnionLoginModel *latest = [IMYAccountUnionLoginService getLatestLoginModel];
    IMYUnionLoginModel *other = [IMYAccountUnionLoginService getOtherLoginModel];
    if (latest) {
        // 已经获取到移动SDK号码的情况下，进行精确判断
        NSString *currentPhoneNumber = [IMYAccountOneKeyLoginService currentPhoneNumber];
        if (currentPhoneNumber.length > 0) {
            BOOL inMobileType = (latest.loginType == IMYAccountLoginTypeSMS || latest.loginType == IMYAccountLoginTypeMobile);
            if (inMobileType && [[IMYAccountHelper vagueMobile:latest.account] isEqualToString:currentPhoneNumber]) {
                return IMYMeAccountVC.class;
            } else {
                return IMYMeUserWelcomeViewController.class;
            }
        } else {
            // 如果移动SDK未取到号，一律先跳联合登录页
            return IMYMeUserWelcomeViewController.class;
        }
    } else if (other) {
        return IMYMeUserWelcomeViewController.class;
    } else {
        return IMYMeAccountVC.class;
    }
}

+ (void)registerUserAction {
    
    /// 获取登录VC的类名
    [[IMYURIManager shareURIManager] addForPath:@"login/class/name"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        Class loginClass = [IMYMeURIRegister welcomeAccountPageClass];
        NSString *className = NSStringFromClass(loginClass) ?: @"IMYMeAccountVC";
        [actionObject callbackWithObject:className];
    }];
    
    /// 执行登录协议
    [[IMYURIManager shareURIManager] addForPath:@"login"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYVKWebViewController *webVC = (id)actionObject.getUsingViewController;
        UINavigationController *nav = webVC.navigationController;
        id params = actionObject.uri.params;
        if ([[params valueForKey:@"closepage"] boolValue]) {
            [nav imy_pop:NO];
        }
        BOOL close_web_when_no_login = [actionObject.uri.params[@"close_web_when_no_login"] boolValue];
        
        IMYMeAccountVC *loginVC = [[IMYMeURIRegister welcomeAccountPageClass] new];
        
        id finishBlock = [params valueForKey:@"finishedBlock"];
        if (finishBlock) {
            NSInteger argsNumber = [IMYThrottle getNumberOfArgumentsWithBlock:finishBlock];
            if (argsNumber == 2) {
                // 兼容老业务代码 传不同参数的Block进来
                void(^actionBlock)(UIViewController *) = finishBlock;
                loginVC.finishedBlock = actionBlock;
            } else {
                loginVC.finishedBlock = ^(UIViewController *loginSycml) {
                    void(^actionBlock)(void) = finishBlock;
                    actionBlock();
                };
            }
        } else {
            BOOL no_login_toast = [actionObject.uri.params[@"no_login_toast"] boolValue];
            if (!no_login_toast) {
                NSString *loginPrompt = [params valueForKey:@"LoginPromptString"] ?: LoginPromptString;
                [UIView imy_showTextHUD:loginPrompt];
            }
        }
        
        [loginVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"goto", nil];
        if (close_web_when_no_login) {
            @weakify(nav);
            loginVC.tapBackBlock = ^{
                [nav_weak_ imy_pop:NO];
            };
        }
        loginVC.fromURI = actionObject.uri;
        [nav imy_present:loginVC];
        
        [IMYEventHelper event:@"tc-dlcz"];
        
        NSString *gotoUri = actionObject.uri.params[@"goto"];
        BOOL noreload = [actionObject.uri.params[@"noreload"] boolValue];
        
        IMYVKWebView *webView = actionObject.webView;
        @weakify(webVC, webView);
        if (webView != nil) {
            [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:LoginSucceedNotification object:nil] throttle:1] take:1] subscribeNext:^(id x) {
                @strongify(webVC, webView);
                if (noreload) {
                    [webView sendEvent:@"loginEvent" params:@{@"userid": [IMYPublicAppHelper shareAppHelper].userid ?: @""}];
                } else {
                    if ([webVC isKindOfClass:[IMYVKWebViewController class]]) {
                        [(IMYVKWebViewController *)webVC reloadURLString];
                    } else if ([webVC respondsToSelector:@selector(reloadURLString)]) {
                        [webVC reloadURLString];
                    } else {
                        [webView reload];
                    }
                }
                if (gotoUri) {
                    [[IMYURIManager shareURIManager] runActionWithString:gotoUri];
                }
            }];
        }
    }];
    
    /// 邮箱登录
    [[IMYURIManager shareURIManager] addForPath:@"login/email" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *email = actionObject.uri.params[@"email"];
        NSEnumerator *lastVCs = actionObject.getUsingViewController.navigationController.viewControllers.reverseObjectEnumerator;
        IMYAccountSessionConfigService *lastSessionConfig = nil;
        for (IMYAccountVC *accountVC in lastVCs) {
            if ([accountVC isKindOfClass:IMYAccountVC.class] ||
                [accountVC isKindOfClass:IMYAccountUnionLoginVC.class]) {
                lastSessionConfig = accountVC.sessionConfig;
                break;
            }
        }
        IMYAccountSessionConfigService *sessionConfig = [lastSessionConfig copy];
        if (!sessionConfig) {
            sessionConfig = [IMYMeAccountVC sessionConfig];
        }
        sessionConfig.didCheckPrivate = NO;
        sessionConfig.recentLoginType = IMYAccountLoginTypeEmail;
        sessionConfig.recentEmail = email;
        
        IMYAccountMailLoginVC *vc = [[IMYAccountMailLoginVC alloc] init];
        vc.sessionConfig = sessionConfig;
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    /// 手机登录
    [[IMYURIManager shareURIManager] addForPath:@"login/phone" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *mobile = actionObject.uri.params[@"phone"];
        mobile = [CocoaSecurity aesDecryptWithBase64:mobile key:kEncrytKey].utf8String ?: mobile;
        
        NSString *nationCode = actionObject.uri.params[@"nationCode"];
        if ([nationCode hasPrefix:@"+"]) {
            // 移除外部传入的+号
            nationCode = [nationCode substringFromIndex:1];
        }
        NSEnumerator *lastVCs = actionObject.getUsingViewController.navigationController.viewControllers.reverseObjectEnumerator;
        IMYAccountSessionConfigService *lastSessionConfig = nil;
        for (IMYAccountVC *accountVC in lastVCs) {
            if ([accountVC isKindOfClass:IMYAccountVC.class] ||
                [accountVC isKindOfClass:IMYAccountUnionLoginVC.class]) {
                lastSessionConfig = accountVC.sessionConfig;
                break;
            }
        }
        IMYAccountVC *vc = [[IMYMeAccountVC alloc] init];
        // 当前手机号跟流量号相同，则直接使用手机号一键登录
        NSString *currentPhoneNumber = [IMYAccountOneKeyLoginService currentPhoneNumber];
        vc.sessionConfig.onekeyLoginEnable = [[IMYAccountHelper vagueMobile:mobile] isEqualToString:currentPhoneNumber];
        if ([mobile imy_isPureInt]) {
            vc.sessionConfig.recentPhoneNumber = mobile;
            vc.sessionConfig.recentPhoneNationCode = nationCode;
        }
        vc.sessionConfig.completion = lastSessionConfig.completion;
        vc.sessionConfig.failedHandler = lastSessionConfig.failedHandler;
        vc.sessionConfig.didCheckPrivate = NO;
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    /// 第三方登录
    [[IMYURIManager shareURIManager] addForPath:@"login/thirdParty" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        const IMYAccountServerPlatform type = [actionObject.uri.params[@"type"] integerValue];
        if (type != IMYAccountServerPlatformQQ &&
            type != IMYAccountServerPlatformWeibo &&
            type != IMYAccountServerPlatformWeixin &&
            type != IMYAccountServerPlatformAppleID) {
            return;
        }
        NSEnumerator *lastVCs = actionObject.getUsingViewController.navigationController.viewControllers.reverseObjectEnumerator;
        IMYAccountSessionConfigService *lastSessionConfig = nil;
        for (IMYAccountVC *accountVC in lastVCs) {
            if ([accountVC isKindOfClass:IMYAccountVC.class] ||
                [accountVC isKindOfClass:IMYAccountUnionLoginVC.class]) {
                lastSessionConfig = accountVC.sessionConfig;
                break;
            }
        }
        IMYAccountSessionConfigService *sessionConfig = [lastSessionConfig copy];
        if (!sessionConfig) {
            sessionConfig = [IMYMeAccountVC sessionConfig];
        }
        // 弹窗确认用户授权
        IMYAccountProtocolReadView *readView = [IMYAccountProtocolReadView readViewWithType:IMYAccountProtocolReadType_Normal];
        [readView setAgreeBlock:^{
            sessionConfig.didCheckPrivate = YES;
            switch (type) {
                case IMYAccountServerPlatformQQ: {
                    [IMYAccountAuthService authByType:IMYShareSDKTypeQzone sessionConfig:sessionConfig];
                } break;
                case IMYAccountServerPlatformWeixin: {
                    [IMYAccountAuthService authByType:IMYShareSDKTypeWeChatTimeline sessionConfig:sessionConfig];
                } break;
                case IMYAccountServerPlatformWeibo: {
                    [IMYAccountAuthService authByType:IMYShareSDKTypeWeibo sessionConfig:sessionConfig];
                } break;
                case IMYAccountServerPlatformAppleID: {
                    [IMYAccountAppleIDService loginWithSessionConfig:sessionConfig];
                } break;
                default:
                    break;
            }
        }];
        [readView show];
    }];
    
    //通用的登录样式，”手机号+验证码“模式
    [[IMYURIManager shareURIManager] addForPath:@"generalLogin" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYMeAccountVC *vc = [[IMYMeAccountVC alloc] init];
        vc.sessionConfig.needUnionLogin = NO;
        vc.sessionConfig.onekeyLoginEnable = NO;
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_present:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"tools/baobaoji/login"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYMeAccountVC *loginVC = [[IMYMeURIRegister welcomeAccountPageClass] new];
        NSString *fictitious_uid = [IMYPublicAppHelper shareAppHelper].userid;
        @weakify(loginVC);
        loginVC.finishedBlock = ^(UIViewController *loginSycml) {
            @strongify(loginVC);
            [loginVC dismissViewControllerAnimated:YES completion:^{
                NSString *userid = [IMYPublicAppHelper shareAppHelper].userid;
                NSMutableDictionary *res = [NSMutableDictionary dictionary];
                BOOL uidChange = ![fictitious_uid isEqualToString:userid]; // 用户id是否发生变化
                [res setObject:@(uidChange) forKey:@"uidChange"];
                [res setObject:@(YES) forKey:@"lamaBBJEntrance"]; // 是否存在用户进入宝宝记实验
                if (actionObject.implCallbackBlock) {
                    actionObject.implCallbackBlock(res, nil, nil);
                }
            }];
        };
        loginVC.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_present:loginVC];
        [UIView imy_showTextHUD:LoginPromptString];
        [IMYEventHelper event:@"tc-dlcz"];
    }];
    
    static SYUserInfoVC_V2 *userInfoVC_V2 = nil;
    [[IMYURIManager shareURIManager] addForPath:@"user/loginout" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        SYUserInfoVC_V2 *vc = [[SYUserInfoVC_V2 alloc] init];
        LoginoutBackBlock block = actionObject.uri.params[@"loginoutBlock"];
        if (block) {
            userInfoVC_V2 = vc;
            vc.loginoutBlock = ^(BOOL success, NSError *error) {
                userInfoVC_V2 = nil;
                !block ?: block(success,error);
            };
        }
        BOOL isConfirmed = [actionObject.uri.params[@"isConfirmed"] boolValue];
        if (isConfirmed) {
            [vc isConfirmedAndLoginout];
        } else {
            [vc loginout];
        }
    }];
    
    //与 user/loginout 区分，不创建vc来退出账号
    [[IMYURIManager shareURIManager] addForPath:@"user/loginout_v2" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYMeLoginManager logout:NULL];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"user/info"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        SYUserInfoVC_V2 *vc = [[SYUserInfoVC_V2 alloc] init];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params filter:@"popToBefore", nil];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"user/mode"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        id mode = actionObject.uri.params[@"modeType"];
        BOOL changeMode = [actionObject.uri.params[@"changeMode"] boolValue];
        IMYPublicBaseViewController *vc = nil;
        if (changeMode) {
            vc = [NSClassFromString(@"SYUserModeSelectViewController") new];
            [vc setValue:@YES forKey:@"changeMode"];
            [vc setValue:mode forKey:@"toMode"];
            NSNumber *position = actionObject.uri.params[@"position"];
            if (position) {
                [vc setValue:position forKeyPath:@"viewModel.position"];
            }
        } else if (mode) {
            if ([mode integerValue] == IMYVKUserModePregnancy) {
                vc = [NSClassFromString(@"SYModeConfirmViewController") new];
            } else {
                vc = [NSClassFromString(@"SYModeDetailViewController") new];
            }
        } else {
            vc = [NSClassFromString(@"SYUserInfoVC_V2") new];
        }
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    
    [[IMYURIManager shareURIManager] addForPath:@"user/birthday"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *birthday = actionObject.uri.params[@"birthday"];
        if (!imy_isBlankString(birthday)) {
            [SYUserHelper sharedHelper].birthday = birthday;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"user/nickname"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYPublicBaseViewController *vc = [NSClassFromString(@"SYEditNicknameVC") new];
        vc.fromURI = actionObject.uri;
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    
    // 孕期卡片首页 -> 宝宝出生了
    [[IMYURIManager shareURIManager] addForPath:@"pregnancyHomeCard/babyBorn"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        __block IMYVKUserMode userMode = IMYVKUserModeLama;
        IMYVKUserMode lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
        [SYUserModeChangeAction checkPregnancyToLamaMode:^(BOOL result, BOOL needAdd, NSArray *deleteBabyList) {
            if (!result) {
                return ;
            }
            ///< 1. 不需要添加宝宝的直接原地切换身份
            if (!needAdd) {
                ///< 2.无宝宝的直接切换为经期身份
                if ([IMYPublicAppHelper shareAppHelper].babyCount == 0) {
                    userMode = IMYVKUserModeNormal;
                    imy_asyncMainBlock(0.3, ^{
                        [UIWindow imy_showTextHUD:IMYString(@"当前无宝宝，已切至经期身份")];
                    });
                }
                if (userMode == IMYVKUserModeLama) {
                    [SYUserModeSelectViewController gotoLamaSetVC];
                }
                SYUserModeViewModel *viewModel = [[SYUserModeViewModel alloc] init];
                [viewModel changeToMode:userMode];
                [IMYGAEventHelper postWithPath:@"bi_mode"
                                        params:@{ @"mode": @(userMode),
                                                  @"exmode": @(lastMode),
                                                  @"position": @(6)
                                               }
                                       headers:nil
                                     completed:nil];
                return ;
            }
            /// 3.跳转到设置身份详情页面
            SYUserModeViewModel *viewModel = [[SYUserModeViewModel alloc] init];
            viewModel.position = 6;
            viewModel.useLastModeForBi = YES;
            viewModel.lastModeForBi = lastMode;
            SYModeDetailViewController *vc = [[SYModeDetailViewController alloc] initWithUserModeViewModel:viewModel prepareMode:IMYVKUserModeLama];
            vc.needBabyFirstResponse = YES;
            vc.fromChange = YES;
            [actionObject.getUsingViewController imy_push:vc];
        }];
    }];
    
    // 孕期卡片首页 -> 宝宝出生了->直接跳转到设置页面
    [[IMYURIManager shareURIManager] addForPath:@"pregnancyHomeCard/babyBorn/withoutSheet"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *params = actionObject.uri.params;
        IMYVKUserMode userMode = IMYVKUserModeLama;
        IMYVKUserMode lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
        /// 3.跳转到设置身份详情页面
        SYUserModeViewModel *viewModel = [[SYUserModeViewModel alloc] init];
        if (params[@"fromWhere_mode_key"]) {
            viewModel.position = [params[@"fromWhere_mode_key"] integerValue];
        } else {
            viewModel.position = 6;
        }
        viewModel.public_info = [params[@"public_info"] integerValue];
        viewModel.useLastModeForBi = YES;
        viewModel.lastModeForBi = lastMode;
        SYModeDetailViewController *vc = [[SYModeDetailViewController alloc] initWithUserModeViewModel:viewModel prepareMode:IMYVKUserModeLama];
        vc.needBabyFirstResponse = YES;
        vc.fromChange = YES;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    
    [[IMYURIManager shareURIManager] addForPath:@"user/address"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        SYAddressVC *vc = [[SYAddressVC alloc] init];
        id callback = actionObject.uri.params[@"callback"];
        if (callback) {
            vc.isYoubiWeb = YES;
            vc.callback = callback;
        }
        [vc setWebCallBack:^(NSDictionary *addressJson) {
            [actionObject callbackWithObject:addressJson];
        }];
        
        [vc imy_setPropertyWithDictionary:actionObject.uri.params filter:@"callback", nil];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"account/unregistering" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYVKWebViewController *webVC = (id)actionObject.getUsingViewController;
        UINavigationController *nav = webVC.navigationController;
        
        __block SYUserInfoVC_V2 *userInfoVC = nil;
        [nav.viewControllers enumerateObjectsUsingBlock:^(__kindof UIViewController * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:[SYUserInfoVC_V2 class]]) {
                userInfoVC = obj;
                *stop = YES;
            }
        }];
        
        imy_asyncMainBlock(^{
            if (userInfoVC) {
                [nav popToViewController:userInfoVC animated:NO];
            } else {
                [nav popToRootViewControllerAnimated:NO];
            }
        });
        
        // 跳转注销成功页面
        imy_asyncMainBlock(^{
            SYUnregistComfirmVC *comfirmVC = [[SYUnregistComfirmVC alloc] init];
            IMYPublicBaseNavigationController *comfirmNav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:comfirmVC];
            [nav imy_present:comfirmNav];
        });
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"account/cancellation" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [[IMYURIManager shareURIManager] runActionWithPath:@"web/pure" params:@{@"url": [IMYAccountWebService cancellationURL], @"simple": @(NO)} info:nil];
    }];
}

//新的 欢迎页
+ (BOOL)isNewModeChangeWelcomePage{
    NSInteger style = [[IMYConfigsCenter sharedInstance] integerForKeyPath:@"womens_health2.mode_change_welcome_page.style"];
    return style == 2;
}
@end
