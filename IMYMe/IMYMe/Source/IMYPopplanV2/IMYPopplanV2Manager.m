//
//  IMYPopplanV2Manager.m
//  IMYMe
//
//  Created by HBQ on 2025/5/20.
//

#import "IMYPopplanV2Manager.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYPopplanV2Api.h"
#import "IMYPopplanV2ExpinfoModel.h"

@interface IMYPopplanV2Manager ()

@property (nonatomic, copy) NSArray<IMYPopplanV2PlanModel *> *planModels;

@end

@implementation IMYPopplanV2Manager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYPopplanV2Manager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

IMY_KYLIN_FUNC_PREMAIN {
    // 提前初始化并启动监听
    [[IMYPopplanV2Manager sharedInstance] startMonitoring];
}

// MARK: - public

/// 同步取缓存的计划
- (IMYPopplanV2PlanModel *)getPlanWithCode:(NSString *)code {
    [self loadPlanModelsFromCacheWithCode:code];
    [self refreshPlansFromRemoteWithCode:code cb:nil];
    IMYPopplanV2PlanModel *validPlanModel = [self getValidPlanModelWithCode:code];
    return validPlanModel;
}

/// 异步取最新的计划
- (void)getPlanWithCode:(NSString *)code
                     cb:(IMYGetPlanBlock)cb {
    @weakify(self);
    [self refreshPlansFromRemoteWithCode:code cb:^(NSError *error) {
        @strongify(self);
        [self loadPlanModelsFromCacheWithCode:code];
        IMYPopplanV2PlanModel *validPlanModel = [self getValidPlanModelWithCode:code];
        if (cb) {
            cb(validPlanModel);
        }
    }];
}

/// 更新曝光时间
- (void)updateExpinfoWithCode:(NSString *)code
                       planId:(NSInteger)planId
                         date:(NSDate *)date {
    NSString *time = [date imy_getDateTimeString];
    
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[u][p %ld][expinfo time = %@]\n", planId, time]];
    // get expinfoModel
    IMYPopplanV2ExpinfoModel *expinfoModel = [self getExpinfoModelWithCode:code planId:planId];
    if (expinfoModel == nil) {
        expinfoModel = [[IMYPopplanV2ExpinfoModel alloc] init];
    }
    
    // update expinfoModel
    expinfoModel.code = code;
    expinfoModel.planId = planId;
    if (imy_isEmptyString(expinfoModel.createTime)) {
        expinfoModel.createTime = time;
    }
    expinfoModel.updateTime = time;
    expinfoModel.times = expinfoModel.times + 1;
    
    // cache expinfo
    [self cacheExpinfo:expinfoModel.imy_jsonObject withCode:code planId:planId];
}

/// 更新曝光关闭
- (void)updateExpinfoCloseWithCode:(NSString *)code
                            planId:(NSInteger)planId
                             close:(BOOL)close {
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[u][p %ld][expinfo close = %@]\n", planId, @(close)]];
    
    IMYPopplanV2ExpinfoModel *expinfoModel = [self getExpinfoModelWithCode:code planId:planId];
    expinfoModel.code = code;
    expinfoModel.planId = planId;
    expinfoModel.close = close;
    [self cacheExpinfo:expinfoModel.imy_jsonObject withCode:code planId:planId];
}

/// 重置曝光信息（针对 showRule 相关）
- (IMYPopplanV2ExpinfoModel *)updateExpinfoRepeatShowRuleWithCode:(NSString *)code
                                                           planId:(NSInteger)planId {
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[u][p %ld][expinfo repeat]\n", planId]];
    
    IMYPopplanV2ExpinfoModel *expinfoModel = [self getExpinfoModelWithCode:code planId:planId];
    expinfoModel.code = code;
    expinfoModel.planId = planId;
    expinfoModel.createTime = @"";
    expinfoModel.updateTime = @"";
    expinfoModel.times = 0;
    [self cacheExpinfo:expinfoModel.imy_jsonObject withCode:code planId:planId];
    
    return expinfoModel;
}

/// 素材曝光
- (void)materialExposureWithPlanModel:(IMYPopplanV2PlanModel *)planModel
                                 item:(NSDictionary *)item {
    if (!planModel || !item || ![item isKindOfClass:[NSDictionary class]]) {
#ifdef DEBUG
        imy_asyncMainBlock(^{
            [UIWindow imy_showTextHUD:@"素材曝光失败，参数错误"];
        });
#endif
        return;
    }
    
    IMYPopplanV2MaterialModel *materialsModel = [planModel.materials firstObject];
    materialsModel.list = @[item];
    
    NSMutableDictionary *exposure_p = [NSMutableDictionary dictionary];
    [exposure_p imy_setNonNilObject:@(planModel.id) forKey:@"plan_id"];
    [exposure_p imy_setNonNilObject:@[materialsModel.imy_jsonObject] forKey:@"materials"];
    uint64_t now = IMYDateTimeIntervalSince1970() * 1000;
    [exposure_p imy_setNonNilObject:@(now) forKey:@"time"];
    [[IMYServerRequest postPath:@"material/exposure"
                           host:mgo_seeyouyima_com
                         params:exposure_p
                        headers:nil] subscribeNext:^(id  _Nullable x) {
        NSLog(@"[popplan-mytab][exposure]succeed");
    } error:^(NSError * _Nullable error) {
        NSLog(@"[popplan-mytab][exposure]error");
    }];
}

// MARK: - debug

+ (void)debugReset {
    [[[IMYKV defaultKV] allKeys] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:@"imypopplanv2-"] || [obj hasPrefix:@"#+imypopplanv2-"]) {
            [[IMYKV defaultKV] removeForKey:obj];
        }
    }];
}

+ (void)debugAddLog:(NSString *)log {
#ifdef DEBUG
    NSLog(@"[ppp2]%@", log);
    
    NSString *key = [self logsKVKey];
    
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:key];
    if (logs == nil) {
        logs = @[];
    }
    NSMutableArray *logs_m = [logs mutableCopy];
    [logs_m addObject:log];
    
    [[IMYKV defaultKV] setArray:logs_m forKey:key];
#endif
}

+ (NSArray *)debugGetLogs {
    NSString *key = [self logsKVKey];
    
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:key];
    if (logs == nil) {
        logs = @[];
    }
    
    return logs;
}

+ (NSArray *)debugGetExpinfos {
    NSMutableArray *expinfos = [NSMutableArray array];
    
    NSString *expInfoPreKey = @"imypopplanv2-expinfo-";
    [[IMYKV defaultKV].allKeys enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:expInfoPreKey]) {
            NSDictionary *expinfo = [[IMYKV defaultKV] dictionaryForKey:obj];
            [expinfos addObject:expinfo];
        }
    }];
    
    return expinfos;
}

+ (NSArray *)debugGetPlansList {
    NSMutableArray *plansList = [NSMutableArray array];
    
    NSString *plansKVKey = @"imypopplanv2-plans-";
    [[IMYKV defaultKV].allKeys enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:plansKVKey]) {
            NSArray *plans = [[IMYKV defaultKV] arrayForKey:obj];
            [plansList addObject:plans];
        }
    }];
    
    return plansList;
}

// MARK: - check planModel with expinfo

/// 检查：返回唯一检测通过的 planModel
- (IMYPopplanV2PlanModel *)getValidPlanModelWithCode:(NSString *)code {
    IMYPopplanV2PlanModel *validPlanModel = nil;
    
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"%@\n[=== 获取计划开始 ===][code %@]", [[NSDate date] imy_getDateTimeString], code]];
    for (IMYPopplanV2PlanModel *planModel in self.planModels) {
        IMYPopplanV2ExpinfoModel *expinfoModel = [self getExpinfoModelWithCode:code planId:planModel.id];
        
        // repeat if need
        expinfoModel = [self repeatIfNeedWithCode:code planModel:planModel expInfoModel:expinfoModel];
        
        // do check
        if ([self doCheckWithCode:code planModel:planModel expInfoModel:expinfoModel]) {
            validPlanModel = planModel;
            break;
        }
    }
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[=== 获取计划结束 ===][code %@][plan %ld]\n", code, validPlanModel.id]];
    
    return validPlanModel;
}

- (IMYPopplanV2ExpinfoModel *)repeatIfNeedWithCode:(NSString *)code
                                         planModel:(IMYPopplanV2PlanModel *)planModel
                                      expInfoModel:(IMYPopplanV2ExpinfoModel *)expinfoModel {
    IMYPopplanV2ShowRuleModel *showRuleModel = planModel.show_rule;
    
    // datas
    NSString *exp_updateTime = expinfoModel.updateTime;
    NSInteger exp_times = expinfoModel.times;
    NSString *rule_repeatf = showRuleModel.repeat_frequency;
    
    // check if need repeat
    BOOL needRepeat = NO;
    BOOL hasExp = imy_isNotEmptyString(exp_updateTime) || exp_times != 0;
    if (hasExp && imy_isNotEmptyString(rule_repeatf)) {
        if ([rule_repeatf isEqualToString:@"weekly"]) {
            needRepeat = ![[exp_updateTime imy_getDateTime] isSameWeekAsDate:[NSDate date]];
        } else if ([rule_repeatf isEqualToString:@"monthly"]) {
            needRepeat = ![[exp_updateTime imy_getDateTime] isSameMonthAsDate:[NSDate date]];
        }
    }
    if (needRepeat) {
        [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][重复频率]{do repeat}", planModel.id]];
        expinfoModel = [self updateExpinfoRepeatShowRuleWithCode:code planId:planModel.id];
        // reset datas
        exp_updateTime = expinfoModel.updateTime;
        exp_times = expinfoModel.times;
    } else {
        [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][重复频率]{no repeat}", planModel.id]];
    }
    
    return expinfoModel;
}

- (BOOL)doCheckWithCode:(NSString *)code
              planModel:(IMYPopplanV2PlanModel *)planModel
            expInfoModel:(IMYPopplanV2ExpinfoModel *)expinfoModel {
    // datas
    IMYPopplanV2ShowRuleModel *showRuleModel = planModel.show_rule;
    NSString *exp_updateTime = expinfoModel.updateTime;
    NSInteger exp_times = expinfoModel.times;
    BOOL exp_close = expinfoModel.close;
    NSInteger rule_interval_hour = showRuleModel.interval_hour;
    NSInteger rule_times = showRuleModel.times;
    
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][曝光信息]{'updateTime': %@, 'times': %ld, close: %@}", planModel.id, exp_updateTime, exp_times, @(exp_close)]];
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][展示规则]{'interval_hour': %ld, 'times': %ld, 'repeat_frequency': %@}", planModel.id, rule_interval_hour, rule_times, showRuleModel.repeat_frequency]];
    
    // [condition] close
    if (exp_close) {
        [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][结果 >>>][close 拦截]", planModel.id]];
        return NO;
    }
    
    // [condition] show_rule.interval_hour
    BOOL need_interval_hour = imy_isNotEmptyString(exp_updateTime) && rule_interval_hour > 0;
    if (need_interval_hour) {
        NSInteger cal_interval_hour = [[exp_updateTime imy_getDateTime] hoursBeforeDate:[NSDate date]];
        if (cal_interval_hour < rule_interval_hour) {
            [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][结果 >>>][show_rule.interval_hour 拦截]", planModel.id]];
            return NO;
        }
    }
    
    // [condition] show_rule.times
    BOOL need_times = exp_times > 0 && rule_times > 0;
    if (need_times) {
        if (exp_times >= rule_times) {
            [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][结果 >>>][show_rule.times 拦截]", planModel.id]];
            return NO;
        }
    }
    
    [IMYPopplanV2Manager debugAddLog:[NSString stringWithFormat:@"[c][p %ld][结果 >>>][通过]", planModel.id]];
    
    return YES;
}

// MARK: - plan

/// 读出缓存数据，持有他
- (void)loadPlanModelsFromCacheWithCode:(NSString *)code {
    NSString *key = [IMYPopplanV2Manager plansKVKeyWithCode:code];
    NSArray *plans = [[IMYKV defaultKV] arrayForKey:key];
    NSArray *planModels = [plans toModels:[IMYPopplanV2PlanModel class]];
    self.planModels = planModels;
}

/// 缓存数据同步服务端（不涉及模型化，只更新缓存）
- (void)refreshPlansFromRemoteWithCode:(NSString *)code
                                    cb:(void (^)(NSError *error))cb {
    @weakify(self);
    [[IMYPopplanV2Api sharedInstance] getPlansWithCode:code onSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *plans = dict[@"list"];
        if (plans && [plans isKindOfClass:[NSArray class]]) {
            NSString *key = [IMYPopplanV2Manager plansKVKeyWithCode:code];
            [[IMYKV defaultKV] setArray:plans forKey:key];
        } else {
            // 没有 list 清空一下计划
            NSString *key = [IMYPopplanV2Manager plansKVKeyWithCode:code];
            [[IMYKV defaultKV] removeForKey:key];
        }
        
        if (cb) {
            cb(nil);
        }
    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
        
        // 接口出错清空一下计划
        NSString *key = [IMYPopplanV2Manager plansKVKeyWithCode:code];
        [[IMYKV defaultKV] removeForKey:key];
        
        if (cb) {
            cb(error);
        }

    }];
}

// MARK: - expinfo

- (IMYPopplanV2ExpinfoModel *)getExpinfoModelWithCode:(NSString *)code
                                               planId:(NSInteger)planId {
    IMYPopplanV2ExpinfoModel *expinfoModel = nil;
    
    NSString *key = [IMYPopplanV2Manager expinfoKVKeyWithCode:code planId:planId];
    NSDictionary *expinfo = [[IMYKV defaultKV] dictionaryForKey:key];
    if ([expinfo isKindOfClass:[NSDictionary class]]) {
        expinfoModel = [expinfo toModel:[IMYPopplanV2ExpinfoModel class]];
    }
    
    return expinfoModel;
}

- (void)cacheExpinfo:(NSDictionary *)expinfo
            withCode:(NSString *)code
              planId:(NSInteger)planId {
    NSString *key = [IMYPopplanV2Manager expinfoKVKeyWithCode:code planId:planId];
    [[IMYKV defaultKV] setDictionary:expinfo forKey:key];
}

// MARK: - keys

+ (NSString *)plansKVKeyWithCode:(NSString *)code {
    return [NSString stringWithFormat:@"imypopplanv2-plans-%@", code];
}

+ (NSString *)expinfoKVKeyWithCode:(NSString *)code planId:(NSInteger)planId {
    return [NSString stringWithFormat:@"imypopplanv2-expinfo-%@-%ld", code, planId];
}

+ (NSString *)logsKVKey {
    return [NSString stringWithFormat:@"#+imypopplanv2-logs"];
}


// MARK: - 启动统计

/// 冷、热启动监听
- (void)startMonitoring {
    @weakify(self);

    // 冷启动：应用启动完成
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidFinishLaunchingNotification object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(__unused NSNotification * _Nullable x) {
        @strongify(self);
        [[IMYPopplanV2Api sharedInstance] reportUserBehaviorEvent:IMYUserBehaviorEventTypeColdStart appId:1 onSuccess:nil onError:nil];
    }];

    // 后台 -> 前台：热启动
    __block NSTimeInterval lastEnterBackgroundTime = [[NSDate date] timeIntervalSince1970];
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(__unused NSNotification * _Nullable x) {
        lastEnterBackgroundTime = [[NSDate date] timeIntervalSince1970];
    }];

    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidBecomeActiveNotification object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(__unused NSNotification * _Nullable x) {
        @strongify(self);
        NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
        // 超过 30 分钟视为热启动
        if (now - lastEnterBackgroundTime > 30 * 60) {
            [[IMYPopplanV2Api sharedInstance] reportUserBehaviorEvent:IMYUserBehaviorEventTypeHotStart appId:1 onSuccess:nil onError:nil];
        }
    }];
}
@end
