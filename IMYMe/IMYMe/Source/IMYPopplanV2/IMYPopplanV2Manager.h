//
//  IMYPopplanV2Manager.h
//  IMYMe
//
//  Created by HBQ on 2025/5/20.
//

#import <Foundation/Foundation.h>
#import "IMYPopplanV2PlanModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^IMYGetPlanBlock)(IMYPopplanV2PlanModel * _Nullable planModel);

@interface IMYPopplanV2Manager : NSObject

+ (instancetype)sharedInstance;

// MARK: - public

/// 同步取缓存的计划
- (IMYPopplanV2PlanModel *)getPlanWithCode:(NSString *)code;

/// 异步取最新的计划
- (void)getPlanWithCode:(NSString *)code
                     cb:(IMYGetPlanBlock)cb;

/// 更新曝光时间
- (void)updateExpinfoWithCode:(NSString *)code
                       planId:(NSInteger)planId
                         date:(NSDate *)date;

/// 更新曝光关闭
- (void)updateExpinfoCloseWithCode:(NSString *)code
                            planId:(NSInteger)planId
                             close:(BOOL)close;

/// 素材曝光
- (void)materialExposureWithPlanModel:(IMYPopplanV2PlanModel *)planModel
                                 item:(NSDictionary *)item;

// MARK: - debug

+ (void)debugReset;

+ (void)debugAddLog:(NSString *)log;

+ (NSArray *)debugGetLogs;

+ (NSArray *)debugGetExpinfos;

+ (NSArray *)debugGetPlansList;

@end

NS_ASSUME_NONNULL_END
