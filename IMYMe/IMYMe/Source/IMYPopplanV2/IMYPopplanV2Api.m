//
//  IMYPopplanV2Api.m
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import "IMYPopplanV2Api.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYPopplanV2Api

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYPopplanV2Api *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - 接口

/// 获取投放计划
- (void)getPlansWithCode:(NSString *)code
               onSuccess:(void (^)(NSDictionary *dict))onSuccess
                 onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:code forKey:@"code"];
    
    // ext
    NSMutableDictionary *ext = [[NSMutableDictionary alloc] init];
    NSNumber *stageType = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"chatAI/stageType" params:nil];
    [ext imy_setNonNilObject:stageType forKey:@"stage_type"];
    NSNumber *babyMonth = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/babyMonth" params:nil];
    [ext imy_setNonNilObject:babyMonth forKey:@"baby_month"];
    NSNumber *pregnancyWeek = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/pregnancyWeek" params:nil];
    [ext imy_setNonNilObject:pregnancyWeek forKey:@"pregnancy_week"];
    [parameter imy_setNonNilObject:ext forKey:@"ext"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"dialog/popplan" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// 用户行为事件上报
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:eventType appId:appId retryCount:0 onSuccess:onSuccess onError:onError];
}

/// 用户行为事件上报（带重试机制）
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                     retryCount:(NSInteger)retryCount
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {
    // 参数校验
    if (eventType != IMYUserBehaviorEventTypeColdStart && eventType != IMYUserBehaviorEventTypeHotStart) {
        if (onError) {
            NSError *error = [NSError errorWithDomain:@"IMYPopplanV2Api"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"无效的事件类型"}];
            onError(error);
        }
        return;
    }

    NSTimeInterval currentTimestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:@(eventType) forKey:@"event"];
    [parameter imy_setNonNilObject:@((NSInteger)currentTimestamp) forKey:@"unix_timestamp"];
    [parameter imy_setNonNilObject:@(appId) forKey:@"app_id"];

    RACSignal *signal = [IMYServerRequest postPath:@"/event/track" host:mgo_seeyouyima_com params:parameter headers:nil];

    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];

    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess();
        }
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        [self handleUserBehaviorEventError:error
                                 eventType:eventType
                                     appId:appId
                                retryCount:retryCount
                                 onSuccess:onSuccess
                                   onError:onError];
    }];
}

/// 上报错误和重试逻辑
- (void)handleUserBehaviorEventError:(NSError *)error
                           eventType:(IMYUserBehaviorEventType)eventType
                               appId:(NSInteger)appId
                          retryCount:(NSInteger)retryCount
                           onSuccess:(void (^)(void))onSuccess
                             onError:(void (^)(NSError *error))onError {

    if (![IMYNetState networkEnable]) {
        if (onError) {
            onError(error);
        }
        return;
    }

    // 获取HTTP状态码
    NSHTTPURLResponse *httpResponse = error.af_httpResponse;
    NSInteger statusCode = httpResponse.statusCode;

    // 判断是否为5xx服务器错误且未重试过
    if (statusCode >= 500 && statusCode < 600 && retryCount == 0) {
        // 延迟1秒后重试
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self reportUserBehaviorEvent:eventType
                                    appId:appId
                               retryCount:retryCount + 1
                                onSuccess:onSuccess
                                  onError:onError];
        });
    } else {
        // 不满足重试条件，直接返回错误
        if (onError) {
            onError(error);
        }
    }
}

// MARK: - 转换

/// IMYHTTPResponse 转 NSDictionary 或 error
+ (RACSignal *)responseFlattenMap:(IMYHTTPResponse *)x {
    if ([x.responseObject isKindOfClass:[NSDictionary class]]) {
        return [RACSignal return:x.responseObject];
    } else {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:x.userInfo];
        userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey] = x.responseData;
        userInfo[AFNetworkingOperationFailingURLResponseErrorKey] = x.response;
        userInfo[NSLocalizedDescriptionKey] = @"网络缓慢，请稍后再试";
        return [RACSignal error:[NSError errorWithDomain:@"ChatAI" code:-9 userInfo:userInfo]];
    }
}

@end
