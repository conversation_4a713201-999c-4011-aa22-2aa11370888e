//
//  IMYPopplanV2Api.h
//  IMYMe
//
//  Created by HBQ on 2025/5/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 用户行为事件类型
typedef NS_ENUM(NSInteger, IMYUserBehaviorEventType) {
    IMYUserBehaviorEventTypeColdStart = 1,
    IMYUserBehaviorEventTypeHotStart = 2,
};

@interface IMYPopplanV2Api : NSObject

+ (instancetype)sharedInstance;

// MARK: - 接口

/// 获取投放计划
- (void)getPlansWithCode:(NSString *)code
               onSuccess:(void (^)(NSDictionary *dict))onSuccess
                 onError:(void (^)(NSError *error))onError;


/// 用户行为事件上报
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError;

@end

NS_ASSUME_NONNULL_END
