//
//  IMYPopplanV2MaterialModel.m
//  IMYMe
//
//  Created by HBQ on 2025/5/20.
//

#import "IMYPopplanV2MaterialModel.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYPopplanV2ContentShowRule

@end

@implementation IMYPopplanV2MaterialItem1

@end

@implementation IMYPopplanV2MaterialItem2

@end

@implementation IMYPopplanV2MaterialItem3

@end

@implementation IMYPopplanV2MaterialItem4

@end

@implementation IMYPopplanV2MaterialItem5

@end

@implementation IMYPopplanV2MaterialModel

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"content_show_rule": IMYPopplanV2ContentShowRule.class,
    };
}

- (NSArray<IMYPopplanV2MaterialItem1 *> *)item1List {
    if (self.material_type != 1) {
        return @[];
    }
    
    NSArray *list = [self.list toModels:[IMYPopplanV2MaterialItem1 class]];
    return list;
}

- (NSArray<IMYPopplanV2MaterialItem2 *> *)item2List {
    if (self.material_type != 2) {
        return @[];
    }
    
    NSArray *list = [self.list toModels:[IMYPopplanV2MaterialItem2 class]];
    return list;
}

- (NSArray<IMYPopplanV2MaterialItem3 *> *)item3List {
    if (self.material_type != 3) {
        return @[];
    }
    
    NSArray *list = [self.list toModels:[IMYPopplanV2MaterialItem3 class]];
    return list;
}

- (NSArray<IMYPopplanV2MaterialItem4 *> *)item4List {
    if (self.material_type != 4) {
        return @[];
    }
    
    NSArray *list = [self.list toModels:[IMYPopplanV2MaterialItem4 class]];
    return list;
}

- (NSArray<IMYPopplanV2MaterialItem5 *> *)item5List {
    if (self.material_type != 5) {
        return @[];
    }
    
    NSArray *list = [self.list toModels:[IMYPopplanV2MaterialItem5 class]];
    return list;
}

@end

@implementation IMYPopplanV2MaterialModel (item4)

- (IMYPopplanV2MaterialItem4 *)currentItem4 {
    NSArray *item4List = [self item4List];
    
    if (![item4List isKindOfClass:[NSArray class]]) {
        return nil;
    }
    
    if (item4List.count == 0) {
        return nil;
    }
    
    IMYPopplanV2MaterialItem4 *item4 = nil;
    if (self.content_show_rule.type == 1) {
        // 轮播
        NSInteger index = [IMYPopplanV2MaterialModel getItem4Index];
        
        // 处理越界
        if (index < 0 || index >= item4List.count) {
            index = 0;
            [IMYPopplanV2MaterialModel setItem4Index:index];
        }
        
        item4 = item4List[index];
    } else {
        // 不轮播
        item4 = [item4List firstObject];
    }
    
    return item4;
}

+ (void)increaseItem4Index {
    NSInteger index = [IMYPopplanV2MaterialModel getItem4Index];
    index++;
    [IMYPopplanV2MaterialModel setItem4Index:index];
    
#ifdef DEBUG
    imy_asyncMainBlock(2.0, ^{
        [UIWindow imy_showTextHUD:[NSString stringWithFormat:@"当前item4索引：%ld", (long)index]];
    });
#endif
}

// MARK: - Helper

+ (NSInteger)getItem4Index {
    NSString *key = @"IMYPopplanV2MaterialModel-item4index";
    NSInteger index = [[IMYKV defaultKV] integerForKey:key];
    return index;
}

+ (void)setItem4Index:(NSInteger)index {
    NSString *key = @"IMYPopplanV2MaterialModel-item4index";
    [[IMYKV defaultKV] setInteger:index forKey:key];
}

@end
