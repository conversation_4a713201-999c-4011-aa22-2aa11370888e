//
//  IMYPopplanV2MaterialModel.h
//  IMYMe
//
//  Created by HBQ on 2025/5/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYPopplanV2ContentShowRule : NSObject

/// 素材内容展示规则类型1依次循环
@property (nonatomic, assign) NSInteger type;

@end

@interface IMYPopplanV2MaterialItem1 : NSObject

@property (nonatomic, copy) NSString *id;

@property (nonatomic, copy) NSString *img;

@property (nonatomic, copy) NSString *jump_uri;

@property (nonatomic, copy) NSDictionary *ext;

@end

@interface IMYPopplanV2MaterialItem2 : NSObject

@property (nonatomic, copy) NSString *id;

@property (nonatomic, copy) NSString *img;

@property (nonatomic, copy) NSString *jump_uri;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSDictionary *ext;

@end

@interface IMYPopplanV2MaterialItem3 : NSObject

@property (nonatomic, copy) NSString *id;

@property (nonatomic, assign) NSInteger research_type;

@property (nonatomic, copy) NSDictionary *ext;

@end

@interface IMYPopplanV2MaterialItem4 : NSObject

@property (nonatomic, copy) NSString *id;

@property (nonatomic, copy) NSString *img_light;

@property (nonatomic, copy) NSString *img_dark;

@property (nonatomic, copy) NSString *jump_uri;

@property (nonatomic, copy) NSDictionary *ext;

@end

@interface IMYPopplanV2MaterialItem5 : NSObject

@property (nonatomic, copy) NSString *id;
@property (nonatomic, assign) NSInteger template;
@property (nonatomic, copy) NSString *background_img_light;
@property (nonatomic, copy) NSString *background_img_dark;
@property (nonatomic, copy) NSString *img_light;
@property (nonatomic, copy) NSString *img_dark;
@property (nonatomic, copy) NSString *text_title;
@property (nonatomic, copy) NSString *text_title_color;
@property (nonatomic, copy) NSString *text_desc;
@property (nonatomic, copy) NSString *main_button_text;
@property (nonatomic, copy) NSString *main_button_uri;
@property (nonatomic, copy) NSString *sub_button_text;
@property (nonatomic, copy) NSString *sub_button_uri;
@property (nonatomic, copy) NSString *sub_button_icon;
@property (nonatomic, copy) NSDictionary *ext;

@end

/// 计划素材：对应的 list 可以模型化为 IMYPopplanV2MaterialItem{material_type}
@interface IMYPopplanV2MaterialModel : NSObject

@property (nonatomic, assign) NSInteger material_type;

/// v893 素材内容展示规则
@property (nonatomic, strong) IMYPopplanV2ContentShowRule *content_show_rule;

@property (nonatomic, copy) NSArray<NSDictionary *> *list;

/// 返回模型化的 list，对应的 material_type 不为 1 则返回空数组
- (NSArray<IMYPopplanV2MaterialItem1 *> *)item1List;

/// 返回模型化的 list，对应的 material_type 不为 2 则返回空数组
- (NSArray<IMYPopplanV2MaterialItem2 *> *)item2List;

/// 返回模型化的 list，对应的 material_type 不为 3 则返回空数组
- (NSArray<IMYPopplanV2MaterialItem3 *> *)item3List;

/// 返回模型化的 list，对应的 material_type 不为 4 则返回空数组
- (NSArray<IMYPopplanV2MaterialItem4 *> *)item4List;

/// 返回模型化的 list，对应的 material_type 不为 5 则返回空数组
- (NSArray<IMYPopplanV2MaterialItem5 *> *)item5List;

@end

@interface IMYPopplanV2MaterialModel (item4)

- (IMYPopplanV2MaterialItem4 *)currentItem4;

+ (void)increaseItem4Index;

@end

NS_ASSUME_NONNULL_END
