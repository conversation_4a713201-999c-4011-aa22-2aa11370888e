//
//  IMYPopPlanManager.h
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import <Foundation/Foundation.h>
#import "IMYPopPlanModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 投放计划管理
@interface IMYPopPlanManager : NSObject

/// 单例
+ (instancetype)sharedInstance;

/// 获得 planModel: 返回该资源位（code）当前可以展示的 planModel。
/// 调用方还需获取具体素材：planModel 提供获取素材 id 的方法，调用方根据素材 id 去获取素材详情
- (IMYPopPlanModel *)getPlanModelWithCode:(NSString *)code
                               refreBlock:(void (^)(IMYPopPlanModel *refreshPlanModel))refreshBlock;

/// 保存曝光信息（用于频控）
- (void)saveShowInfoWithPlanId:(NSString *)planId
                      surveyId:(NSString *)surveyId;

/// 保存关闭信息（用于频控）
- (void)saveCloseInfo:(NSString *)surveyId;

/// 重置 show_rule（根据 repeat_frequency 重置 show_rule 的曝光信息）
- (BOOL)repeatShowRuleIfNeedWithSurveyId:(NSString *)surveyId planModel:(IMYPopPlanModel *)planModel;

// MARK: - 素材曝光

- (void)materialExposureWithPlanModel:(IMYPopPlanModel *)planModel
                                 item:(NSDictionary *)item;

/// MARK: - debug

- (NSArray *)debugGetALLLog;

- (NSArray *)debugGetAllPlanList;

- (NSDictionary *)debugGetAllGfcPolicy;

- (NSArray *)debugGetAllShowInfos;

- (NSString *)debugGetLastShowTime;

- (void)removeAllCache;

- (void)removePlanList;

@end

NS_ASSUME_NONNULL_END
