//
//  IMYMeMine3ModeSwitchItem.m
//  IMYMe
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/7.
//

#import "IMYMeMine3ModeSwitchItem.h"
#import "IMYMeURIRegister.h"
@implementation IMYMeMine3ModeSwitchItem

+ (IMYMeMine3ModeSwitchItem *)switchItemWithUserMode:(IMYVKUserMode)userMode {
    IMYMeMine3ModeSwitchItem *item = [[IMYMeMine3ModeSwitchItem alloc] init];
    item.userMode = userMode;
    
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.mytab_guide"];
    
    // pic
    NSString *picKey = [NSString stringWithFormat:@"mode_side_pic_%ld", userMode];
//    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
//        picKey = [NSString stringWithFormat:@"3d_%@",picKey];
//    }
    NSString *pic = [group stringForKey:picKey];
    if (imy_isEmptyString(pic)) {
        pic = [self defaultPicForUserMode:userMode];
    }
    item.icon = pic;
    
    // title
    NSString *titleKey = [NSString stringWithFormat:@"mode_side_title_%ld", userMode];
    NSString *title = [group stringForKey:titleKey];
    if (imy_isEmptyString(title)) {
        title = [self defaultTitleForUserMode:userMode];
    }
    item.title = title;
    
    // maskIcon
    item.maskIcon = [self maskIconForUserMode:userMode];
    
    return item;
}

#pragma mark - LocalData

+ (NSString *)defaultPicForUserMode:(IMYVKUserMode)userMode {
    NSString *ret = @"";
    
    switch (userMode) {
        case IMYVKUserModeNormal: {
            ret = @"pt_icon_mode_jingqi";
        }
            break;
        case IMYVKUserModeForPregnant: {
            ret = @"pt_icon_mode_beiyun";
        }
            break;
        case IMYVKUserModePregnancy: {
            ret = @"pt_icon_mode_huaiyun";
        }
            break;
        case IMYVKUserModeLama: {
            ret = @"pt_icon_mode_yuer";
        }
            break;
        default: {
            
        }
            break;
    }
    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
        ret = [NSString stringWithFormat:@"3d_%@",ret];
    }
    return ret;
}

+ (NSString *)defaultTitleForUserMode:(IMYVKUserMode)userMode {
    NSString *ret = @"";
    
    switch (userMode) {
        case IMYVKUserModeNormal: {
            ret = IMYString(@"经期模式");
        }
            break;
        case IMYVKUserModeForPregnant: {
            ret = IMYString(@"备孕模式");
        }
            break;
        case IMYVKUserModePregnancy: {
            ret = IMYString(@"怀孕模式");
        }
            break;
        case IMYVKUserModeLama: {
            ret = IMYString(@"育儿模式");
        }
            break;
        default: {
            
        }
            break;
    }
    
    return ret;
}

+ (NSString *)maskIconForUserMode:(IMYVKUserMode)userMode {
    NSString *ret = @"";
    
    switch (userMode) {
        case IMYVKUserModeNormal: {
            ret = @"mine_select_left_small";
        }
            break;
        case IMYVKUserModeForPregnant: {
            ret = @"mine_select_centre_small";
        }
            break;
        case IMYVKUserModePregnancy: {
            ret = @"mine_select_centre_small";
        }
            break;
        case IMYVKUserModeLama: {
            ret = @"mine_select_right_small";
        }
            break;
        default: {
            
        }
            break;
    }
    
    return ret;
}

@end
