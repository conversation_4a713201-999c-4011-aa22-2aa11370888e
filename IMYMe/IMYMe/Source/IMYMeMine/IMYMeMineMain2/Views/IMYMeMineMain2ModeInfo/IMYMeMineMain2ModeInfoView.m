//
//  IMYMeMineMain2ModeInfoView.m
//  AFNetworking
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/7.
//

#import "IMYMeMineMain2ModeInfoView.h"
#import "IMYMeMineMain2Manager.h"
#import "IMYModeWelcomeContentView.h"
#import "IMYModeWelcomeContentView_V2.h"
#import <IMYCommonKit/IMYCKLoadingTextButton.h>
#import "IMYMeURIRegister.h"
@interface IMYMeMineMain2ModeInfoView ()

@property (nonatomic, assign) IMYVKUserMode userMode;

@property (nonatomic, strong) UIView *myMaskView;

@property (nonatomic, strong) IMYModeWelcomeContentView *contentView;
@property (nonatomic, strong) IMYModeWelcomeContentView_V2 *contentViewV2;

@end

@implementation IMYMeMineMain2ModeInfoView

- (instancetype)initWithUserMode:(IMYVKUserMode)userMode {
    self = [super init];
    if (self) {
        self.userMode = userMode;
        
        [self setupUI];
        
        // 曝光埋点
        [self biEvent:1];
    }
    return self;
}

#pragma mark - Subviews

- (void)setupUI {
    self.frame = UIScreen.mainScreen.bounds;
    [self addSubview:self.myMaskView];
    [self configContentView];
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [window addSubview:self];
}

- (void)configContentView {
    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
        [self configContentViewV2];
        return;
    }
    self.contentView = [[IMYModeWelcomeContentView alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT)];
    self.contentView.type = self.userMode;
    self.contentView.fromName = @"mytabSelect";
    
    @weakify(self);
    [self.contentView setCloseBlock:^{
        @strongify(self);
        [self handleCloseEvent:nil];
    }];

    self.contentView.nextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        if (self.contentView.willNextActionBlock) {
            //“我”tab切换身份
            if (self.dismissBlock) {
                self.dismissBlock();
            }
        } else {
            //记录tab切换身份
            [self dismiss];
        }
        [self handleStartEvent:nil];
    };
    
    self.contentView.willNextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        self.myMaskView.userInteractionEnabled = NO;
        self.contentView.userInteractionEnabled = NO;
        @weakify(self);
        [UIView animateWithDuration:0.2 animations:^{
            @strongify(self);
            self.myMaskView.alpha = 0;
            self.contentView.imy_top = self.imy_height;
        } completion:^(BOOL finished) {
            @strongify(self);
            [self removeFromSuperview];
        }];
    };

    [self.myMaskView addSubview:self.contentView];
}


- (void)configContentViewV2 {
    self.contentViewV2 = [[IMYModeWelcomeContentView_V2 alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT)];
    self.contentViewV2.type = self.userMode;
    self.contentViewV2.fromName = @"mytabSelect";
    
    @weakify(self);
    [self.contentViewV2 setCloseBlock:^{
        @strongify(self);
        [self handleCloseEvent:nil];
    }];
    [self.contentViewV2 setCloseForChangeModeBlock:^{
        @strongify(self);
        [self handleCloseEvent:nil];
    }];

    self.contentViewV2.nextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        if (self.contentViewV2.willNextActionBlock) {
            //“我”tab切换身份
            if (self.dismissBlock) {
                self.dismissBlock();
            }
        } else {
            //记录tab切换身份
            [self dismiss];
        }
        [self handleStartEvent:nil];
    };
    
    self.contentViewV2.willNextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        self.myMaskView.userInteractionEnabled = NO;
        self.contentViewV2.userInteractionEnabled = NO;
        @weakify(self);
        [UIView animateWithDuration:0.2 animations:^{
            @strongify(self);
            self.myMaskView.alpha = 0;
            self.contentViewV2.imy_top = self.imy_height;
        } completion:^(BOOL finished) {
            @strongify(self);
            [self removeFromSuperview];
        }];
    };

    [self.myMaskView addSubview:self.contentViewV2];
}

#pragma mark - Show

- (void)show {
    [self.contentView.nextBtn ?:self.contentViewV2.nextBtn setTitle:[[IMYMeMineMain2Manager sharedInstance] modeBtn:self.userMode] buttonType:IMYCKLoadingButtonRedType];
    [self showWithAnim:YES];
}

- (void)showWithAnim:(BOOL)isAnim {
    UIView *contentView = self.contentView ?: self.contentViewV2;
    if (!isAnim) {
        self.myMaskView.userInteractionEnabled = YES;
        contentView.userInteractionEnabled = YES;
        self.myMaskView.alpha = 1;
        contentView.imy_top = self.imy_height - contentView.imy_height;
        return;
    }
    
    self.myMaskView.userInteractionEnabled = NO;
    contentView.userInteractionEnabled = NO;
    @weakify(self);
    [UIView animateWithDuration:0.2 animations:^{
        @strongify(self);
        self.myMaskView.alpha = 1;
        contentView.imy_top = self.imy_height - contentView.imy_height;
    } completion:^(BOOL finished) {
        @strongify(self);
        self.myMaskView.userInteractionEnabled = YES;
        contentView.userInteractionEnabled = YES;
        self.myMaskView.alpha = 1;
        contentView.imy_top = self.imy_height - contentView.imy_height;
    }];
}

- (void)dismiss {
    UIView *contentView = self.contentView ?: self.contentViewV2;
    self.myMaskView.userInteractionEnabled = NO;
    contentView.userInteractionEnabled = NO;
    @weakify(self);
    [UIView animateWithDuration:0.2 animations:^{
        @strongify(self);
        self.myMaskView.alpha = 0;
        contentView.imy_top = self.imy_height;
    } completion:^(BOOL finished) {
        @strongify(self);
        [self removeFromSuperview];
    }];
    
    if (self.dismissBlock) {
        self.dismissBlock();
    }
}

#pragma mark - Actions

/// 开始
- (void)handleStartEvent:(id)sender {
    if (self.clickBlock) {
        self.clickBlock(self.userMode, 1);
        
        [self biEvent:2];
    }
}

/// 关闭
- (void)handleCloseEvent:(id)sender {
    [self dismiss];
    
    if (self.clickBlock) {
        self.clickBlock(self.userMode, 2);
    }
}

/// 点击空白区域
- (void)handleTouchEmptyEvent:(id)sender {
    [self dismiss];
    
    if (self.clickBlock) {
        self.clickBlock(self.userMode, 3);
    }
}

#pragma mark - BI

- (void)biEvent:(NSInteger)action {
    NSMutableDictionary *p = [NSMutableDictionary dictionary];
    [p imy_setNonNilObject:@(action) forKey:@"action"];
    NSString *event = @"";
    if (self.userMode == IMYVKUserModePregnancy) {
        event = @"yy_wtab_kqhyms";
    } else if (self.userMode == IMYVKUserModeForPregnant) {
        event = @"yy_wtab_kqbyms";
    } else if (self.userMode == IMYVKUserModeLama) {
        event = @"yy_wtab_kqyems";
    } else if (self.userMode == IMYVKUserModeNormal) {
        event = @"yy_wtab_kqjqms";
    }
    
    if (imy_isEmptyString(event)) {
        return;
    }
    
    [p imy_setNonNilObject:event forKey:@"event"];
    [IMYGAEventHelper postWithPath:@"event" params:p headers:nil completed:nil];
}

#pragma mark - Get

- (UIView *)myMaskView {
    if (!_myMaskView) {
        _myMaskView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        _myMaskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
        _myMaskView.alpha = 0;
        
        @weakify(self);
        [_myMaskView bk_whenTapped:^{
            @strongify(self);
            [self handleTouchEmptyEvent:nil];
        }];
    }
    return _myMaskView;
}

@end
