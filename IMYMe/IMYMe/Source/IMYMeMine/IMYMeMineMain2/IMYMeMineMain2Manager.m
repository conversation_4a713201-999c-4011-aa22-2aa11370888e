//
//  IMYMeMineMain2Manager.m
//  AFNetworking
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/2.
//

#import "IMYMeMineMain2Manager.h"
#import "IMYMeURIRegister.h"
@interface IMYMeMineMain2Manager ()

@property (nonatomic, strong) NSDictionary *localMytabGuide;

@end

@implementation IMYMeMineMain2Manager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYMeMineMain2Manager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
        instance.localMytabGuide = @{
            @"mode_side_title_0": @"经期模式",
            @"mode_side_title_1": @"怀孕模式",
            @"mode_side_title_2": @"备孕模式",
            @"mode_side_title_3": @"育儿模式",
            @"mode_side_pic_0": @"pt_icon_mode_jingqi",
            @"mode_side_pic_1": @"pt_icon_mode_huaiyun",
            @"mode_side_pic_2": @"pt_icon_mode_beiyun",
            @"mode_side_pic_3": @"pt_icon_mode_yuer",
            @"3d_mode_side_pic_0": @"3d_pt_icon_mode_jingqi",
            @"3d_mode_side_pic_1": @"3d_pt_icon_mode_huaiyun",
            @"3d_mode_side_pic_2": @"3d_pt_icon_mode_beiyun",
            @"3d_mode_side_pic_3": @"3d_pt_icon_mode_yuer",
            @"mode_info_pic_0": @"pt_modal_service_jingqi",
            @"mode_info_pic_1": @"pt_modal_service_huaiyun",
            @"mode_info_pic_2": @"pt_modal_service_beiyun",
            @"mode_info_pic_3": @"pt_modal_service_yuer",
            @"mode_info_btn_0": @"开启经期模式",
            @"mode_info_btn_1": @"开启怀孕模式",
            @"mode_info_btn_2": @"开启备孕模式",
            @"mode_info_btn_3": @"开启育儿模式",
        };
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

- (NSString *)localModeSideTile:(IMYVKUserMode)userMode {
    NSString *key = [NSString stringWithFormat:@"mode_side_title_%ld", userMode];
    return self.localMytabGuide[key];
}

- (NSString *)localModeSidePic:(IMYVKUserMode)userMode {
    NSString *key = [NSString stringWithFormat:@"mode_side_pic_%ld", userMode];
    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
        key = [NSString stringWithFormat:@"3d_%@",key];
    }
    return self.localMytabGuide[key];
}

- (NSString *)localModeInfoPic:(IMYVKUserMode)userMode {
    NSString *key = [NSString stringWithFormat:@"mode_info_pic_%ld", userMode];
    return self.localMytabGuide[key];
}

- (NSString *)localModeInfoBtn:(IMYVKUserMode)userMode {
    NSString *key = [NSString stringWithFormat:@"mode_info_btn_%ld", userMode];
    return self.localMytabGuide[key];
}

- (NSString *)modePic:(IMYVKUserMode)userMode {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.mytab_guide"];
    NSString *key = [NSString stringWithFormat:@"mode_info_pic_%ld", userMode];
    
    NSString *ret = [group stringForKey:key];
    if (imy_isEmptyString(ret)) {
        ret = [[IMYMeMineMain2Manager sharedInstance] localModeInfoPic:userMode];
    }
    
    return ret;
}

- (NSString *)modeBtn:(IMYVKUserMode)userMode {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.mytab_guide"];
    NSString *key = [NSString stringWithFormat:@"mode_info_btn_%ld", userMode];
    
    NSString *ret = [group stringForKey:key];
    if (imy_isEmptyString(ret)) {
        ret = [[IMYMeMineMain2Manager sharedInstance] localModeInfoBtn:userMode];
    }
    
    return ret;
}

- (NSString *)sideTitle:(IMYVKUserMode)userMode {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.mytab_guide"];
    NSString *key = [NSString stringWithFormat:@"mode_side_title_%ld", userMode];
    
    NSString *ret = [group stringForKey:key];
    if (imy_isEmptyString(ret)) {
        ret = [[IMYMeMineMain2Manager sharedInstance] localModeSideTile:userMode];
    }
    
    return ret;
}

- (NSString *)sidePic:(IMYVKUserMode)userMode {
    IMYCConfigsGroup *group = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.mytab_guide"];
    NSString *key = [NSString stringWithFormat:@"mode_side_pic_%ld", userMode];
//    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
//        key = [NSString stringWithFormat:@"3d_%@",key];
//    }
    NSString *ret = [group stringForKey:key];
    if (imy_isEmptyString(ret)) {
        ret = [[IMYMeMineMain2Manager sharedInstance] localModeSidePic:userMode];
    }
    
    return ret;
}

@end
