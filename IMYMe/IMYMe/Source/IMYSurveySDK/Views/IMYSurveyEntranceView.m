//
//  IMYSurveyEntranceView.m
//  IMYMe
//
//  Created by HBQ on 2025/3/19.
//

#import "IMYSurveyEntranceView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYSurveyDialog.h"
#import "IMYSurveyAPI.h"
#import "IMYPopPlanManager.h"

@interface IMYSurveyEntranceView ()

@property (nonatomic, assign) CGFloat maxWidth;

@property (nonatomic, strong) IMYSurveyModel *surveyModel;

@property (nonatomic, strong) UIView *bgView;

@property (nonatomic, strong) UIView *containerView;

@property (nonatomic, strong) UIImageView *colorView;

@property (nonatomic, strong) IMYTouchEXButton *closeBtn;

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) IMYSurveyQListView *surveyQListView;

@end

@implementation IMYSurveyEntranceView

// MARK: - init

+ (IMYSurveyEntranceView *)entranceViewWithMaxWidth:(CGFloat)maxWidth {
    IMYSurveyEntranceView *view = [[IMYSurveyEntranceView alloc] initWithFrame:CGRectZero];
    view.maxWidth = maxWidth;
    [view setupUI];
    return view;
}

// MARK: - setup

- (void)setupUI {
    [self imy_setBackgroundColorForKey:kCK_White_AN];
    [self imy_drawAllCornerRadius:12];
    
    [self addSubview:self.colorView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.closeBtn];
    [self addSubview:self.surveyQListView];
    
    [self.colorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(0);
        make.trailing.mas_equalTo(self.mas_trailing).offset(0);
        make.width.mas_equalTo(180);
        make.height.mas_equalTo(80);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(12);
        make.leading.mas_equalTo(self.mas_leading).offset(12);
        make.trailing.mas_equalTo(self.mas_trailing).offset(-44);
        make.height.mas_equalTo(21);
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mas_top).offset(12);
        make.trailing.mas_equalTo(self.mas_trailing).offset(-12);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
    }];

    [self.surveyQListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(0);
        make.leading.mas_equalTo(self.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.mas_trailing).offset(0);
        make.height.mas_equalTo(CGFLOAT_MIN);
    }];
}

// MARK: - data

- (void)setupSurveyModel:(IMYSurveyModel *)surveyModel {
    _surveyModel = surveyModel;
    
    CGFloat height = [self.class heightWithSurveyModel:surveyModel maxWidth:self.maxWidth];
    [self.surveyQListView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
    
    // 根据问卷 id 走曝光检测
    self.imyut_eventInfo.eventName = [NSString stringWithFormat:@"IMYSurveyEntranceView-%@", surveyModel.id];
    @weakify(self);
    [self.imyut_eventInfo setExposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        if (!self.surveyModel.local_isNegative) {
            [[IMYPopPlanManager sharedInstance] saveShowInfoWithPlanId:self.surveyModel.local_planId surveyId:self.surveyModel.id];
        }
        
        // 素材曝光
        IMYPopPlanModel *planModel = surveyModel.local_planModel;
        IMYPopPlanMateriaModel *materiaModel = planModel.materials.firstObject;
        NSDictionary *item = materiaModel.list.firstObject;
        [[IMYPopPlanManager sharedInstance] materialExposureWithPlanModel:planModel item:item];
    }];
    
    self.titleLabel.text = surveyModel.title;
    
    [self.surveyQListView setupSurveyModel:surveyModel];
}

+ (CGFloat)heightWithSurveyModel:(IMYSurveyModel *)surveyModel
                        maxWidth:(CGFloat)maxWidth {
    CGFloat qListViewHeight = [IMYSurveyQListView heightWithSurveyModel:surveyModel cardWidth:maxWidth style:IMYSurveyQListStyle_Entrace];
    return 12 + 21 + qListViewHeight;
}

// MARK: - actions

- (void)handleClickClose:(id)sender {
    // [问卷埋点][feeds] pt_zhwj_wjdc 关闭问卷 点击
    NSString *planBiz = self.surveyModel.local_planBiz;
    NSString *surveyId = self.surveyModel.id;
    NSInteger scoreValue = 0;
    NSString *tgValue = @"";
    [[IMYSurveyAPI sharedInstance] postEvent_pt_zhwj_gbwj:2
                                              public_type:planBiz
                                               topical_id:surveyId
                                              public_info:scoreValue
                                                 info_key:tgValue
                                                  info_id:@""];
    
    [self.surveyModel resetUseQuestionList];
    if (self.onClickClose) {
        self.onClickClose(self.surveyModel);
    }
    
     [[IMYPopPlanManager sharedInstance] saveCloseInfo:self.surveyModel.id];
}

- (void)handleClickSurveyModel:(IMYSurveyModel *)surveyModel
                        qModel:(IMYSurveyQModel *)qModel
                        option:(IMYSurveyOptionModel *)optionModel {
    self.userInteractionEnabled = NO;
    imy_asyncMainBlock(1, ^{
        self.userInteractionEnabled = YES;
    });
    
    // 提前回调给使用方，可以进行埋点
    if (self.onClickOption) {
        self.onClickOption(surveyModel, qModel, optionModel);
    }
    
    BOOL ifDirectCommit = YES;
    BOOL hasNextQuestion = imy_isNotEmptyString(optionModel.question_id);
    if (surveyModel.use_question_list.count > 1 || hasNextQuestion || qModel.type == IMYSurveyQuestionType_TG2) {
        ifDirectCommit = NO;
    }
    
    if (ifDirectCommit) {
        [self.surveyQListView setupSurveyModel:surveyModel];
        
        // [问卷埋点][feeds] pt_zhwj_tjwjjg 提交问卷结果 点击
        for (IMYSurveyQModel *qModel in self.surveyModel.use_question_list) {
            NSString *planBiz = self.surveyModel.local_planBiz;
            NSString *surveyId = self.surveyModel.id;
            NSInteger scoreValue = [qModel calScoreValue];
            NSString *tgValue = [qModel calTgValue];
            [[IMYSurveyAPI sharedInstance] postEvent_pt_zhwj_tjwjjg:2
                                                        public_type:planBiz
                                                         topical_id:surveyId
                                                        public_info:scoreValue
                                                           info_key:tgValue
                                                            info_id:qModel.question_id];
        }
        
        // 直接提交
        @weakify(self);
        [[IMYSurveyAPI sharedInstance] postSurveyAnswerWithSurveyId:self.surveyModel.id surveyModel:self.surveyModel onSuccess:^(NSDictionary * _Nonnull dict) {
            @strongify(self);
            [self.surveyModel resetUseQuestionList];
            if (self.onClickSubmit) {
                self.onClickSubmit(self.surveyModel);
            }
            
            imy_asyncMainBlock(^{
                [UIWindow imy_showTextHUD:@"提交成功，感谢你的反馈"];
                [[IMYPopPlanManager sharedInstance] saveCloseInfo:self.surveyModel.id];
            });
        } onError:^(NSError * _Nonnull error) {
            imy_asyncMainBlock(^{
                if (error.code == 10001004) {
                    [UIWindow imy_showTextHUD:@"还有内容未填写哦"];
                } else if (![IMYNetState networkEnable]) {
                    [UIWindow imy_showTextHUD:@"网络不见了，请检查网络"];
                } else {
                    [UIWindow imy_showTextHUD:@"提交失败，请重试"];
                }
            });
        }];
    } else {
        // 找下一题
        [self.surveyModel clickQModel:qModel option:optionModel];
        [self setupSurveyModel:surveyModel];
        
        // 显示弹窗
        IMYSurveyDialog *dialog = [[IMYSurveyDialog alloc] initWithFrame:[UIScreen mainScreen].bounds];
        @weakify(self);
        [dialog setOnClickClose:^(IMYSurveyModel * _Nullable surveyModel) {
            @strongify(self);
            [self setupSurveyModel:self.surveyModel];
        }];
        [dialog setOnClickSubmit:^(IMYSurveyModel * _Nullable surveyModel) {
            @strongify(self);
            self.surveyModel = surveyModel;
            if (self.onClickSubmit) {
                self.onClickSubmit(self.surveyModel);
            }
            
            imy_asyncMainBlock(^{
                [UIWindow imy_showTextHUD:@"提交成功，感谢你的反馈"];
                [[IMYPopPlanManager sharedInstance] saveCloseInfo:self.surveyModel.id];
            });
        }];
        [dialog setOnClickOption:^(IMYSurveyModel * _Nonnull surveyModel, IMYSurveyQModel * _Nonnull qModel, IMYSurveyOptionModel * _Nonnull optionModel) {
            @strongify(self);
            self.surveyModel = surveyModel;
            [self setupSurveyModel:surveyModel];
        }];
        [dialog setupSurveyModel:self.surveyModel];
        [dialog show];
    }
}

// MARK: - get

- (UIImageView *)colorView {
    if (!_colorView) {
        UIImageView *view = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 180, 80)];
        [view imy_setImageForKey:@"imysurvey_img_bg"];
        _colorView = view;
    }
    return _colorView;
}

- (IMYTouchEXButton *)closeBtn {
    if (!_closeBtn) {
        IMYTouchEXButton *btn = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 20, 20)];
        UIImage *img = [UIImage imy_imageForKey:@"imysurvey_icon_guanbi"];
        [btn imy_setImage:img state:UIControlStateNormal];
        [btn addTarget:self action:@selector(handleClickClose:) forControlEvents:UIControlEventTouchUpInside];
        [btn setExtendTouchAllValue:12];
        
        _closeBtn = btn;
    }
    return _closeBtn;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
        label.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [label imy_setTextColorForKey:kCK_Black_A];
        [label setTextAlignment:NSTextAlignmentLeft];
        
        _titleLabel = label;
    }
    return _titleLabel;
}

- (IMYSurveyQListView *)surveyQListView {
    if (!_surveyQListView) {
        _surveyQListView = [IMYSurveyQListView qListViewWithCardWidth:self.maxWidth
                                                                style:IMYSurveyQListStyle_Entrace];
        
        @weakify(self);
        [_surveyQListView setOnHeightChange:^(IMYSurveyModel * _Nullable surveyModel) {
            @strongify(self);
            [self setupSurveyModel:surveyModel];
        }];
        
        [_surveyQListView setOnClickOption:^(IMYSurveyModel * _Nonnull surveyModel, IMYSurveyQModel * _Nonnull qModel, IMYSurveyOptionModel * _Nonnull optionModel) {
            @strongify(self);
            [self handleClickSurveyModel:surveyModel qModel:qModel option:optionModel];
        }];
    }
    return _surveyQListView;
}

@end
