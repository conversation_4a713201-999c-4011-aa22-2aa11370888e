//
//  IMYSurveyModel.h
//  IMYMe
//
//  Created by HBQ on 2025/3/13.
//

#import <Foundation/Foundation.h>
#import "IMYSurveyQModel.h"
#import "IMYPopPlanModel.h"

typedef NS_ENUM(NSUInteger, IMYSurveyQListStyle) {
    /// 入口类型
    IMYSurveyQListStyle_Entrace,
    /// 弹窗类型
    IMYSurveyQListStyle_Dialog
};

@class IMYSurveyModel;

NS_ASSUME_NONNULL_BEGIN

typedef void (^IMSurveyClickOptionBlock)(IMYSurveyModel *surveyModel, IMYSurveyQModel *qModel, IMYSurveyOptionModel *optionModel);

typedef void (^IMSurveyModelBlock)(IMYSurveyModel* _Nullable surveyModel);

typedef void (^IMSurveyListDidScrollBlock)(UITableView *tableView);

@interface IMYSurveyModel : NSObject

@property (nonatomic, copy) NSString *code;

@property (nonatomic, copy) NSString *id;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, assign) NSInteger style;

/// 服务端字段：服务端下发的问题列表，用于查找要展示的问题
@property (nonatomic, copy) NSArray<IMYSurveyQModel *> *question_list;

/// 本地字段：用于渲染的问题列表
@property (nonatomic, copy) NSArray<IMYSurveyQModel *> *use_question_list;

/// 本地字段：这个问卷应该展示在信息流的第几楼，默认 0
@property (nonatomic, assign) NSInteger local_floor;

@property (nonatomic, copy) NSString *local_planId;

@property (nonatomic, copy) NSString *local_planBiz;

/// 是否负反馈
@property (nonatomic, assign) BOOL local_isNegative;

/// 本地字段：素材曝光要用
@property (nonatomic, strong) IMYPopPlanModel *local_planModel;

- (BOOL)needShow;

- (void)resetUseQuestionList;

- (void)clickQModel:(IMYSurveyQModel *)qModel
             option:(IMYSurveyOptionModel *)optionModel;

@end

NS_ASSUME_NONNULL_END
