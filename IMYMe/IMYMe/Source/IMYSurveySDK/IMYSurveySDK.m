//
//  IMYSurveySDK.m
//  IMYMe
//
//  Created by HBQ on 2025/3/13.
//

#import "IMYSurveySDK.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYSurveyAPI.h"
#import "IMYPopPlanManager.h"

IMY_KYLIN_FUNC_IDLE {
    [[NSNotificationCenter defaultCenter] addObserverForName:IMYApplicationOnUserClearCacheNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        NSLog(@"[IMYSurveySDK]移除所有接口缓存");
        [[IMYSurveySDK sharedInstance] removePlansAndSurveys];
    }];
}

@interface IMYSurveySDK ()

@end

@implementation IMYSurveySDK

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYSurveySDK *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
        [instance addNotification];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - public

- (IMYSurveyModel *)surveyModelForCode:(NSString *)code {
    @weakify(self);
    IMYPopPlanModel *planModel = [[IMYPopPlanManager sharedInstance] getPlanModelWithCode:code refreBlock:^(IMYPopPlanModel * _Nonnull refreshPlanModel) {
        @strongify(self);
        NSString *surveyId = [refreshPlanModel surveyId];
        [self requestSurveyWithId:surveyId cb:nil];
    }];
    
    NSString *surveyId = [planModel surveyId];
    IMYSurveyModel *surveyModel = [self getCahedSurveyModelWithSurveyId:surveyId planModel:planModel];
    
    if (!surveyModel) {
        NSString *planId = [NSString stringWithFormat:@"%ld", planModel.id];
        [self ppmLog:@"没有缓存的问卷信息" planId:planId surveyId:surveyId isCache:YES];
    }
    
    return surveyModel;
}

- (IMYSurveyEntranceView *)createEntranceViewWithMaxWidth:(CGFloat)maxWidth {
    return [IMYSurveyEntranceView entranceViewWithMaxWidth:maxWidth];
}

// MARK: - notification

- (void)addNotification {
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"NOTI_IMYSurveySDK_CLEAN" object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        [self removeAllCacheSurvey];
        [[IMYPopPlanManager sharedInstance] removeAllCache];
    }];
}

// MARK: - data: server data

- (void)requestSurveyWithId:(NSString *)surveyId
                         cb:(void (^)(NSError *error))cb {
    if (imy_isEmptyString(surveyId)) {
        return;
    }
    
    @weakify(self);
    [[IMYSurveyAPI sharedInstance] getSurveyDetailWithSurveyId:surveyId onSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        [self cacheSurvey:surveyId dict:dict];
    } onError:^(NSError * _Nonnull error) {
        
    }];
}

// MARK: - data: cache survey

- (void)cacheSurvey:(NSString *)surveyId dict:(NSDictionary *)dict {
    if (imy_isEmptyString(surveyId)) {
        return;
    }
    
    NSString *key = [self surveyKVKey:surveyId];
    [[IMYKV defaultKV] setDictionary:dict forKey:key];
}

- (IMYSurveyModel *)getCahedSurveyModelWithSurveyId:(NSString *)surveyId planModel:(IMYPopPlanModel *)planModel {
    if (imy_isEmptyString(surveyId)) {
        return nil;
    }
    
    IMYSurveyModel *surveyModel = nil;
    
    NSString *key = [self surveyKVKey:surveyId];
    NSDictionary *dict = [[IMYKV defaultKV] dictionaryForKey:key];
    
    surveyModel = [dict toModel:[IMYSurveyModel class]];
    if (surveyModel) {
        NSString *planId = [NSString stringWithFormat:@"%ld", planModel.id];
        NSString *planBiz = planModel.biz;
        
        surveyModel.local_planId = planId;
        surveyModel.local_planBiz = planBiz;
        surveyModel.local_floor = planModel.pos.floor;
        surveyModel.local_isNegative = [planModel isNegative];
        surveyModel.local_planModel = planModel;
        [surveyModel resetUseQuestionList];
    }
    
    return surveyModel;
}

- (NSString *)surveyKVKey:(NSString *)surveyId {
    return [NSString stringWithFormat:@"IMYSurveySDK-888-%@", surveyId];
}

// MARK: - data: debug

- (void)removeAllCacheSurvey {
    [[[IMYKV defaultKV] allKeys] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:@"IMYSurveySDK-888"]) {
            [[IMYKV defaultKV] removeForKey:obj];
        }
    }];
}

- (NSArray *)debugGetAllServey {
    NSMutableArray *dicts = [NSMutableArray array];
    
    NSString *kvKey = [NSString stringWithFormat:@"IMYSurveySDK-888-"];
    [[IMYKV defaultKV].allKeys enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:kvKey]) {
            NSDictionary *dict = [[IMYKV defaultKV] dictionaryForKey:obj];
            [dicts addObject:dict];
        }
    }];
    
    return dicts;
}

/// 移除接口缓存（用户清除缓存的时候移除所有接口缓存，不影响使用，后续继续缓存即可，不要移除曝光信息就行）
- (void)removePlansAndSurveys {
    [self removeAllCacheSurvey];
    [[IMYPopPlanManager sharedInstance] removePlanList];
}

// MARK: - Debug

- (void)ppmLog:(NSString *)log
        planId:(NSString *)planId
      surveyId:(NSString *)surveyId
       isCache:(BOOL)isCache {
    if (isCache) {
        NSString *log0 = [NSString stringWithFormat:@"[ppm][p %@ s %@][%@]", planId, surveyId, log];
        [self handleLog:log0];
    }
}

- (void)handleLog:(NSString *)log {
    NSLog(@"%@", log);
    
#ifdef DEBUG
    NSString *kvKey = @"#+IMYPopPlanManager-logArray";
    NSMutableArray *logArrayM = [NSMutableArray array];
    NSArray *logArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    if (logArray) {
        logArrayM = [logArray mutableCopy];
    }
    
    [logArrayM addObject:log];
    [[IMYKV defaultKV] setArray:logArrayM.copy forKey:kvKey];
#endif
}

@end
