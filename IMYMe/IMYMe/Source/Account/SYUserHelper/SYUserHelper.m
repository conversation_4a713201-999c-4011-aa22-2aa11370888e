//
//  SYUserHelper.m
//  Seeyou
//
//  Created by upin on 13-3-22.
//  Copyright (c) 2013年 linggan. All rights reserved.
//

#import "SYUserHelper.h"
#import <IMYBaseKit/IMYPublic.h>
#import <IMYBaseKit/IMYABTestManager.h>
#import <WatchConnectivity/WCSession.h>
#import <LKDBHelper/LKDBHelper.h>
#import "SYPublicFun.h"
#import "SYPublicFun+Setting.h"
#import "LKDBHelper+SY.h"
#import "SYUserHelper+Method.h"

#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#endif

#if __has_include(<IMYRecord/IMYRecordABTestManager.h>)
#import <IMYRecord/IMYRecordABTestManager.h>
#endif

static NSString *const SYUserDefaults = @"cu_default";

static SYUserHelper *instance;
static NSRecursiveLock *userHelperLock;

@interface SYUserHelper ()
@end

@implementation SYUserHelper
@synthesize userid = _userid;
@synthesize userModelType = _userModelType;
@synthesize pars_interval = _pars_interval;
@synthesize pars_menses_day = _pars_menses_day;
@synthesize baby_sex = _baby_sex;
@synthesize headImageURL = _headImageURL;
@synthesize nickname = _nickname;
@synthesize birth_year = _birth_year;

+ (void)sy_lock:(void (^)(void))block
{
    [userHelperLock lock];
    block();
    [userHelperLock unlock];
}

+ (void)initialize
{
    if (self == SYUserHelper.class) {
        userHelperLock = [[NSRecursiveLock alloc] init];
        [self setUserCalculateForPTN:@"NSDictionary"];
    }
}

+ (SYUserHelper *)sharedHelper
{
    if (instance == nil) {
        [self sy_lock:^{
            if (instance == nil) {
                if ([SYPublicFun getLoginType] != SYLoginTypeNone) {
                    [SYUserHelper setUserID:[SYPublicFun getUserID]];
                } else if ([SYPublicFun getVirtualSuccess]) {
                    [SYUserHelper setUserID:[SYPublicFun getVirtualUserID]];
                } else {
                    [SYUserHelper setUserID:nil];
                }
            }
        }];
    }
    return instance;
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (LKDBHelper *)getUsingLKDBHelper
{
    static LKDBHelper *dbHelper;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        dbHelper = [[LKDBHelper alloc] initWithDBName:@"recordDB"];
    });
    return dbHelper;
}

+ (NSString *)getTableName
{
    return @"cu_userInfo";
}
+ (NSString *)getPrimaryKey
{
    return @"userid";
}

- (NSString *)screen_name
{
    IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
    if ([vars boolForKey:@"default_nickname"]) {
        // 使用服务端下发的配置文案
        return [vars stringForKey:@"default_nickname_txt"];
    }
    if (imy_isEmptyString(_screen_name) && imy_isEmptyString(_nickname)) {
        return nil;
    }
    if (imy_isEmptyString(_screen_name)) {
        return _nickname;
    }
    return _screen_name;
}

+ (void)initWithUserID:(NSString *)userid
{
    [self sy_lock:^{
        [self initWithUserIDWithSafe:userid];
    }];
}
+ (void)initWithUserIDWithSafe:(NSString *)userid
{
    ///userid 不能为nil 因为数据库以userid当主键 当为null时 主键失去唯一值效果
    if (imy_isEmptyString(userid)) {
        userid = SYUserDefaults;
    }

    ///相同userid不切换
    if ([instance.userid isEqualToString:userid]) {
        return;
    }

    ///从数据库 读取数据
    NSString *where = [NSString stringWithFormat:@"userid = '%@'", userid];
    instance = [SYUserHelper searchSingleWithWhere:where orderBy:nil];
    if (instance == nil) {
        instance = [self alloc];
        instance = [instance init];
        instance.userid = userid;
    }
    instance.pars_is_sync = YES;
    [LKDBHelper setCurrentUserID:userid userHelper:instance];
}

+ (NSString *)fastUserId {
    if (instance) {
        // 已初始化完毕
        return [self sharedHelper].userid;
    } else {
        NSString *userid = nil;
        if ([SYPublicFun getLoginType] != SYLoginTypeNone) {
            userid = [SYPublicFun getUserID];
        } else if ([SYPublicFun getVirtualSuccess]) {
            userid = [SYPublicFun getVirtualUserID];
        }
        if (!userid.length) {
            userid = SYUserDefaults;
        }
        return userid;
    }
}

+ (void)setUserID:(NSString *)userid
{
    [self sy_lock:^{
        [self setUserIDFromMain:userid];
    }];
}
+ (SYUserHelper *)setUserIDFromMain:(NSString *)userid
{
    ///userid 不能为nil 因为数据库以userid当主键 当为null时 主键失去唯一值效果
    if (imy_isEmptyString(userid)) {
        userid = SYUserDefaults;
    }

    ///相同userid不切换
    if ([instance.userid isEqualToString:userid]) {
        return instance;
    }

    if (instance) {
        ///当前userid是默认值 并且要切换的 userid 在数据库中没有记录    那就把默认userid的值 给该userid
        if ([instance.userid isEqualToString:SYUserDefaults] && [self rowCountWithWhere:@"userid='%@'", userid] == 0) {
            instance.userid = userid;
            [LKDBHelper setCurrentUserID:userid userHelper:instance];

            [self insertToDB:instance];
            [self deleteWithWhere:@{@"userid": SYUserDefaults}];

            return instance;
        }
        ///退出到默认userid
        [instance exit_only];
    }

    [self initWithUserID:userid];

    return instance;
}

+ (SYUserModelType)usermodeFromPublic:(IMYVKUserMode)mode {
    switch (mode) {
        case IMYVKUserModeLama: {
            return SYUserModelTypeLama;
        } break;
        case IMYVKUserModeForPregnant: {
            return SYUserModelTypeForPregnant;
        } break;
        case IMYVKUserModePregnancy: {
            return SYUserModelTypePregnancy;
        } break;
        case IMYVKUserModeQinYou: {
            return SYUserModelTypeQinYou;
        } break;
        default: {
            return SYUserModelTypeNormal;
        } break;
    }
}

+ (IMYVKUserMode)modeFromUserModelType:(SYUserModelType)userModelType {
    switch (userModelType) {
        case SYUserModelTypeForPregnant: {
            return IMYVKUserModeForPregnant;
        } break;
        case SYUserModelTypePregnancy: {
            return IMYVKUserModePregnancy;
        } break;
        case SYUserModelTypeLama: {
            return IMYVKUserModeLama;
        } break;
        case SYUserModelTypeQinYou: {
            return IMYVKUserModeQinYou;
        } break;
        default: {
            return IMYVKUserModeNormal;
        } break;
    }
}

#pragma mark - userid
- (NSString *)userid 
{
    if (!_userid.length) {
        _userid = SYUserDefaults;
    }
    return _userid;
}

- (void)setUserid:(NSString *)userid
{
    @synchronized (self) {
        id oldUid = _userid;
        _userid = [userid copy];
        imy_asyncMainBlock(1, ^{
            [oldUid class];
        });
    }
}

#pragma mark -
- (void)exit_only
{
    [self.class insertToDB:self];
    instance = nil;

    [LKDBHelper setCurrentUserID:nil userHelper:instance];
}
- (void)exit
{
    [self exit_only];
    ///退出账号的时候 也发送用户id修改通知
    [SYUserHelper sendUserIDChangedEvent];
}

- (id)userGetValueForModel:(LKDBProperty *)property
{
    id value = [self valueForKey:property.propertyName];
    value = [value YYJSONString];

    return value;
}

- (void)userSetValueForModel:(LKDBProperty *)property value:(id)value
{
    value = [value imy_jsonObject];
    [self setValue:value forKey:property.propertyName];
}

#pragma mark - 经期信息
- (void)setPars_interval:(NSInteger)pars_interval
{
    if (_pars_interval == pars_interval)
        return;

    _pars_interval = pars_interval;
    _pars_is_sync = NO;
}

- (NSInteger)pars_interval
{
    if (_pars_interval <= 0) {
        self.pars_interval = 28;
    }
    return _pars_interval;
}

- (void)setPars_menses_day:(NSInteger)pars_menses_day
{
    if (_pars_menses_day == pars_menses_day)
        return;

    _pars_menses_day = pars_menses_day;
    _pars_is_sync = NO;
}

- (NSInteger)pars_menses_day
{
    if (_pars_menses_day <= 0) {
        self.pars_menses_day = 5;
    }
    return _pars_menses_day;
}

#pragma mark - 个人信息
- (void)setBMarry:(BOOL)bMarry
{
    if (_bMarry == bMarry)
        return;

    _bMarry = bMarry;
    _pars_is_sync = NO;
}

- (void)setHeigth:(float)heigth
{

    if (fabs(_heigth - heigth) < 0.000001)
        return;

    _heigth = heigth;
    _pars_is_sync = NO;
}

- (void)setTagetWeight:(float)tagetWeight
{

    if (fabs(_tagetWeight - tagetWeight) < 0.000001)
        return;

    _tagetWeight = tagetWeight;
    _pars_is_sync = NO;
}

- (void)setBirthday:(NSString *)birthday
{
    if ([birthday containsString:@"0000"]) {
        birthday = nil;
    }
    
    if (imy_isEmptyString(birthday) && imy_isEmptyString(_birthday)) {
        return;
    }

    if ([birthday compare:_birthday] == NSOrderedSame && [_birthday compare:birthday] == NSOrderedSame) {
        return;
    }

    _birthday = birthday;
    if (birthday.length >= 4) {
        //设置生日字段，要自动同步出生年份字段
        NSString *year = [birthday substringToIndex:4];
        NSInteger birth_year = [year integerValue];
        [self setBirth_year:birth_year];
    }
    [IMYPublicAppHelper shareAppHelper].userAge = 0;
    [[IMYPublicAppHelper getUserBirthdayChangedSubject] sendNext:birthday];
    _pars_is_sync = NO;
}

- (void)setBirth_year:(NSInteger)birth_year
{
    _birth_year = birth_year;
    _pars_is_sync = NO;
}

- (void)setNickname:(NSString *)nickname
{
    if (![nickname isKindOfClass:[NSString class]] ||
        (nickname && [nickname compare:_nickname] == NSOrderedSame)) {
        return;
    }
    _pars_is_sync = NO;
}

- (NSString *)nickname
{
    IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
    if ([vars boolForKey:@"default_nickname"]) {
        // 使用服务端下发的配置文案
        return [vars stringForKey:@"default_nickname_txt"];
    }
    if (imy_isEmptyString(_screen_name) && imy_isEmptyString(_nickname)) {
        return nil;
    }
    if (imy_isEmptyString(_nickname)) {
        return _screen_name;
    }
    return _nickname;
}

- (void)setAccountName:(NSString *)accountName
{
    if (imy_isEmptyString(accountName))
        return;

    if ([accountName compare:_accountName] == NSOrderedSame) {
        return;
    }

    _accountName = accountName;
    _pars_is_sync = NO;
}

- (void)setUserModelType:(SYUserModelType)userModelType
{
    if (_userModelType == userModelType) {
        return;
    }
    if (userModelType != SYUserModelTypeLama) {
        _baby_sex = 0;
    }

    _userModelType = userModelType;
    _pars_is_sync = NO;
}

- (SYUserModelType)userModelType
{
#ifdef DEBUG
    NSUserDefaults *ud = [NSUserDefaults standardUserDefaults];
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // 防止启动就闪退，这边修正下
        if ([ud boolForKey:@"#+ForceQinYouMode-TempCheck"]) {
            // 有闪退标记，这边不强制亲友
            [ud setObject:@0 forKey:@"#+ForceQinYouMode"];
        } else {
            // 10秒后移除标记位
            [ud setBool:YES forKey:@"#+ForceQinYouMode-TempCheck"];
            imy_asyncMainBlock(10, ^{
                [ud removeObjectForKey:@"#+ForceQinYouMode-TempCheck"];
            });
        }
    });
    NSNumber *forceQinYou = [ud objectForKey:@"#+ForceQinYouMode"];
    if (forceQinYou.boolValue) {
        return SYUserModelTypeQinYou;
    } else if (!forceQinYou) {
        [ud setObject:@0 forKey:@"#+ForceQinYouMode"];
    }
#endif
    if (_userModelType == SYUserModelTypeNone) {
        _userModelType = SYUserModelTypeNormal;
    }
    return _userModelType;
}

- (void)setIsSetUserModel:(BOOL)isSetUserModel
{
    if (_isSetUserModel == isSetUserModel) {
        return;
    }

    _isSetUserModel = isSetUserModel;
    _pars_is_sync = NO;
        if ([WCSession isSupported]) {
            [[WCSession defaultSession] transferUserInfo:@{ @"isSetUserModel": @(isSetUserModel) }];
        }
}

- (void)setContactQQ:(NSString *)contactQQ
{
    if (![contactQQ isKindOfClass:[NSString class]] ||
        ([contactQQ compare:_contactQQ] == NSOrderedSame && [_contactQQ compare:contactQQ] == NSOrderedSame)) {
        return;
    }

    _contactQQ = contactQQ;
    _pars_is_sync = NO;
}

- (void)setContactEmail:(NSString *)contactEmail
{
    if (![contactEmail isKindOfClass:[NSString class]] ||
        ([contactEmail compare:_contactEmail] == NSOrderedSame && [_contactEmail compare:contactEmail] == NSOrderedSame)) {
        return;
    }

    _contactEmail = contactEmail;
    _pars_is_sync = NO;
}

- (void)setCity:(NSString *)city
{
    if (![city isKindOfClass:[NSString class]] ||
        ([city compare:_city] == NSOrderedSame && [_city compare:city] == NSOrderedSame)) {
        return;
    }

    _city = city;
    _pars_is_sync = NO;
}

- (void)setBabyBirthday:(NSString *)babyBirthday
{
    if ([babyBirthday containsString:@"0000"]) {
        babyBirthday = nil;
    }
    if (![babyBirthday isKindOfClass:[NSString class]] ||
        ([babyBirthday compare:_babyBirthday] == NSOrderedSame && [_babyBirthday compare:babyBirthday] == NSOrderedSame)) {
        return;
    }

    _babyBirthday = babyBirthday;
    _pars_is_sync = NO;
}

- (void)setBaby_sex:(NSInteger)baby_sex
{
    _baby_sex = baby_sex;
    _pars_is_sync = NO;
}

- (void)setHeadImageFileName:(NSString *)headImageFileName
{
    if ([_headImageFileName isEqualToString:headImageFileName]) {
        return;
    }
    if (imy_isNotEmptyString(headImageFileName)) {
        _headImageURL = nil;
    }
    _headImageFileName = headImageFileName;
    if ([headImageFileName containsString:@"://"]) {
        _headImageURL = headImageFileName;
    }
    _pars_is_sync = NO;
}

/// me 接口，只获取随机头像，设置的时候不同步服务端
- (void)noSyncSetHeadImageFileName:(NSString *)headImageFileName
{
    if ([_headImageFileName isEqualToString:headImageFileName]) {
        return;
    }
    if (imy_isNotEmptyString(headImageFileName)) {
        _headImageURL = nil;
    }
    _headImageFileName = headImageFileName;
    if ([headImageFileName containsString:@"://"]) {
        _headImageURL = headImageFileName;
    }
}

- (void)setHospital:(NSString *)hospital
{
    if (![hospital isKindOfClass:[NSString class]] ||
        ([hospital compare:_hospital] == NSOrderedSame && [_hospital compare:hospital] == NSOrderedSame)) {
        return;
    }

    _hospital = hospital;
    _pars_is_sync = NO;
}

- (void)setHospitalId:(NSInteger)hospitalId
{
    if (hospitalId == _hospitalId) {
        return;
    }

    _hospitalId = hospitalId;
    _pars_is_sync = NO;
}

- (void)setHeadImageURL:(NSString *)headImageURL
{
    if (imy_isNotEmptyString(headImageURL) && [headImageURL containsString:@"://"]) {
        _headImageURL = headImageURL;
    } else {
        if (!_headImageFileName) {
            _headImageFileName = headImageURL;
        }
        _headImageURL = nil;
    }
}

- (NSString *)headImageURL
{
    IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"disable_operation"];
    if ([vars boolForKey:@"default_avatar"]) {
        // 使用服务端下发的配置头像URL
        return [vars stringForKey:@"default_avatar_pic"];
    }
    if (imy_isNotEmptyString(_headImageURL)) {
        return _headImageURL;
    } else if (imy_isNotEmptyString(_headImageFileName)) {
#ifdef DEBUG
        // 测试环境, 头像默认地址 使用 test-sc 当做 domain
        if ([IMYURLEnvironmentManager currentType] == IMYURLEnviromentTypeTest) {
            return [NSString stringWithFormat:@"%@/%@", @"https://test-sc.seeyouyima.com", _headImageFileName];
        }
#endif
        return [NSString stringWithFormat:@"%@/%@", sc_seeyouyima_com, _headImageFileName];
    } else {
        return nil;
    }
}

- (SYUserModelType)s_user_ModelType
{
    SYUserModelType type = self.userModelType;
#if __has_include(<IMYRecord/IMYRecord.h>)
    //亲友模式不走修复逻辑，直接 return || 亲友模式下 不做身份兼容处理
    if (type == SYUserModelTypeQinYou || [IMYPregnanceModel getQinyouPregnanceModel]) {
        return type;
    }
    BOOL isPregance = [IMYDayRecordModel dateInsidePregnance_for294:[NSDate imy_today]];
    if (type == SYUserModelTypePregnancy && isPregance == NO) {
        type = SYUserModelTypeNormal;
    } else if (isPregance) {
        BOOL isTodayEnd = NO;
        NSDate *endDate = [IMYPregnanceModel getLastPregnancyModel].endDate;
        if (endDate) {
            isTodayEnd = [endDate isEqualToDate:[NSDate imy_today]];
        }
        if (isTodayEnd) {
            if (type == SYUserModelTypePregnancy) {
                type = SYUserModelTypeLama;
            }
        } else {
            type = SYUserModelTypePregnancy;
        }
    }
#endif
    return type;
}

- (NSString *)s_userModelTypeString
{
    switch ([SYUserHelper sharedHelper].s_user_ModelType) {
        case SYUserModelTypeLama:
            return IMYString(@"我在育儿");
        case SYUserModelTypePregnancy:
            return IMYString(@"我怀孕了");
        case SYUserModelTypeForPregnant:
            return IMYString(@"我在备孕");
        case SYUserModelTypeQinYou:
            return IMYString(@"亲友模式");
        default:
            return IMYString(@"只记经期");
    }
}


/// 辣妈身份名称ab实验,  -- 785版本
/// 辣妈身份下的名称 实验组"我在育儿",控制组:"我是辣妈"
- (NSString *)lamaModeNameStyleString
{
    if ([self isLamaNewNameStyle]) {
        return IMYString(@"我在育儿");
    } else {
        return IMYString(@"我是辣妈");
    }
}

/// 辣妈身份名称ab实验,其他身份复用 `s_userModelTypeString`  -- 785版本
///https://test-admin-ab.meiyou.com/ab/experiment/detail?id=1024
///实验别名（key）: mode_mother_name
///实验变量：mode_name_style
///控制组：exp 1024-2585，mode_name_style = 1 我是辣妈
///实验组：exp 1024-2586 ，mode_name_style = 2  我在育儿
- (BOOL)isLamaNewNameStyle
{
    //8.0.8 需求要求忽略实验 https://www.tapd.cn/21039721/prong/stories/view/1121039721001100269
    return YES;
}

- (void)downloadUserSettings:(void (^)(BOOL success))completion {
    if (![IMYPublicAppHelper shareAppHelper].userid.imy_isPositiveInt) {
        // 用户id还未获得，无法进行数据同步
        if (completion) {
            imy_asyncMainBlock(^{
                completion(NO);
            });
        }
        return;
    }
    [[[IMYServerRequest getPath:@"usersettings" host:users_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *response) {
        // 有值才进行设置
        id personalRecommand = response.responseObject[@"open_person_recom"];
        if (personalRecommand) {
            [SYPublicFun setPersonalRecommand:[personalRecommand boolValue]];
        }
        
        id app_mode = response.responseObject[@"app_mode"];
        if (app_mode) {
            NSInteger isJiJianMode = [app_mode integerValue] == 2 ? YES : NO;
#if __has_include(<IMYRecord/IMYRecordABTestManager.h>)
            BOOL isJiJian = [IMYRecordABTestManager isJiJianMode];
            if (isJiJian != isJiJianMode) { //有更变 才需要重新设置值
                [IMYRecordABTestManager settingJiJianModeAndPostNot:isJiJianMode];
            }
#endif
        }
        /// 设置页是否展示亲友模式 1 展示 2不展示
        self.isShowQyForSetting = [response.responseObject[@"is_show_qy"] integerValue] == 1 ? YES : NO;
        
        id qpOpen = response.responseObject[@"quick_protection"];
        id qpDate = response.responseObject[@"quick_protection_date"];
        if (qpOpen && imy_isNotBlankString(qpDate)) {
            [SYPublicFun setQuickProtection:([qpOpen integerValue] == 1)];
            [SYPublicFun setQuickProtectionEndDateString:qpDate];
        }
        
        IMYPrivateMsgSettingType privateMsgSettingType = IMYPrivateMsgSettingType_Follow;
        NSNumber *private_letter_scope = response.responseObject[@"private_letter_scope"];
        if (private_letter_scope.integerValue == 1) {
            privateMsgSettingType = IMYPrivateMsgSettingType_Forbid;
        }
        [SYPublicFun setPrivateMsgSetting:privateMsgSettingType];
        
        // 不限制身份
        if (YES) {
            NSNumber *youthModelNumber = response.responseObject[@"is_youth_mode"];
            const BOOL oldState = [SYPublicFun isYouthMode];
            // 以服务端为准，非 1 2 不设置
            if (youthModelNumber != nil) {
                if ([youthModelNumber integerValue] == 1) {
                    // 非经期强制关闭青少年模式，并上报新值给服务端
                    //  https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001233578
                    if (IMYPublicAppHelper.shareAppHelper.userMode != IMYVKUserModeNormal) {
                        [SYPublicFun setIsYouthModeEnable:NO];
                        [SYPublicFun setChangedYouthMode:YES];
                        // 同步给服务端
                        [[SYUserHelper sharedHelper] uploadUserSettings:nil];
                    } else {
                        // 经期身份保持老逻辑
                        [SYPublicFun setIsYouthModeEnable:YES];
                    }
                } else if ([youthModelNumber integerValue] == 2) {
                    [SYPublicFun setIsYouthModeEnable:NO];
                }
            }
            const BOOL nowState = [SYPublicFun isYouthMode];
            if (nowState != oldState) {
                //[[NSNotificationCenter defaultCenter] postNotificationName:@"IMYYouthModeStateChanged" object:@(nowState)];
#ifdef DEBUG
                imy_asyncMainBlock(2, ^{
                    NSString *tip = [NSString stringWithFormat:IMYString(@"青少年模式: %@"), (nowState ? IMYString(@"开") : IMYString(@"关"))];
                    [UIWindow imy_showTextHUD:tip];
                });
#endif
            }
        }
        
        if (completion) {
            completion(YES);
        }
    } error:^(NSError *error) {
        if (completion) {
            completion(NO);
        }
    }];
}

- (void)uploadUserSettings:(void (^)(BOOL success, NSError *error))completion {
    [self uploadUserSettings:NO saveUserData:YES completion:completion];
}

- (void)uploadUserSettings:(BOOL)recommandSetting
              saveUserData:(BOOL)saveUserData
                completion:(void (^)(BOOL success, NSError *error))completion {
    if (recommandSetting) {
        // 立即请求
        [self delay_uploadUserSettings:recommandSetting
                          saveUserData:saveUserData
                            completion:completion];
    } else {
        // 防止大量并发请求
        imy_throttle(1, ^{
            [self delay_uploadUserSettings:NO
                              saveUserData:NO
                                completion:completion];
        });
    }
}

- (void)delay_uploadUserSettings:(BOOL const)recommandSetting
                    saveUserData:(BOOL const)saveUserData
                      completion:(void (^)(BOOL success, NSError *error))completion {
    
    if (![IMYPublicAppHelper shareAppHelper].userid.imy_isPositiveInt) {
        // 用户id还未获得，无法进行数据同步
        if (completion) {
            imy_asyncMainBlock(^{
                NSError *error = [NSError errorWithDomain:@"user nologin!" code:NSURLErrorCancelled userInfo:nil];
                completion(NO, error);
            });
        }
        return;
    }
    
    // 数据逻辑
    NSInteger weakFirstDay = 0;
    if ([SYPublicFun getWeekFirstDay] == 1) {
        weakFirstDay = 1;
    } else {
        weakFirstDay = 7;
    }

    NSInteger remindBell;
    NSString *str = [SYPublicFun notifyRingName];
    if ([str compare:IMYString(@"风铃")] == NSOrderedSame) {
        remindBell = 2;
    } else if ([str compare:IMYString(@"竖琴")] == NSOrderedSame) {
        remindBell = 3;
    } else if ([str compare:IMYString(@"钟琴")] == NSOrderedSame) {
        remindBell = 4;
    } else if ([str compare:IMYString(@"系统默认")] == NSOrderedSame) {
        remindBell = 1;
    } else {
        remindBell = 1;
    }


    NSMutableDictionary *bodyDic = [[NSMutableDictionary alloc] init];
    bodyDic[@"is_auto_download_image_wifi"] = @([SYPublicFun getWifiLoadPic]);
    bodyDic[@"firstday_of_week"] = @([SYPublicFun getWeekFirstDay]);
    bodyDic[@"reminder_bell"] = @(remindBell);
    if (imy_isNotEmptyString([SYPublicFun getSecretKey])) {
        bodyDic[@"lock_password"] = [SYPublicFun getSecretKey];
    }
    bodyDic[@"open_person_recom"] = @([SYPublicFun personalRecommand]);
    bodyDic[@"open_person_ad"] = [SYPublicFun openPersonAD] ? @1 : @2;
    bodyDic[@"open_person_eb_recom"] = [SYPublicFun openPersonEBRecommend] ? @1 : @2;
    bodyDic[@"open_person_search_recom"] = [SYPublicFun openPersonSearchRecommend] ? @1 : @2;
    
    BOOL const hasQuickChange = [SYPublicFun hasChangeQuickProtection];
    if (hasQuickChange) {
        bodyDic[@"quick_protection"] = [SYPublicFun isQuickProtection] ? @1 : @2;
    }
    
    if (@available(iOS 14, *)) {
        bodyDic[@"idfa_status"] = @([UIDevice imy_idfaAuthorizationStatus]);
    }
    
    // 非经期强制关闭青少年模式，并上报新值给服务端
    //  https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001233578
    if (IMYPublicAppHelper.shareAppHelper.userMode != IMYVKUserModeNormal && [SYPublicFun isYouthMode]) {
        [SYPublicFun setIsYouthModeEnable:NO];
        [SYPublicFun setChangedYouthMode:YES];
    }
    
    BOOL const hasYouthChange = [SYPublicFun hasChangeYouthMode];
    if (hasYouthChange) {
        //青少年模式 1.开启 2关闭
        bodyDic[@"is_youth_mode"] = [SYPublicFun isYouthMode] ? @(1) : @(2);
    }

    if (recommandSetting) {
        bodyDic[@"is_save_person_recom_data"] = @(saveUserData);
    }
    
#if __has_include(<IMYRecord/IMYRecordABTestManager.h>)
    if ([IMYRecordABTestManager hasChangeJiJianMode]) {
        bodyDic[@"app_mode"] = [IMYRecordABTestManager isJiJianMode] ? @(2) : @(1);
    }
#endif
    
    [bodyDic imy_setNonNilObject:@([SYPublicFun getPrivateMsgSetting]) forKey:@"private_letter_scope"];

    NSLog(@"put usersettings:%@", bodyDic);
    
    [[IMYServerRequest putPath:@"usersettings" host:users_seeyouyima_com params:bodyDic headers:nil] subscribeNext:^(IMYHTTPResponse *response) {
        [self setIsUploadUsersettingFail:NO];
        if (hasQuickChange) {
            [SYPublicFun setChangedQuickProtection:NO];
        }
        if (hasYouthChange) {
            [SYPublicFun setChangedYouthMode:NO];
        }
        if (completion) {
            imy_asyncMainBlock(^{
                completion(YES, nil);
            });
        }
    } error:^(NSError *error) {
        [self setIsUploadUsersettingFail:YES];
        if (completion) {
            imy_asyncMainBlock(^{
                completion(NO, error);
            });
        }
    }];
}

- (BOOL)isUploadUsersettingFail {
    NSString *key = [NSString stringWithFormat:@"isUploadUsersettingFail_%@", [IMYPublicAppHelper shareAppHelper].userid];
    return [[IMYUserDefaults standardUserDefaults] boolForKey:key];
}

- (void)setIsUploadUsersettingFail:(BOOL const)isUploadUsersettingFail {
    NSString *key = [NSString stringWithFormat:@"isUploadUsersettingFail_%@", [IMYPublicAppHelper shareAppHelper].userid];
    [[IMYUserDefaults standardUserDefaults] setBool:isUploadUsersettingFail forKey:key];
}

- (void)asyncSaveToDB {
    NSString *queueKey = [NSString stringWithFormat:@"SYUserHelper.AsyncSaveToDB.%p", self];
    [NSObject imy_asyncBlock:^{
        [self saveToDB];
    } onQueue:dispatch_get_global_queue(0, 0) afterSecond:0 forKey:queueKey];
}

@end
