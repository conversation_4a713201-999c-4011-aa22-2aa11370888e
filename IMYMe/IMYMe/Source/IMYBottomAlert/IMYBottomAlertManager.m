//
//  IMYBottomAlertManager.m
//  IMYMe
//
//  Created by HBQ on 2025/9/4.
//

#import "IMYBottomAlertManager.h"
#import <IMYMe/IMYPopplanV2Api.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYPopplanV2PlanModel.h"
#import "IMYBottomAlertViewV1.h"
#import "IMYBottomAlertViewV2.h"
#import "IMYBottomAlertViewV3.h"
#import "IMYAlertShowManager.h"
#import "IMYAlertShowManager+MeetYouAdapter.h"
#import "IMYAlertShowAutoRobot.h"

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    imy_asyncMainBlock(^{
        [[IMYBottomAlertManager sharedInstance] runBottomAlert];
    });
}

@implementation IMYBottomAlertManager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYBottomAlertManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - 暂不使用弹窗管理器弹窗（和安卓保持一致）

- (void)runBottomAlert {
    if (![IMYAlertShowManager canShowWithADStatus]) {
        imy_asyncMainBlock(1.0, ^{
            [self runBottomAlert];
        });
        return;
    }
    
    @weakify(self);
    
    [[IMYPopplanV2Api sharedInstance] getPlansWithCode:@"homepage-bottomsheet" onSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *plans = dict[@"list"];
        NSArray *planModels = [plans toModels:[IMYPopplanV2PlanModel class]];
        IMYPopplanV2PlanModel *planModel = planModels.firstObject;
        IMYPopplanV2MaterialModel *useMaterialModel = [planModel.materials firstObject];
        IMYPopplanV2MaterialItem5 *useItem = useMaterialModel.item5List.firstObject;
        [self showAlertWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 无可用弹窗，不用加入弹窗队列"];
#endif
    }];
}

- (void)showAlertWithPlanModel:(IMYPopplanV2PlanModel *)planModel
              useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                       useItem:(IMYPopplanV2MaterialItem5 *)useItem {
    if (![IMYAlertShowManager canShowWithOtherAlerts]) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 存在其他弹窗，直接放弃"];
#endif
        return;
    }
    
    if (!planModel || !useMaterialModel || !useItem) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 无可用弹窗，直接放弃"];
#endif
        return;
    }
    
    if (useItem.template <= 0 || useItem.template >= 4) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 未知模板，直接放弃"];
#endif
        return;
    }
    
    BOOL isHomePage = YES;
    id<IOCAppMainTabVC> rootVC = IMYHIVE_BINDER(IOCAppMainTabVC);
    if (!rootVC.isViewDidAppear) {
        isHomePage = NO;
    } else {
        const SYTabBarIndexType tabIndexType = rootVC.selectedTabIndexType;
        UINavigationController *navVC = [rootVC getRootVCWithTabIndexType:tabIndexType];
        const BOOL inTabHomePage = (navVC.viewControllers.count == 1);
        if (!inTabHomePage || !tabIndexType == SYTabBarIndexTypeHome) {
            isHomePage = NO;
        }
    }
    if (!isHomePage) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 不在首页，直接放弃"];
#endif
        return;
    }
    
    // 前置检查通过，显示弹窗
    IMYBottomAlertViewBase *alertView = nil;
    if (useItem.template == 1) {
        alertView = [IMYBottomAlertViewV1 new];
        [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    } else if (useItem.template == 2) {
        alertView = [IMYBottomAlertViewV2 new];
        [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    } else if (useItem.template == 3) {
        alertView = [IMYBottomAlertViewV3 new];
        [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    }
    [alertView show];
}

// MARK: - 使用弹窗管理器弹窗

- (void)robotRunBottomAlert {
    @weakify(self);
    [[IMYPopplanV2Api sharedInstance] getPlansWithCode:@"homepage-bottomsheet" onSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *plans = dict[@"list"];
        NSArray *planModels = [plans toModels:[IMYPopplanV2PlanModel class]];
        IMYPopplanV2PlanModel *planModel = planModels.firstObject;
        IMYPopplanV2MaterialModel *useMaterialModel = [planModel.materials firstObject];
        IMYPopplanV2MaterialItem5 *useItem = useMaterialModel.item5List.firstObject;
        [self robotShowAlertWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 无可用弹窗，不用加入弹窗队列"];
#endif
    }];
}

- (void)robotShowAlertWithPlanModel:(IMYPopplanV2PlanModel *)planModel
                   useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                            useItem:(IMYPopplanV2MaterialItem5 *)useItem {
    if (!planModel || !useMaterialModel || !useItem) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 无可用弹窗，不用加入弹窗队列"];
#endif
        return;
    }
    
    if (useItem.template <= 0 || useItem.template >= 4) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"homepage-bottomsheet 未知模板，不用加入弹窗队列"];
#endif
        return;
    }
    
    @weakify(self);
    
    IMYAlertShowAutoRobot *robot = [IMYAlertShowAutoRobot createRobotWithKey:@"IMYBottomAlertManager-robot"];
    if (robot.isReady) {
        return;
    }
    robot.Priority(-10000).OnlyFirstly().TabHomeType(SYTabBarIndexTypeHome);
    robot.CreateRealAlertView(^id<IMYAlertShowViewProtocol> _Nonnull{
        @strongify(self);
        IMYBottomAlertViewBase *alertView = nil;
        if (useItem.template == 1) {
            alertView = [IMYBottomAlertViewV1 new];
            [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
        } else if (useItem.template == 2) {
            alertView = [IMYBottomAlertViewV2 new];
            [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
        } else if (useItem.template == 3) {
            alertView = [IMYBottomAlertViewV3 new];
            [alertView configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
        }
        return alertView;
    }).Ready();
}

@end
