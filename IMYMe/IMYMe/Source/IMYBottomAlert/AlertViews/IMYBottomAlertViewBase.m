//
//  IMYBottomAlertViewBase.m
//  IMYMe
//
//  Created by HBQ on 2025/9/8.
//

#import "IMYBottomAlertViewBase.h"

@interface IMYBottomAlertViewBase ()

@property (nonatomic, strong) IMYPopplanV2PlanModel *planModel;
@property (nonatomic, strong) IMYPopplanV2MaterialModel *useMaterialModel;
@property (nonatomic, strong) IMYPopplanV2MaterialItem5 *useItem;

@end

@implementation IMYBottomAlertViewBase

- (void)configWithPlanModel:(IMYPopplanV2PlanModel *)planModel
           useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                    useItem:(IMYPopplanV2MaterialItem5 *)useItem {
    _planModel = planModel;
    _useMaterialModel = useMaterialModel;
    _useItem = useItem;
}

- (void)show {
    if (self.superview != nil) {
        return;
    }
    
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [window addSubview:self];

    if (self.onShow) {
        self.onShow();
    }
}

- (void)dismiss {
    if (self.onDismiss) {
        self.onDismiss();
    }
}

@end
