//
//  IMYBottomAlertViewBase.h
//  IMYMe
//
//  Created by HBQ on 2025/9/8.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYPopplanV2PlanModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface IMYBottomAlertViewBase : UIView

@property (nonatomic, strong, readonly) IMYPopplanV2PlanModel *planModel;
@property (nonatomic, strong, readonly) IMYPopplanV2MaterialModel *useMaterialModel;
@property (nonatomic, strong, readonly) IMYPopplanV2MaterialItem5 *useItem;

@property (nonatomic, copy) void (^onShow)(void);
@property (nonatomic, copy) void (^onDismiss)(void);

- (void)configWithPlanModel:(IMYPopplanV2PlanModel *)planModel
           useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                    useItem:(IMYPopplanV2MaterialItem5 *)useItem;

- (void)show;

- (void)dismiss;

@end

NS_ASSUME_NONNULL_END
