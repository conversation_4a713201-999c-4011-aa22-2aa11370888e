//
//  IMYBottomAlertViewV3.m
//  IMYMe
//
//  Created by HBQ on 2025/9/4.
//

#import "IMYBottomAlertViewV3.h"

@interface IMYBottomAlertViewV3 ()

@property (nonatomic, strong) UIView *myMaskView;

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *contentImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) IMYCapsuleButton *mainButton;
@property (nonatomic, strong) IMYButton *secondaryButton;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@end

@implementation IMYBottomAlertViewV3

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [self setupSubviews];
    }
    return self;
}

// MARK: - Setup

- (void)setupSubviews {
    [self addSubview:self.myMaskView];
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.contentImageView];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.subTitleLabel];
    [self.contentView addSubview:self.mainButton];
    [self.contentView addSubview:self.secondaryButton];
    [self.contentView addSubview:self.closeButton];
}

- (void)refreshSubviews {
    NSString *contentImage = self.useItem.img_light;
    if (IMYPublicAppHelper.shareAppHelper.isNight) {
        contentImage = self.useItem.img_dark;
    }
    BOOL isShowContentImage = imy_isNotEmptyString(contentImage);
    CGSize contentImageSize = [contentImage imy_lastComponentImageSizeByDefault:CGSizeMake(SCREEN_WIDTH, 64)];
    
    NSString *title = self.useItem.text_title;
    BOOL isShowTitle = imy_isNotEmptyString(title);
    
    NSString *subTitle = self.useItem.text_desc;
    BOOL isShowSubTitle = imy_isNotEmptyString(subTitle);
    
    NSString *mainButtonTitle = self.useItem.main_button_text;
    BOOL isShowMainButton = imy_isNotEmptyString(mainButtonTitle);
    
    NSString *secondaryButtonTitle = self.useItem.sub_button_text;
    BOOL isShowSecondaryButton = imy_isNotEmptyString(secondaryButtonTitle);
    NSString *secondaryButtonImage = self.useItem.sub_button_icon;
    BOOL isShowSecondaryButtonImage = imy_isNotEmptyString(secondaryButtonImage);
    
    // 赋值
    [self.contentImageView imy_setImageURL:contentImage];
    self.titleLabel.text = title;
    if (imy_isNotEmptyString(subTitle)) {
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.lineSpacing = 4;
        paragraphStyle.alignment = NSTextAlignmentCenter;
        paragraphStyle.lineBreakMode = NSLineBreakByTruncatingTail;
        NSDictionary *attributes = @{
            NSFontAttributeName: self.subTitleLabel.font,
            NSForegroundColorAttributeName: self.subTitleLabel.textColor,
            NSParagraphStyleAttributeName: paragraphStyle
        };
        self.subTitleLabel.attributedText = [[NSAttributedString alloc] initWithString:subTitle attributes:attributes];
    } else {
        self.subTitleLabel.attributedText = nil;
        self.subTitleLabel.text = subTitle;
    }
    [self.mainButton setTitle:mainButtonTitle forState:UIControlStateNormal];
    [self.secondaryButton setTitle:secondaryButtonTitle forState:UIControlStateNormal];
    if (isShowSecondaryButtonImage) {
        [self.secondaryButton sd_setImageWithURL:[NSURL URLWithString:secondaryButtonImage] forState:UIControlStateNormal];
        [self.secondaryButton setImageAtDirection:IMYDirectionLeft];
        [self.secondaryButton setOffset:4];
    } else {
        [self.secondaryButton sd_setImageWithURL:nil forState:UIControlStateNormal];
        [self.secondaryButton setOffset:0];
    }
    
    self.myMaskView.frame = self.bounds;
    self.contentView.frame = self.bounds;

    // 从顶部布局
    CGFloat cursorY = 0;
    self.contentImageView.hidden = !isShowContentImage;
    if (isShowContentImage) {
        CGFloat aspectRatio = contentImageSize.width / contentImageSize.height;
        CGSize imageSize = CGSizeZero;
        if (aspectRatio > 0) {
            imageSize = CGSizeMake(self.imy_width, self.imy_width / aspectRatio);
        }
        self.contentImageView.frame = CGRectMake(0, 0, imageSize.width, imageSize.height);
        self.contentImageView.imy_top = cursorY;
        self.contentImageView.imy_left = 0.0;
        
        cursorY = self.contentImageView.imy_bottom;
    }
    self.titleLabel.hidden = !isShowTitle;
    if (isShowTitle) {
        cursorY = cursorY + 20;
        
        [self.titleLabel sizeToFit];
        self.titleLabel.frame = CGRectMake(36, cursorY, self.imy_width - 72, 26);
        
        cursorY = self.titleLabel.imy_bottom;
    }
    self.subTitleLabel.hidden = !isShowSubTitle;
    if (isShowSubTitle) {
        cursorY = cursorY + 8;
        
        CGSize subTitleSize = [self.subTitleLabel sizeThatFits:CGSizeMake(self.imy_width - 32, CGFLOAT_MAX)];
        self.subTitleLabel.imy_size = CGSizeMake(SCREEN_WIDTH - 32, subTitleSize.height);
        self.subTitleLabel.imy_top = cursorY;
        self.subTitleLabel.imy_left = 16;
        
        cursorY = self.subTitleLabel.imy_bottom;
    }
    self.mainButton.hidden = !isShowMainButton;
    if (isShowMainButton) {
        cursorY = cursorY + 20;
        
        self.mainButton.frame = CGRectMake(12, cursorY, self.imy_width - 24, 48);
        
        cursorY = self.mainButton.imy_bottom;
    }
    self.secondaryButton.hidden = !isShowSecondaryButton;
    if (isShowSecondaryButton) {
        cursorY = cursorY + 12;
        
        [self.secondaryButton sizeToFit];
        self.secondaryButton.imy_top = cursorY;
        self.secondaryButton.imy_centerX = self.imy_width / 2.0;
        
        cursorY = self.secondaryButton.imy_bottom;
    }
    
    cursorY = cursorY + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    self.contentView.frame = CGRectMake(0, 0, self.imy_width, cursorY);
    self.contentView.imy_bottom = self.imy_height;
    
    // 其他布局
    self.closeButton.frame = CGRectMake(0, 0, 20, 20);
    self.closeButton.imy_top = 12;
    self.closeButton.imy_right = self.imy_width - 12;
}


// MARK: - Data

- (void)configWithPlanModel:(IMYPopplanV2PlanModel *)planModel
           useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                    useItem:(IMYPopplanV2MaterialItem5 *)useItem {
    [super configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    
    @weakify(self);
    [self imy_addThemeActionBlock:^(id weakObject) {
        @strongify(self);
        [self refreshSubviews];
    } forKey:@"configWithPlanModel"];
}

// MARK: - Actions

- (void)handleClickMaskView {
    [self dismiss];
}

- (void)handleClickCloseButton {
    [self dismiss];
}

- (void)handleClickMainButton {
    [self dismiss];
    
    imy_asyncMainBlock(^{
        [[IMYURIManager sharedInstance] runActionWithString:self.useItem.main_button_uri];
    });
}

- (void)handleClickSecondaryButton {
    [self dismiss];
    
    imy_asyncMainBlock(^{
        [[IMYURIManager sharedInstance] runActionWithString:self.useItem.sub_button_uri];
    });
}

// MARK: - IMYAlertShowViewProtocol

- (void)show {
    [super show];
    
    self.myMaskView.alpha = 0.0;
    self.contentView.imy_top = self.imy_height;
    [UIView animateWithDuration:0.3 animations:^{
        self.myMaskView.alpha = 1.0;
        self.contentView.imy_bottom = self.imy_height;
    }];
}

- (void)dismiss {
    [super dismiss];
    
    self.myMaskView.alpha = 1.0;
    self.contentView.imy_bottom = self.imy_height;
    [UIView animateWithDuration:0.3 animations:^{
        self.myMaskView.alpha = 0.0;
        self.contentView.imy_top = self.imy_height;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

// MARK: - Get

- (UIView *)myMaskView {
    if (!_myMaskView) {
        _myMaskView = [[UIView alloc] initWithFrame:self.bounds];
        _myMaskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
        @weakify(self);
        [_myMaskView bk_whenTapped:^{
            @strongify(self);
            [self handleClickMaskView];
        }];
    }
    return _myMaskView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:CGRectZero];
        [_contentView imy_setBackgroundColorForKey:kCK_White_AN];
        [_contentView imy_drawTopCornerRadius:12];
    }
    return _contentView;
}

- (IMYTouchEXButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [IMYTouchEXButton new];
        [_closeButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
            BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
            NSString *imageName;
            if (isNight) {
                imageName = @"icon_pop_close_yejian";
            } else {
                imageName = @"icon_pop_close";
            }
            [weakObject imy_setImage:imageName];
        }];
        [_closeButton addTarget:self action:@selector(handleClickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [IMYBottomAlertViewV3 titleFont];
        [_titleLabel imy_setTextColorForKey:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.numberOfLines = 1;
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [UILabel new];
        _subTitleLabel.font = [IMYBottomAlertViewV3 subTitleFont];
        [_subTitleLabel imy_setTextColorForKey:kCK_Black_M];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
        _subTitleLabel.numberOfLines = 2;
    }
    return _subTitleLabel;
}

- (UIImageView *)contentImageView {
    if (!_contentImageView) {
        _contentImageView = [UIImageView new];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFit;
        _contentImageView.clipsToBounds = YES;
    }
    return _contentImageView;
}

- (IMYCapsuleButton *)mainButton {
    if (!_mainButton) {
        _mainButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 48)];
        _mainButton.type = IMYButtonTypeFillRed;
        _mainButton.titleLabel.font = [IMYBottomAlertViewV3 mainButtonFont];
        [_mainButton imy_setTitle:IMYString(@"")];
        [_mainButton addTarget:self action:@selector(handleClickMainButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _mainButton;
}

- (IMYButton *)secondaryButton {
    if (!_secondaryButton) {
        _secondaryButton = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
        _secondaryButton.titleLabel.font = [IMYBottomAlertViewV3 secondaryButtonFont];
        [_secondaryButton setTitleColor:[IMYColor colorWithNormal:kCK_Black_M]];
        [_secondaryButton imy_setBackgroundColorForKey:kIMYClearColorKey];
        [_secondaryButton addTarget:self action:@selector(handleClickSecondaryButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _secondaryButton;
}

+ (UIFont *)titleFont {
    return [UIFont systemFontOfSize:19 weight:UIFontWeightMedium];
}

+ (UIFont *)subTitleFont {
    return [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
}

+ (UIFont *)mainButtonFont {
    return [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
}

+ (UIFont *)secondaryButtonFont {
    return [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
}

@end
