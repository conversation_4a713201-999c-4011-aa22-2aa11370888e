//
//  IMYBottomAlertViewV1.m
//  IMYMe
//
//  Created by HBQ on 2025/9/4.
//

#import "IMYBottomAlertViewV1.h"

@interface IMYBottomAlertViewV1 ()

@property (nonatomic, strong) UIView *myMaskView;

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *contentImageView;
@property (nonatomic, strong) IMYCapsuleButton *mainButton;
@property (nonatomic, strong) IMYButton *secondaryButton;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@end

@implementation IMYBottomAlertViewV1

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [self setupSubviews];
    }
    return self;
}

// MARK: - Setup

- (void)setupSubviews {
    [self addSubview:self.myMaskView];
    
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.contentImageView];
    [self.contentView addSubview:self.mainButton];
    [self.contentView addSubview:self.secondaryButton];
    [self.contentView addSubview:self.closeButton];
}

- (void)refreshSubviews {
    NSString *contentImage = self.useItem.background_img_light;
    if (IMYPublicAppHelper.shareAppHelper.isNight) {
        contentImage = self.useItem.background_img_dark;
    }
    BOOL isShowContentImage = imy_isNotEmptyString(contentImage);
    CGSize contentImageSize = [contentImage imy_lastComponentImageSizeByDefault:CGSizeMake(SCREEN_WIDTH, 100)];
    
    NSString *mainButtonTitle = self.useItem.main_button_text;
    BOOL isShowMainButton = imy_isNotEmptyString(mainButtonTitle);
    
    NSString *secondaryButtonTitle = self.useItem.sub_button_text;
    BOOL isShowSecondaryButton = imy_isNotEmptyString(secondaryButtonTitle);
    NSString *secondaryButtonImage = self.useItem.sub_button_icon;
    BOOL isShowSecondaryButtonImage = imy_isNotEmptyString(secondaryButtonImage);
    
    // 赋值
    [self.contentImageView imy_setImageURL:contentImage];
    [self.mainButton setTitle:mainButtonTitle forState:UIControlStateNormal];
    [self.secondaryButton setTitle:secondaryButtonTitle forState:UIControlStateNormal];
    if (isShowSecondaryButtonImage) {
        [self.secondaryButton sd_setImageWithURL:[NSURL URLWithString:secondaryButtonImage] forState:UIControlStateNormal];
        [self.secondaryButton setImageAtDirection:IMYDirectionLeft];
        [self.secondaryButton setOffset:4];
    } else {
        [self.secondaryButton sd_setImageWithURL:nil forState:UIControlStateNormal];
        [self.secondaryButton setOffset:0];
    }
    
    self.myMaskView.frame = self.bounds;
    self.contentView.frame = self.bounds;
    
    // 从顶部布局
    CGFloat cursorY = 0;
    self.contentImageView.hidden = !isShowContentImage;
    if (isShowContentImage) {
        CGFloat aspectRatio = contentImageSize.width / contentImageSize.height;
        CGSize imageSize = CGSizeZero;
        if (aspectRatio > 0) {
            imageSize = CGSizeMake(self.imy_width, self.imy_width / aspectRatio);
        }
        self.contentImageView.frame = CGRectMake(0, 0, imageSize.width, imageSize.height);
        self.contentImageView.imy_top = cursorY;
        self.contentImageView.imy_left = 0.0;
        
        cursorY = self.contentImageView.imy_bottom;
    }
    
    self.contentView.frame = CGRectMake(0, 0, self.imy_width, cursorY);
    self.contentView.imy_bottom = self.imy_height;
    
    // 从底部布局
    CGFloat bottomCursorY = self.contentView.imy_height - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    self.secondaryButton.hidden = !isShowSecondaryButton;
    if (isShowSecondaryButton) {
        bottomCursorY = bottomCursorY - 12;
        
        [self.secondaryButton sizeToFit];
        self.secondaryButton.imy_bottom = bottomCursorY;
        self.secondaryButton.imy_centerX = self.imy_width / 2.0;
        
        bottomCursorY = self.secondaryButton.imy_top;
    }
    self.mainButton.hidden = !isShowMainButton;
    if (isShowMainButton) {
        bottomCursorY = bottomCursorY - 12;
        
        self.mainButton.frame = CGRectMake(12, bottomCursorY - 48, self.imy_width - 24, 48);
        
        bottomCursorY = self.mainButton.imy_top;
    }
    
    // 其他布局
    self.closeButton.frame = CGRectMake(0, 0, 20, 20);
    self.closeButton.imy_top = 12;
    self.closeButton.imy_right = self.imy_width - 12;
}

// MARK: - Data

- (void)configWithPlanModel:(IMYPopplanV2PlanModel *)planModel
           useMaterialModel:(IMYPopplanV2MaterialModel *)useMaterialModel
                    useItem:(IMYPopplanV2MaterialItem5 *)useItem {
    [super configWithPlanModel:planModel useMaterialModel:useMaterialModel useItem:useItem];
    
    @weakify(self);
    [self imy_addThemeActionBlock:^(id weakObject) {
        @strongify(self);
        [self refreshSubviews];
    } forKey:@"configWithPlanModel"];
}

// MARK: - Actions

- (void)handleClickMaskView {
    [self dismiss];
}

- (void)handleClickCloseButton {
    [self dismiss];
}

- (void)handleClickMainButton {
    [self dismiss];
    
    imy_asyncMainBlock(^{
        [[IMYURIManager sharedInstance] runActionWithString:self.useItem.main_button_uri];
    });
}

- (void)handleClickSecondaryButton {
    [self dismiss];
    
    imy_asyncMainBlock(^{
        [[IMYURIManager sharedInstance] runActionWithString:self.useItem.sub_button_uri];
    });
}

// MARK: - IMYAlertShowViewProtocol

- (void)show {
    [super show];
    
    self.myMaskView.alpha = 0.0;
    self.contentView.imy_top = self.imy_height;
    [UIView animateWithDuration:0.3 animations:^{
        self.myMaskView.alpha = 1.0;
        self.contentView.imy_bottom = self.imy_height;
    }];
}

- (void)dismiss {
    [super dismiss];
    
    self.myMaskView.alpha = 1.0;
    self.contentView.imy_bottom = self.imy_height;
    [UIView animateWithDuration:0.3 animations:^{
        self.myMaskView.alpha = 0.0;
        self.contentView.imy_top = self.imy_height;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

// MARK: - Get

- (UIView *)myMaskView {
    if (!_myMaskView) {
        _myMaskView = [[UIView alloc] initWithFrame:self.bounds];
        _myMaskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
        @weakify(self);
        [_myMaskView bk_whenTapped:^{
            @strongify(self);
            [self handleClickMaskView];
        }];
    }
    return _myMaskView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [[UIView alloc] initWithFrame:CGRectZero];
        _contentView.backgroundColor = [UIColor whiteColor];
        [_contentView imy_drawTopCornerRadius:12];
    }
    return _contentView;
}

- (IMYTouchEXButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [IMYTouchEXButton new];
        [_closeButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
            BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
            NSString *imageName;
            if (isNight) {
                imageName = @"icon_pop_close_yejian";
            } else {
                imageName = @"icon_pop_close";
            }
            [weakObject imy_setImage:imageName];
        }];
        [_closeButton addTarget:self action:@selector(handleClickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UIImageView *)contentImageView {
    if (!_contentImageView) {
        _contentImageView = [UIImageView new];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFit;
        _contentImageView.clipsToBounds = YES;
    }
    return _contentImageView;
}

- (IMYCapsuleButton *)mainButton {
    if (!_mainButton) {
        _mainButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 48)];
        _mainButton.type = IMYButtonTypeFillRed;
        _mainButton.titleLabel.font = [IMYBottomAlertViewV1 mainButtonFont];
        [_mainButton imy_setTitle:IMYString(@"")];
        [_mainButton addTarget:self action:@selector(handleClickMainButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _mainButton;
}

- (IMYButton *)secondaryButton {
    if (!_secondaryButton) {
        _secondaryButton = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
        _secondaryButton.titleLabel.font = [IMYBottomAlertViewV1 secondaryButtonFont];
        [_secondaryButton setTitleColor:[IMYColor colorWithNormal:kCK_Black_M]];
        [_secondaryButton imy_setBackgroundColorForKey:kIMYClearColorKey];
        [_secondaryButton addTarget:self action:@selector(handleClickSecondaryButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _secondaryButton;
}

+ (UIFont *)mainButtonFont {
    return [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
}

+ (UIFont *)secondaryButtonFont {
    return [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
}

@end
