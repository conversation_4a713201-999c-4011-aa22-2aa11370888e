//
//  SYModeSelectModel.h
//  Seeyou
//
//  Created by <PERSON><PERSON>lin on 15/8/5.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "SYDataDefine.h"

@interface SYModeSelectModel : NSObject <NSCopying>

@property (nonatomic, assign) SYUserModelType mode;
@property (nonatomic, assign) NSInteger pars_menses_day;
@property (nonatomic, assign) NSInteger pars_interval;
@property (nonatomic, strong) NSDate *date;
@property (nonatomic, strong) NSDate *babyBirthday;
//计算预产期用的末次经期
@property (nonatomic, strong) NSDate *calculateDate;
//计算孕产期用的周期
@property (nonatomic, assign) NSInteger calculateInterval;
@property (nonatomic, strong) NSDate *lastMenseDate;
@property (nonatomic, assign) NSInteger babySex;
@property (nonatomic, assign) NSInteger birthType;//birth_type，生产(分娩)方式,有变更才上报-同已有字段,1-顺产,2-剖宫产
@property (nonatomic, copy) NSString *weight;  //体重
@property (nonatomic, copy) NSString *nickname;//昵称

@property (nonatomic, strong) NSDate *birthdayDate; //用户生日日期
@property (nonatomic, assign) NSInteger birth_year; //用户出生年份，eg:"2000"

- (NSString *)babySexString;
- (NSString *)menseDayString;
- (NSString *)menseIntervalString;
- (NSString *)menseDayAndIntervalString;
- (NSString *)tmpMenseIntervalString;
- (NSDate *)getDueDateWithCalculateDate:(NSDate *)calculateDate calculateInterval:(NSInteger)calculateInterval;

// for compare
@property (nonatomic, copy) NSString *babyBirthdayStr;
@property (nonatomic, copy) NSString *dateStr;

@end
