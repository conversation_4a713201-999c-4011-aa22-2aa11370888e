//
//  SYModeDetailViewController.m
//  Seeyou
//
//  Created by 林云峰 on 16/6/7.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "SYModeDetailViewController.h"
#import "SYModeSelectModel.h"
#import "IMYMe.h"
#import <TPKeyboardAvoidingScrollView.h>
#import "IMYUserModeBabyAlert.h"
#import "IMYCKLoadingTextButton.h"
#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#import <IMYRecord/IMYRecordAnalysisBriefHelper.h>
#import <IMYRecord/IMYRecordPregnancyBabyManager.h>
#import <IMYRecord/IMYRecordHKDataManager.h>
#import <IMYRecord/IMYBabyWeightKeyboard.h>
#import <IMYRecord/IMYRecordNickNamePicker.h>

#endif

#if __has_include("SYBaseTabBarController.h")
#import "SYBaseTabBarController.h"
#endif

#if __has_include(<IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>)
#import <IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>
#endif

#import "IMYMeMineABManager.h"

@interface SYModeDetailViewController () <UITextFieldDelegate>

@property (nonatomic, assign) IMYVKUserMode type;
@property (nonatomic, strong) TPKeyboardAvoidingScrollView *scrollView;
@property (nonatomic, strong) UITextField *identifierTextField;
@property (nonatomic, strong) UITextField *lamaTextField;  //宝宝出生日
@property (nonatomic, strong) UITextField *birthTypeTextField; //生产方式
@property (nonatomic, strong) UITextField *babyWeightTextField; //宝宝出生体重
@property (nonatomic, strong) UITextField *babyNickNameTextField; //宝宝小名

@property (nonatomic, strong) UITextField *dueDateTextField;
@property (nonatomic, strong) UITextField *menseDayTextField;
@property (nonatomic, strong) UITextField *menseCircleTextField;
@property (nonatomic, strong) UITextField *babySexTextField;
@property (nonatomic, strong) UITextField *appleHealthField;
@property (nonatomic, strong) IMYSwitch *switchButton;
@property (nonatomic, assign) BOOL autoShowBirthdayPicker;

@property (nonatomic, strong) IMYPickerView *datePicker;
@property (nonatomic, strong) IMYPickerView *menseDayPicker;
@property (nonatomic, strong) IMYPickerView *menseCirclePicker;
@property (nonatomic, strong) IMYPickerView *babySexPicker;

@property (nonatomic, strong) IMYPickerView *birthTypePicker;//生产方式
@property (nonatomic, assign) BOOL autoShowBirthTypePicker;
@property (nonatomic, assign) BOOL autoShowBabyWeightPicker;
@property (nonatomic, assign) BOOL autoShowBabyNickNamePicker;

@property (nonatomic, strong) SYModeSelectModel *tmpData;
@property (nonatomic, strong) NSString *originalPregnacy;
@property (nonatomic, strong) SYUserModeViewModel *viewModel;

@property (nonatomic, strong) SYModeSelectModel *originalData;
@property (nonatomic, assign) BOOL switchOriginalOn;    //智能预测开关原值
@property (nonatomic, assign) BOOL autoEndPeriodOriginalValue;    //经期自动截断开关原值

@property (nonatomic, strong) IMYSwitch *switchVisibleButton;//怀孕几率switch
@property (nonatomic, assign) BOOL switchVisible;    //怀孕几率开关原值

@property (nonatomic, strong) NSMutableDictionary *followUpForChange;

@property (nonatomic, assign) BOOL popWithoutSaved;///< 不保存，直接离开当前页面
@property (nonatomic, strong) IMYCKLoadingTextButton *loadingButton;


// 底部提示文案
@property (nonatomic, strong) UIView *bottomTipsView;
@property (nonatomic, assign) BOOL isAddNewBaby;///< 是否添加新宝宝

@property (nonatomic, assign) BOOL canShowAuth;
#if __has_include(<IMYRecord/IMYRecordHKDataManager.h>)
@property (nonatomic, copy) NSArray<IMYRecordHKConfigModel *> *quantity;
@property (nonatomic, copy) NSArray<IMYRecordHKConfigModel *> *category;
#endif

@property (nonatomic, strong) UIView *tipBgView;
#if __has_include(<IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>)
@property (nonatomic, strong) IMYYunyuBabyInfoBirthdayTipView *zodiacSignView; //星座
@property (nonatomic, strong) IMYYunyuBabyInfoBirthdayTipView *lunarView;  //农历
#endif

@end


static NSString *yuchanqiErrorString = @"您输入的预产期距离今天超过280天，不能填入未来的怀孕记录";
#define kSectionSpace 26


@implementation SYModeDetailViewController
- (instancetype)initWithUserModeViewModel:(SYUserModeViewModel *)viewModel prepareMode:(IMYVKUserMode)prepareMode {
    if (self = [super init]) {
        SYUserModeViewModel *tmpViewModel = [SYUserModeViewModel new];
        tmpViewModel.changeCompleteBlock = viewModel.changeCompleteBlock;
        tmpViewModel.controller = viewModel.controller;
        tmpViewModel.position = viewModel.position; //统计用
        tmpViewModel.useLastModeForBi = viewModel.useLastModeForBi;
        tmpViewModel.lastModeForBi = viewModel.lastModeForBi;
        tmpViewModel.public_info = viewModel.public_info;
        @weakify(self);
        tmpViewModel.extraChangeCompleteBlock = ^(IMYVKUserMode mode) {
            @strongify(self);
            if (self) {
                //辣妈身份，且是同步机制
#if __has_include(<IMYRecord/IMYRecord.h>)
                if (mode == IMYVKUserModeLama) {
                    [self sync_extraChangeCompleteBlock:mode];
                } else {
                    [self async_extraChangeCompleteBlock:mode];
                }
#endif
            }
        };
        _viewModel = tmpViewModel;
        _type = prepareMode;
    }
    return self;
}

/// 同步方法
/// - Parameter mode: mode description
- (void)sync_extraChangeCompleteBlock:(IMYVKUserMode)mode {
    NSArray *blocks = self.followUpForChange.allValues;
    for (void (^block)(void) in blocks) {
        block();
    }
}

/// 异步
/// - Parameter mode: mode description
- (void)async_extraChangeCompleteBlock:(IMYVKUserMode)mode {
    NSArray *blocks = self.followUpForChange.allValues;
    for (void (^block)(void) in blocks) {
        block();
    }
    [self defaultChanged];

    // 重复点击问题 https://www.tapd.cn/21039721/bugtrace/bugs/view?bug_id=1121039721001075450
    [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    [self turnToHomeTabWithMode:mode];///跳转到首页逻辑
}
- (instancetype)init {
    if (self = [super init]) {
        _type = [IMYPublicAppHelper shareAppHelper].userMode;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationItem.title = [self navigationTitle];
    self.followUpForChange = [NSMutableDictionary dictionary];
    if (self.viewModel == nil) {
        self.viewModel = [SYUserModeViewModel new];
    }
    [self initTmpData];
    [self initViews];

    if (!self.fromChange) {
        [self.followUpForChange removeAllObjects];
    }
    
    //在当前页面登录后，退出至RootViewController
    @weakify(self);
    RACSignal *loginSuccessSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"LoginSucceedNotification" object:nil];
    [[loginSuccessSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.popWithoutSaved = YES;
        [self.navigationController popToRootViewControllerAnimated:NO];
    }];
    
    [self fetchIIHealth];
}

- (void)dealloc {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if (self.originalPregnacy && ![self.originalPregnacy isEqualToString:[IMYCalendarUserHelper sharedHelper].pregnancy]) {
        [SYUserHelper sharedHelper].pars_is_sync = NO;
    }
    //修改了宝宝数据，及时同步数据
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordUploadDataImmediately object:nil];
#endif
    [_datePicker freePicker];
    [_menseCirclePicker freePicker];
    [_menseDayPicker freePicker];
    [_babySexPicker freePicker];
    
}

- (void)defaultChanged {
#if __has_include(<IMYRecord/IMYRecord.h>)
    [IMYMensesModel refreshMenses];
    [self checkTmpDataHasChanged];
#endif
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.needBabyFirstResponse = NO;
    if (self.tmpData.mode == SYUserModelTypePregnancy && [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeNormal) {
        //孕期身份进来的却最终删除了孕期，需要刷新下身份
        [IMYPublicAppHelper shareAppHelper].userMode = IMYVKUserModeNormal;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (self.needBabyFirstResponse) {
        self.autoShowBirthdayPicker = YES;
//        self.autoShowBirthTypePicker = YES;
//        self.autoShowBabyWeightPicker = YES;
//        self.autoShowBabyNickNamePicker = YES;
        [self showPicker:5];
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (self.imy_isPop && !self.popWithoutSaved) {
        if (!self.fromChange) {
            NSArray *blocks = self.followUpForChange.allValues;
            for (void (^block)(void) in blocks) {
                block();
            }
            [self defaultChanged];
            //立即同步个人信息
            [SYPublicFun uploadUserInfoData];
        }
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - Apple健康

- (void)refreshAppleHealthState {
    self.appleHealthField.text = self.canShowAuth ? IMYString(@"未连接") : IMYString(@"已连接");
    
    self.appleHealthField.imyut_eventInfo.eventName = @"IMYMe-SYModeDetailViewController-appleHealthField";
    @weakify(self);
    [self.appleHealthField setImyut_exposuredBlock:^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        //BI曝光
        NSDictionary *gaParams = @{@"event": @"jq_jqszy_jksql", @"action": @(1), @"public_type":self.appleHealthField.text};
        [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
    }];
}

- (void)fetchIIHealth {
    @weakify(self);
    [[IMYPublicServerRequest getPath:@"v4/health/ios_item_config" host:diaries_seeyouyima_com params:nil headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        
        imy_asyncMainBlock(^{
            @strongify(self);
            
            // update cache
            if(x.responseObject){
                NSData *data = [NSJSONSerialization dataWithJSONObject:x.responseObject options:NSJSONWritingPrettyPrinted error:nil];
#if __has_include(<IMYRecord/IMYRecordHKDataManager.h>)
                [[IMYUserDefaults standardUserDefaults] setObject:data forKey:kRecordHKConfigDataCacheKey];
#endif

            }
            
            NSArray *quantity = x.responseObject[@"quantity"];
            NSArray *category = x.responseObject[@"category"];
            
#if __has_include(<IMYRecord/IMYRecordHKDataManager.h>)
            self.quantity = [quantity toModels:[IMYRecordHKConfigModel class]];
            self.category = [category toModels:[IMYRecordHKConfigModel class]];

            self.canShowAuth = [[IMYRecordHKDataManager sharedInstance] canShowAuthViewForQuantity:self.quantity category:self.category];
#endif
            
            [self refreshAppleHealthState];
        });
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        
        imy_asyncMainBlock(^{
            @strongify(self);
            
#ifdef DEBUG
            [UIWindow imy_showTextHUD:@"[debug toast][ios_item_config fail]"];
#endif
            
#if __has_include(<IMYRecord/IMYRecordHKDataManager.h>)
            self.quantity = [IMYRecordHKConfigModel defaultQuantity];
            self.category = [IMYRecordHKConfigModel defaultCategory];

            self.canShowAuth = [[IMYRecordHKDataManager sharedInstance] canShowAuthViewForQuantity:self.quantity category:self.category];
#endif
            [self refreshAppleHealthState];
        });
    }];
}

- (void)handleGotoHelerEvent:(id)sender {
    NSString *urlString = @"https://view.seeyouyima.com/help/guide/guide_detail.html?app_id=1&action=usage_detail&id=200";
    if ([IMYURLEnvironmentManager currentType] == IMYURLEnviromentTypeTest) {
        urlString = @"https://test-view.seeyouyima.com/help/guide/guide_detail.html?app_id=1&action=usage_detail&id=199";
    }
    
    [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": urlString} info:nil];
}

#pragma mark - 跳转到首页
- (void)positionToFirstHomeTab:(BOOL)shouldPopToRoot {
    id appDelegate = [[UIApplication sharedApplication] delegate];
    UIWindow *window = [appDelegate valueForKey:@"window"];
    UITabBarController *tabbarController = (UITabBarController *)window.rootViewController;
    if ([tabbarController isKindOfClass:UITabBarController.class]) {
        NSInteger circleIndex = 0;
        if (circleIndex >= 0 && tabbarController.selectedIndex != circleIndex) {
#if __has_include("SYBaseTabBarController.h")
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = SYTabBarIndexTypeHome;
#endif
        }
    }
    if (shouldPopToRoot) {
        //【ios】我页面，小工具列表切换身份到首页后，点击“我”tab，不应该进入小工具列表页面
        //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001176177
        
        //当前身份选择是通过present方式展示的，需要将 presentingViewController popToRoot
        UIViewController *tabbarController = self.navigationController.presentingViewController;
        if (tabbarController) {
            UINavigationController *navVC = (UINavigationController *)[(UITabBarController *)tabbarController selectedViewController];
            [navVC popToRootViewControllerAnimated:NO];
            
            //“我”tab，回首页
            [[IMYURIManager shareURIManager] runActionWithString:@"homepage/mine"];
        } else {
            [self.navigationController popToRootViewControllerAnimated:NO];
        }
    }
}

#pragma mark - method

- (NSString *)navigationTitle {
    
    if (self.type == IMYVKUserModeNormal) {
        return self.fromChange ? IMYString(@"经期模式设置") : IMYString(@"经期设置");
    }
    if (self.type == IMYVKUserModeForPregnant) {
        return self.fromChange ? IMYString(@"备孕模式设置") : IMYString(@"经期设置");
    }
    if (self.type == IMYVKUserModePregnancy) {
        return self.fromChange ? IMYString(@"怀孕模式设置") : IMYString(@"预产期设置");
    }
    if (self.type == IMYVKUserModeLama) {
        return self.fromChange ? IMYString(@"育儿模式设置") : IMYString(@"经期设置");
    }
    return @"";
}

- (NSString *)changeTipTitle {
    if ([IMYMeMineABManager sharedInstance].isMytabStyle1) {
        return IMYString(@"确认");
    }
    if (self.type == IMYVKUserModeNormal) {
        return IMYString(@"开始记经期模式");
    }
    if (self.type == IMYVKUserModeForPregnant) {
        return IMYString(@"开始备孕模式");
    }
    if (self.type == IMYVKUserModePregnancy) {
        return IMYString(@"开始怀孕模式");
    }
    if (self.type == IMYVKUserModeLama) {
        return IMYString(@"开始育儿模式");
    }
    return @"";
}

- (void)initTmpData {
    self.tmpData = [SYModeSelectModel new];
    self.tmpData.mode = [SYUserHelper sharedHelper].userModelType;
    self.tmpData.pars_interval = [IMYPublicAppHelper shareAppHelper].parsInterval;
    self.tmpData.pars_menses_day = [IMYPublicAppHelper shareAppHelper].localMensesDay;
    self.tmpData.babyBirthday = [[IMYPublicAppHelper shareAppHelper].babyBirthday imy_getOnlyDate];
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeLama || !self.tmpData.babyBirthday) {
        self.tmpData.babyBirthday = [NSDate imy_today];
    }
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.tmpData.date = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getDateZero];
    self.originalPregnacy = [IMYCalendarUserHelper sharedHelper].pregnancy;
    self.tmpData.babySex = [IMYPublicAppHelper shareAppHelper].baby_sex;
    self.originalData = [self.tmpData copy];
    self.autoEndPeriodOriginalValue = [IMYCalendarJSHelper JSHelper].is_auto_period_end;
#endif
}

- (void)checkTmpDataHasChanged {
    BOOL pushNoti = NO;
    if (![self.tmpData isEqual:self.originalData]) {
        pushNoti = YES;
    }
    if (self.switchButton.on != self.switchOriginalOn) {
        pushNoti = YES;
    }
    // 开关
    if (pushNoti) {
#if __has_include(<IMYRecord/IMYRecord.h>)
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordCalendarMenseChange object:nil];
        [IMYAnalyzePopManager sharedManager].forecastCompleteText = @"经期预测已更新";
        [[IMYAnalyzePopManager sharedManager] updateMensesPredictiveInfo:@{@"type": @(3)}];
        [IMYRecordAnalysisBriefHelper summaryActionType:IMYRecordSummaryActionTypeUserInfo actionDate:nil info:nil];
#endif
    }
    
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYCalendarJSHelper JSHelper].is_auto_period_end != self.autoEndPeriodOriginalValue || pushNoti) {
        if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
            [[IMYCalendarJSHelper JSHelper] uploadUserSetting:NULL];
        }
    }
#endif

    [self checkBabyInfoChangeAndPostNotification];
}

- (void)checkBabyInfoChangeAndPostNotification {

    BOOL equalBabySex = self.tmpData.babySex == self.originalData.babySex;
    BOOL equalBabyBirthday = (!self.tmpData.babyBirthdayStr && !self.originalData.babyBirthdayStr) || [self.tmpData.babyBirthdayStr isEqualToString:self.originalData.babyBirthdayStr];

    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (!equalBabySex) {
        params[@"changedBabySex"] = @(YES);
    }

    if (!equalBabyBirthday) {
        params[@"changeBabyBirthday"] = @(YES);
        //修改宝宝生日后刷新AB
        [[IMYABTestManager sharedInstance] refresh];
    }

    if (params.count) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kSYUserBabyInfoChangedNotification object:params];
    }
}

#pragma mark - 怀孕设置

- (BOOL)checkPregnancyValidate:(NSDate *)pregnancyDate {
    @weakify(self);
    BOOL result = [self.viewModel checkPregnancyValid:pregnancyDate
                                        completeBlock:^(BOOL valid, NSDate *resulteDate) {
                                            @strongify(self);
                                            if (!valid) {
                                                [self.datePicker setSelectWithDate:resulteDate];
                                            }
                                        }];
    return result;
}

- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate {
    return [self.viewModel makeSureShortPregnacy:dueDate];
}

#pragma mark - 辣妈设置
- (BOOL)checkBabyBirthdayValidate:(NSDate *)babyBirthday {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYPregnanceModel *pregnacy = [IMYPregnanceModel getLastPregnancyModel];
    if (pregnacy && [babyBirthday compare:pregnacy.startDate] == NSOrderedAscending) {
        //        [UIWindow imy_showTextHUD:IMYString(@"宝宝出生日小于最近孕期开始日，请重新确认")];
        [IMYErrorTraces postWithType:IMYErrorTraceTypeOthers pageName:[self ga_pageName] category:IMYErrorTraceCategoryJingqi message:@"宝宝出生日小于最近孕期开始日" detail:nil];
        return NO;
    }
#endif
    return YES;
}

#pragma mark - switch开关

- (void)changeUserAvgJsSwitch:(BOOL)isOn recalculate:(BOOL)recalculate needPostBI:(BOOL)needPostBI {
    void (^block)(void) = ^{
#if __has_include(<IMYRecord/IMYRecord.h>)
        [IMYCalendarJSHelper JSHelper].userAvgJsSwitch = isOn;
        if (recalculate) {
            [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
        }
#endif
    };
    [self.followUpForChange setObject:block forKey:@"userAvgJsSwitch"];
    
    if (needPostBI) {
        //BI点击
        NSDictionary *gaParams = @{@"event": @"jq_jqszy_znyckgbg", @"action": @(2), @"public_type":(isOn ? @"关": @"开"), @"public_info":(isOn ? @"开": @"关")};
        [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
    }
}

- (void)switchValueChanage:(IMYSwitch *)klSwitch {
    [IMYEventHelper event:@"znyc"]; //智能预测
    @weakify(klSwitch);
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        klSwitch.on = NO;
        //没登录不让改
        //        7.9.3紧急需求
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示")
                                        message:IMYString(@"开启智能预测需要先备份当前记录，请先登录。")
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"马上备份")]
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            @strongify(klSwitch);
                                            if (buttonIndex == 1) {
                                                [[IMYURIManager shareURIManager] runActionWithString:@"login"];
                                            }
                                            klSwitch.on = NO;
                                            [self changeUserAvgJsSwitch:NO recalculate:NO needPostBI:NO];
                                        }];
    } else {
        if (klSwitch.isOn) {
            if (self.type == IMYVKUserModeForPregnant) {
                [IMYActionMessageBox showBoxWithTitle:IMYString(@"提示") message:IMYString(@"确定要开启智能预测吗？") action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                    @strongify(klSwitch);
                    if (sender == messageBox.leftButton) {
                        klSwitch.on = NO;
                    }
                    BOOL needPostBI = (sender == messageBox.rightButton) ? YES : NO;
                    [self changeUserAvgJsSwitch:klSwitch.isOn recalculate:YES needPostBI:needPostBI];
                    [messageBox dismiss];
                }];
                return;
            }
        }
        [self changeUserAvgJsSwitch:klSwitch.isOn recalculate:YES needPostBI:YES];
    }
}

- (void)closeSwitchAlert {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYCalendarJSHelper JSHelper].userAvgJsSwitch) {
#else
    if (YES) {
#endif
        @weakify(self)
            [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示")
                                            message:IMYString(@"您是否要关闭智能预测？")
                                  cancelButtonTitle:IMYString(@"取消")
                                  otherButtonTitles:@[IMYString(@"确定")]
                                            handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                                @strongify(self);
                                                if (buttonIndex == 1) {
                                                    self.switchButton.on = NO;
                                                }
                                                BOOL needPostBI = (buttonIndex == 1) ? YES : NO;
                                                [self changeUserAvgJsSwitch:self.switchButton.isOn recalculate:NO needPostBI:needPostBI];
                                            }];
    }
}

#pragma mark -
- (void)showPicker:(NSInteger)type {
    UIButton *button = [UIButton new];
    button.tag = type;
    [self pickerButtonAction:button];
}

- (void)pickerButtonAction:(UIButton *)button {
    if (button.tag == 1) {
        //经期长度
        [self.menseDayPicker setSelectWithString:[NSString stringWithFormat:IMYString(@"%ld天"), self.tmpData.pars_menses_day]];
        [self.menseDayPicker show];
    } else if (button.tag == 2) {
        //周期长度
        [self.menseCirclePicker setSelectWithString:[NSString stringWithFormat:IMYString(@"%ld天"), self.tmpData.pars_interval]];
        [self.menseCirclePicker show];

    } else if (button.tag == 3) {
        //预产期
#if __has_include(<IMYRecord/IMYRecord.h>)
        [self.datePicker setSelectWithDate:[[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate]];
#endif
        [self.datePicker show];
    } else if (button.tag == 4) {
        //宝宝出生日
        [self.datePicker setSelectWithDate:self.tmpData.babyBirthday];
        [self.datePicker show];
    } else if (button.tag == 5) {
        //宝宝性别
        [self.babySexPicker setSelectWithString:self.tmpData.babySex == 1 ? IMYString(@"小王子") : IMYString(@"小公主")];
        [self.babySexPicker show];
    } else if (button.tag == 6) {
        //Apple健康
        //BI点击
        NSDictionary *gaParams = @{@"event": @"jq_jqszy_jksql", @"action": @(2), @"public_type":self.appleHealthField.text};
        [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
        
        if (self.canShowAuth) {
#if __has_include(<IMYRecord/IMYRecordHKDataManager.h>)
            [[IMYRecordHKDataManager sharedInstance] requestAuthorizationWithQuantity:self.quantity category:self.category completion:^(BOOL success, NSError *error) {
                imy_asyncMainBlock(^{
                    self.canShowAuth = [[IMYRecordHKDataManager sharedInstance] canShowAuthViewForQuantity:self.quantity category:self.category];
                    [self refreshAppleHealthState];
                    [[IMYRecordHKDataManager sharedInstance] uploadDataIfNeed];
                });
            }];
#endif
        } else {
            [self handleGotoHelerEvent:nil];
        }
    } else if (button.tag == 7) {
        //宝宝生产方式
        [self.birthTypePicker setSelectWithString:self.tmpData.birthType == 2 ? IMYString(@"剖宫产") : IMYString(@"顺产")];
        [self.birthTypePicker show];
    } else if (button.tag == 8) {
        // 宝宝体重
#if __has_include(<IMYRecord/IMYRecord.h>)
        IMYBabyWeightKeyboard *keyboard = [IMYBabyWeightKeyboard keyboardForSimple];
        keyboard.needShowWeightAdviceLabel = YES;
        keyboard.adviceLabel.text = @"填写的体重超出正常范围";
        [keyboard setTitle:@"宝宝出生体重"];
        [keyboard setMinValue:@"0.5"];
        [keyboard setMaxValue:@"150"];
        [keyboard setUnit:@"kg"];
        keyboard.isKg = YES;
        keyboard.switchUnitButton.hidden = YES;
        [keyboard setValue:self.tmpData.weight]; //传入公斤
        @weakify(self);
        [keyboard setConfirmMotherBlock:^(BOOL isValidity, NSString *value) { //忽略 api 命名
            @strongify(self);
            if (isValidity) {
                self.tmpData.weight = value;
                self.babyWeightTextField.text = [NSString stringWithFormat:@"%@kg",value];
                if (self.autoShowBabyNickNamePicker) {
                    self.autoShowBabyNickNamePicker = NO;
                    [self showPicker:9];
                }
            }
        }];
        [keyboard show];
#endif
    } else if (button.tag == 9) {
        [_babySexPicker freePicker];
        _babySexPicker = nil;
        [_datePicker freePicker];
        _datePicker = nil;
        [_babySexPicker freePicker];
        _babySexPicker = nil;
        [_birthTypePicker freePicker];
        _birthTypePicker = nil;
#if __has_include(<IMYRecord/IMYRecord.h>)
        IMYRecordNickNamePicker *picker = [IMYRecordNickNamePicker picker];
        picker.titleLabel.text = @"填写宝宝昵称";
        [picker setupPickerWithNickName:self.tmpData.nickname placeholder:@"请输入宝宝昵称，限10个字" maxLength:10];
        @weakify(self);
        [picker setConfirmBlock:^(NSString *value) {
            @strongify(self);
            self.tmpData.nickname = value;
            self.babyNickNameTextField.text = value;
        }];
        [picker show];
#endif
    }
}

#pragma mark - change Mode
- (void)changeMode:(IMYCapsuleButton *)sender {
#if __has_include(<IMYRecord/IMYRecord.h>)
        [self sync_changeMode:sender];
#endif
   
}

- (void)sync_changeMode:(IMYCapsuleButton *)sender {
    [IMYEventHelper event:@"sfsz_ksandj" attributes:@{@"leixing": [sender titleForState:UIControlStateNormal] ?: self.navigationItem.title}];

    self.isAddNewBaby = NO;
    @weakify(self);
    if (self.type == IMYVKUserModeLama) {
        if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {///7.9.3怀孕切辣妈
            self.viewModel.useLastModeForBi = YES;  //切换身份的时候 当执行到 [IMYDayRecordModel deleteCurrentPregnancy] 身份会被强制切换到经期 导致mode不对。故改为 使用useLastModeForBi 和lastModeForBi
            self.viewModel.lastModeForBi = IMYVKUserModePregnancy;
            @weakify(self);
            NSDate *birthday = self.tmpData.babyBirthday;
            [SYUserModeChangeAction sync_turnOnLamaModeWithBabyBirthday:birthday completion:^(BOOL result, BOOL addNew, NSArray *deleteBabyList) {
                @strongify(self);
                if (!result) {
                    return ;
                }
                self.viewModel.babyBirthday = birthday;
                self.viewModel.babySex = self.tmpData.babySex;

                self.isAddNewBaby = addNew;
                if (addNew) {
#if __has_include(<IMYRecord/IMYRecord.h>)
                    [self sync_continueChangeModeToLama:YES andDeleteBabyList:deleteBabyList completion:^(BOOL result) {
                        @strongify(self);
                        if (result) {
                                ///< 小于140天且无宝宝，跳转到创建宝宝,要在此刻删除孕期记录,过早删除会导致身份异常
                                ///< 参考 `handlePregnacyLessThan140Days`
//                            BOOL noBabyAndPregnancyDayLessThan140Days = NO;
//                            NSInteger pregnancyDay = [SYUserModeChangeAction getPregnancyStartDayDiff:birthday];
//                            if ([IMYRecordBabyManager sharedInstance].babyList.count <= 1 && pregnancyDay >= 0 && pregnancyDay < 140) {
//                                noBabyAndPregnancyDayLessThan140Days = YES;
//                            }
//                            if (noBabyAndPregnancyDayLessThan140Days) {
//                                NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
//                                [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
////                                [IMYDayRecordModel deleteCurrentPregnancy];///小于140天，删除
//                            }else{
//                                [SYUserModeChangeAction lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
////                                [IMYDayRecordModel birthCurrentPregnancy:birthday];//大于140天，妊娠变为已出生
//                            }
                        }
                    }];
#endif
                }else{ //没有新添加宝宝
#if __has_include(<IMYRecord/IMYRecord.h>)
                    [self.viewModel sync_confirmChangeToMode:IMYVKUserModeLama 
                                                 consultable:NO
                                           andDeleteBabyList:deleteBabyList
                                                  completion:^(BOOL result) {
                        @strongify(self);
                        if(result) {
                            /// 2.选中最小的宝宝
                            [SYUserModeChangeAction handleSelectedLastOrLastBaby];
//                            全局拉取，这里不处理 levy
                            [IMYDayRecordModel deleteCurrentPregnancy];///不需要新增宝宝,需要删除当前孕期
                            [SYUserModeChangeAction lamaClickGAEventName:@"hyqhqtsf_bbcsl"];

                            [self defaultChanged];

                            // 重复点击问题 https://www.tapd.cn/21039721/bugtrace/bugs/view?bug_id=1121039721001075450
                            [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
                            [self turnToHomeTabWithMode:IMYVKUserModeLama];///跳转到首页逻辑
                        }
                    }];
#endif
                }
                
            }];
        }else {//其他身份切辣妈
            IMYRecordBabyModel *baby = nil;
#if __has_include(<IMYRecord/IMYRecord.h>)
//            baby = [IMYRecordBabyModel searchSingleWithWhere:[NSString stringWithFormat:@"birthday = '%@' and is_deleted = 0", [self.tmpData.babyBirthday imy_getOnlyDateString]] orderBy:nil];
            baby = [IMYRecordBabyManager.sharedInstance myBabySearchWithBirthday:[self.tmpData.babyBirthday imy_getOnlyDateString]];
#endif
            if (baby) {
                [IMYUserModeBabyAlert showWithBaby:baby compeltion:^(NSInteger index) {
                    @strongify(self);
                    if (index == 2) {
                        return;
                    }
                    self.isAddNewBaby = index == 1;
                    [self sync_continueChangeModeToLama:index == 1 andDeleteBabyList:nil completion:^(BOOL result) {
                        
                    } ];
                }];
            }  else {
                self.isAddNewBaby = YES;
                [self sync_continueChangeModeToLama:YES andDeleteBabyList:nil completion:^(BOOL result) {
                    
                } ];
            }
        }
    } else if (self.type == IMYVKUserModePregnancy) {
#if __has_include(<IMYRecord/IMYRecord.h>)
        if ([[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
#else
        if (NO) {
#endif
            [self showBabyCountLimitAlert];
        } else {
            [self continueChangeMode];
        }
    } else {
        //怀孕几率按钮 切换至 经期模式
        //确认操作
        if ((self.fromChange == YES) && self.type == IMYVKUserModeNormal && self.switchVisibleButton) {
            NSString *visiblekey = [self getNotVisiblekey];
            [[IMYUserDefaults standardUserDefaults] setBool:!self.switchVisible forKey:visiblekey];
        }
        [self continueChangeMode];
    }
}
//
//- (void)continueChangeModeToLama:(BOOL)isNew {
//#if __has_include(<IMYRecord/IMYRecord.h>)
//    if (isNew && [[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
//#else
//    if (NO) {
//#endif
//        [self showBabyCountLimitAlert];
//    } else {
//        [self setLamaFollowUpForChange:isNew];
//        [self continueChangeMode];
//    }
//}
- (void)sync_continueChangeModeToLama:(BOOL)isNew andDeleteBabyList:(NSArray *)deleteBabyList completion:(SYUserModeSaveLamaBlk)completionBlk {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if (isNew && [[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
#else
    if (NO) {
#endif
        ///大于5个宝宝，弹窗让用户跳转到宝宝列表页 去删除宝宝
        [self showBabyCountLimitAlert];
        !completionBlk?:completionBlk(NO);
    } else {
        @weakify(self);
        [self sync_setLamaFollowUpForChange:isNew andDeleteBabyList:deleteBabyList isFromForcedHandoverLama:NO completion:^(BOOL result) {
            @strongify(self);
            if(result) {
                [self continueChangeMode];
            }
            !completionBlk?:completionBlk(result);
        }];
    }
}
- (void)setLamaFollowUpForChange:(BOOL)isNew {
    @weakify(self);
    void (^block)(void) = ^{
        @strongify(self);
        if (self.needBabyFirstResponse) {
#if __has_include(<IMYRecord/IMYRecord.h>)
            [IMYDayRecordModel resetBabyBirthDay:self.tmpData.babyBirthday inPregnan:[IMYPregnanceModel getLastPregnancyModel]];
#endif
        }
        [self.viewModel saveLamaWithBabyBirthday:self.tmpData.babyBirthday sex:self.tmpData.babySex isNew:isNew];
    };
    [self.followUpForChange setObject:block forKey:@"datePicker_Lama"];
}
//同步添加宝宝，辣妈身份
- (void)sync_setLamaFollowUpForChange:(BOOL)isNew
                    andDeleteBabyList:(NSArray *)deleteBabyList
                isFromForcedHandoverLama:(BOOL)isFromForcedHandoverLama
                            completion:(SYUserModeSaveLamaBlk)completionBlk {
    
    @weakify(self);
    void (^block)(void) = ^{
        @strongify(self);
        
        [UIWindow imy_showTextHUDWithoutUI];
        [self.loadingButton showLoading:YES];
        [self.viewModel sync_saveLamaWithBabyBirthday:self.tmpData.babyBirthday
                                                  sex:self.tmpData.babySex
                                             nickName:self.tmpData.nickname
                                                isNew:isNew
                                    andDeleteBabyList:deleteBabyList
                             isFromForcedHandoverLama:isFromForcedHandoverLama
                                           completion:^(BOOL result)
         {
            @strongify(self);
            [self.loadingButton showLoading:NO];
            [UIWindow imy_hideHUD];
            if (result) {
#if __has_include(<IMYRecord/IMYRecord.h>)
                //同步出生方式和体重
                if(self.tmpData.birthType > 0 || imy_isNotBlankString(self.tmpData.weight)){
                    IMYRecordBabyModel *baby = [IMYRecordBabyManager.sharedInstance myBabySearchWithBirthday:[self.tmpData.babyBirthday imy_getOnlyDateString]];
                    [self.viewModel syncBabyInfoWithBirthType:self.tmpData.birthType weight:self.tmpData.weight bbj_baby_id:baby.bbj_baby_id completion:nil];
                }
                BOOL noBabyAndPregnancyDayLessThan140Days = NO;
                NSInteger pregnancyDay = [SYUserModeChangeAction getPregnancyStartDayDiff:self.tmpData.babyBirthday];
                if ([IMYRecordBabyManager sharedInstance].babyList.count <= 1 && pregnancyDay >= 0 && pregnancyDay < 140) {
                    noBabyAndPregnancyDayLessThan140Days = YES;
                }
                if (noBabyAndPregnancyDayLessThan140Days) {
//               先调了 babyBatchOpearte接口，再删除妊娠，这里是否要调用删除妊娠接口？     TODO Levy
                    [IMYRecordPregnancyBabyManager deleteGestation:NO
                                                        loadingBtn:self.loadingButton
                                                     completeBlock:^(NSError * _Nullable error) {
                        [IMYDayRecordModel deleteCurrentPregnancy];///小于140天，删除
                    }];
                    NSDictionary *dict = @{@"event": @"hyqhqtsf_cjbb", @"action": @2};
                    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];

                }else{
//               先调了 babyBatchOpearte接口，再删除妊娠，这里是否要调用宝宝出生妊娠接口？     TODO Levy
                    [IMYDayRecordModel birthCurrentPregnancy:self.tmpData.babyBirthday];//大于140天，妊娠变为已出生
                    [SYUserModeChangeAction lamaClickGAEventName:@"hyqhqtsf_bbcsl"];
                }
#endif
                if (self.needBabyFirstResponse) {
        #if __has_include(<IMYRecord/IMYRecord.h>)
                    //               先调了 babyBatchOpearte接口，再删除妊娠，这里是否要调用宝宝出生妊娠接口？     TODO Levy
                    [IMYDayRecordModel resetBabyBirthDay:self.tmpData.babyBirthday inPregnan:[IMYPregnanceModel getLastPregnancyModel]];
        #endif
                }
                [self defaultChanged];
                // 重复点击问题 https://www.tapd.cn/21039721/bugtrace/bugs/view?bug_id=1121039721001075450
                [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
                [self turnToHomeTabWithMode:IMYVKUserModeLama];///跳转到首页逻辑
            }
            !completionBlk?:completionBlk(result);
        }];
    };
    block();
//    [self.followUpForChange setObject:block forKey:@"datePicker_Lama"];
}



- (void)showBabyCountLimitAlert {
    [IMYActionMessageBox showBoxWithTitle:IMYString(@"已达到宝宝上限")
                                  message:IMYString(@"最多只能添加5个宝宝，新增需要删除至少一个宝宝")
                                    style:IMYMessageBoxStyleFlat
                        isShowCloseButton:NO
                            textAlignment:NSTextAlignmentCenter
                        cancelButtonTitle:IMYString(@"取消")
                         otherButtonTitle:IMYString(@"去删除")
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        if (sender == messageBox.rightButton) {
            [[IMYURIManager shareURIManager] runActionWithString:@"user/baby/list"];
        }
        if (sender) {
            [messageBox dismiss];
        }
    }];
}
- (void)continueChangeMode {
    [self.viewModel continueChangeToMode:self.type];
}
- (void)sync_continueChangeModeWithCompletion:(SYUserModeSaveLamaBlk)completionBlk{
    [self.viewModel sync_continueChangeToMode:self.type completion:completionBlk];
}

#pragma mark - Views

- (void)initViews {
    if (self.type == IMYVKUserModePregnancy) {
        [self initPregnancyView];
    } else if (self.type == IMYVKUserModeLama) {
        [self initLamaView];
    } else {
        [self initNormalViewAtTop:kSectionSpace];
    }
    // 增加底部提示文案
    [self addBottomTipsView];
}

- (void)addBottomTipsView {
    if (self.bottomTipsView) {
        if (!self.bottomTipsView.superview) {
            [self.view addSubview:self.bottomTipsView];
        }
        return;
    }
}

    
- (UIButton *)creatChanedButton {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if (self.fromChange) {
        IMYCKLoadingTextButton *view = [[IMYCKLoadingTextButton alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 48)];
        self.loadingButton = view;
        self.viewModel.loadingBtn = self.loadingButton;
        [self.loadingButton setTitle:[self changeTipTitle] buttonType:IMYCKLoadingButtonRedType];
        view.titleLabel.font = [UIFont systemFontOfSize:17];
        [view addTarget:self action:@selector(changeMode:) forControlEvents:UIControlEventTouchUpInside];
        [view imy_drawAllCornerRadius:12];
        
        view.titleColor = nil;
        view.contentColor = nil;
        view.borderColor = nil;
        [view imy_addThemeChangedBlock:^(IMYCKLoadingTextButton *weakObject) {
            [weakObject setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];
            weakObject.backgroundColor = [UIColor imy_colorForKey:kCK_White_AN];
            weakObject.indicatorView.activityIndicatorViewStyle = IMYPublicAppHelper.shareAppHelper.isNight ? UIActivityIndicatorViewStyleWhite : UIActivityIndicatorViewStyleGray;
        }];
        
        view.imyut_eventInfo.eventName = [NSString stringWithFormat:@"SYModeDetailViewController-change-%f", floor([[NSDate date] timeIntervalSince1970])];
        @weakify(self);
        view.imyut_eventInfo.exposuredBlock = ^(UIButton *button, NSDictionary *params) {
            @strongify(self);
            [IMYEventHelper event:@"sfsz_ksanbg" attributes:@{@"leixing": [button titleForState:UIControlStateNormal] ?: self.navigationItem.title}];
        };
        
        return view;
    }
#endif
    return nil;
}

- (void)initPregnancyView {
    UILabel *descLabel = [self descLabelAtTop:kSectionSpace text:IMYString(@"您的预产期是哪天？")];
    UIView *view = [self cellViewWithIcon:@"data_icon_yunqi.png" title:IMYString(@"预产期") switchButton:NO atTop:descLabel.imy_bottom + 10];
    view.tag = 3;
    [view addSubview:self.dueDateTextField];
    UIButton *changedBtn = [self creatChanedButton];
    if (changedBtn) {
        changedBtn.imy_top = view.imy_bottom + 30;
        [self.scrollView addSubview:changedBtn];
    }
}
    
- (void)updateWithDidSelectedAction{
#if __has_include(<IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>)
    NSString *dateString = [self.tmpData.babyBirthday imy_getOnlyDateString];
    NSString *lunarMonthAndDay = [[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"seeyou/lunarMonthAndDay" params:@{@"dateString":dateString}];
    NSString *zodiacSign = [[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"seeyou/zodiacSign" params:@{@"dateString":dateString}];
    zodiacSign = [NSString stringWithFormat:@"星座 %@",zodiacSign];
    lunarMonthAndDay = [NSString stringWithFormat:@"农历 %@",lunarMonthAndDay];
    [self.zodiacSignView setupViewWithText:zodiacSign];
    [self.lunarView setupViewWithText:lunarMonthAndDay];
    CGFloat zodiacSignWidth = [zodiacSign imy_getDrawSizeWithFont:[UIFont imy_regularWith:11] maxSize:CGSizeMake(CGFLOAT_MAX, 13)].width + 20 + 4;
    CGFloat lunarMonthAndDayWidth = [lunarMonthAndDay imy_getDrawSizeWithFont:[UIFont imy_regularWith:11] maxSize:CGSizeMake(CGFLOAT_MAX, 13)].width + 20 + 4;
    [self.zodiacSignView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(zodiacSignWidth);
    }];
    [self.lunarView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(lunarMonthAndDayWidth);
    }];
    
    [self.tipBgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(lunarMonthAndDayWidth + zodiacSignWidth + 8);
    }];
    self.tipBgView.hidden = NO;
    [self.tipBgView.superview bringSubviewToFront:self.tipBgView];
#endif
}

- (void)initLamaView {
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeLama) {
        UILabel *descLabel = [self descLabelAtTop:kSectionSpace text:IMYString(@"填写宝宝信息")];
        UIView *sexView = [self cellViewWithIcon:@"data_icon_sex" title:IMYString(@"宝宝性别") switchButton:NO atTop:descLabel.imy_bottom + 10];
        [sexView imy_drawTopCornerRadius:12];
        [sexView addSubview:self.babySexTextField];
        sexView.tag = 5;
        [sexView imy_lineViewWithDirection:IMYDirectionDown show:YES margin:50];

        UIView *birthdayView = [self cellViewWithIcon:@"data_icon_baby.png" title:IMYString(@"宝宝出生日") switchButton:NO atTop:sexView.imy_bottom];
        [birthdayView imy_drawAllCornerRadius:0];
        [birthdayView addSubview:self.lamaTextField];
        birthdayView.tag = 4;
        
        UIView *birthdayView2 = [self cellViewWithIcon:@"data_icon_baby.png" title:IMYString(@"宝宝出生日") switchButton:NO atTop:birthdayView.imy_bottom];
        birthdayView2.tag = 4;
        birthdayView2.imy_height = 28;
        [birthdayView2 imy_removeAllSubviews];
        [birthdayView2 imy_drawBottomCornerRadius:12];
#if __has_include(<IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>)
        [birthdayView2 addSubview:self.tipBgView];
        self.tipBgView.userInteractionEnabled = NO;
        [self.tipBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(16);
            make.right.mas_equalTo(-12);
            make.bottom.mas_equalTo(-12);
            make.width.mas_equalTo(10);
        }];
        [self.tipBgView addSubview:self.zodiacSignView];
        [self.zodiacSignView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(0);
            make.top.bottom.mas_equalTo(0);
            make.width.mas_equalTo(0);
        }];
        [self.tipBgView addSubview:self.lunarView];
        [self.lunarView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(0);
            make.top.bottom.mas_equalTo(0);
            make.width.mas_equalTo(0);
        }];
#endif
        UIView *birthTypeView = [self cellViewWithIcon:@"data_icon_parturition.png" title:IMYString(@"生产方式") switchButton:NO atTop:birthdayView2.imy_bottom + 8];
        [birthTypeView imy_drawTopCornerRadius:12];
        [birthTypeView imy_lineViewWithDirection:IMYDirectionDown show:YES margin:50];
        [birthTypeView addSubview:self.birthTypeTextField];
        birthTypeView.tag = 7;
        UIView *weightViewView = [self cellViewWithIcon:@"data_icon_weight.png" title:IMYString(@"宝宝出生体重") switchButton:NO atTop:birthTypeView.imy_bottom];
        [weightViewView imy_drawAllCornerRadius:0];
        [weightViewView imy_lineViewWithDirection:IMYDirectionDown show:YES margin:50];
        [weightViewView addSubview:self.babyWeightTextField];
        weightViewView.tag = 8;
        UIView *nickNameView = [self cellViewWithIcon:@"data_icon_childhoodname.png" title:IMYString(@"宝宝小名") switchButton:NO atTop:weightViewView.imy_bottom];
        [nickNameView imy_drawBottomCornerRadius:12];
        [nickNameView addSubview:self.babyNickNameTextField];
        nickNameView.tag = 9;
        UIButton *changedBtn = [self creatChanedButton];
        if (changedBtn) {
            changedBtn.imy_top = nickNameView.imy_bottom + 20;
            [self.scrollView addSubview:changedBtn];
        }
        CGSize contentSize;
        contentSize = CGSizeMake(0, MAX(self.view.imy_height + 0.5, (changedBtn ? changedBtn.imy_bottom : descLabel.imy_bottom) + 10));
        self.scrollView.contentSize = contentSize;
        [self updateWithDidSelectedAction];
    }else{
        CGFloat top = kSectionSpace;
        [self initNormalViewAtTop:top];
    }
}

- (void)initNormalViewAtTop:(CGFloat)top {
    UILabel *descLabel = [self descLabelAtTop:top text:IMYString(@"您的月经大概持续几天？")];
    UIView *view = [self cellViewWithIcon:@"data_icon_jingqi" title:IMYString(@"经期长度") switchButton:NO atTop:descLabel.imy_bottom + 10];
    view.tag = 1;
    [view addSubview:self.menseDayTextField];
    descLabel = [self descLabelAtTop:view.imy_bottom + kSectionSpace
                                text:IMYString(@"两次月经开始日大概间隔多久？")];
    view = [self cellViewWithIcon:@"data_icon_zhouqi.png" title:IMYString(@"周期长度") switchButton:NO atTop:descLabel.imy_bottom + 10];
    view.tag = 2;
    [view addSubview:self.menseCircleTextField];
    
    UIButton *changedBtn = [self creatChanedButton];
    if (changedBtn) {
        [self.scrollView addSubview:changedBtn];
    }
    CGSize contentSize;
    CGFloat currentBottom = view.imy_bottom;
    
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYCalendarJSHelper JSHelper].serverAvgDoor) {
#else
    if (YES) {
#endif
        view = [self cellViewWithIcon:@"data_icon_zhineng.png" title:IMYString(@"使用智能预测") switchButton:YES atTop:view.imy_bottom + kSectionSpace];
        descLabel = [self descLabelAtTop:view.imy_bottom + 10 text:IMYString(@"建议开启，开启后美柚会根据您设置的数据和之前的月经记录预测下次的月经时间。")];
        currentBottom = descLabel.imy_bottom;
        
        changedBtn.imy_top = currentBottom + 30;
        contentSize = CGSizeMake(0, MAX(self.view.imy_height + 0.5, (changedBtn ? changedBtn.imy_bottom : descLabel.imy_bottom) + 10));
    } else {
        changedBtn.imy_top = currentBottom + 30;
        contentSize = CGSizeMake(0, MAX(self.view.imy_height + 0.5, (changedBtn ? changedBtn.imy_bottom : view.imy_bottom) + 10));
    }
    
    //1.经期模式、备孕模式、育儿模式，经期设置页均需要增加。
    //2.切换流程，切换至经期模式、备孕模式，设置页均需要增加
    BOOL case1 = !self.fromChange;
    BOOL case2 = self.fromChange && (self.type == IMYVKUserModeNormal || self.type == IMYVKUserModeForPregnant);
    if (case1 || case2) {
        view = [self cellViewWithIcon:@"data_icon_apple_health.png" title:IMYString(@"Apple健康") switchButton:NO atTop:descLabel.imy_bottom + 24];
        view.tag = 6;
        [view addSubview:self.appleHealthField];
        descLabel = [self descLabelAtTop:view.imy_bottom + 8 text:IMYString(@"授权后将使用静息心率等健康数据进一步提升预测准确率。")];
        currentBottom = descLabel.imy_bottom;
            
        changedBtn.imy_top = currentBottom + 30;
        contentSize = CGSizeMake(0, MAX(self.view.imy_height + 0.5, (changedBtn ? changedBtn.imy_bottom : descLabel.imy_bottom) + 10));
    }
        
    //满足以下条件 显示几率按钮--
    //不切换身份 范围:仅经期模式(包括极简模式，不包括青少年模式) !self.fromChange
    //切换身份  (仅仅当切换至 经期模式 才会显示按钮) self.fromChange = yes
        if ((IMYPublicAppHelper.shareAppHelper.userMode == IMYVKUserModeNormal && !IMYPublicAppHelper.shareAppHelper.useYoungMode && self.fromChange == NO) || (self.type == IMYVKUserModeNormal && self.fromChange == YES)) {
        view = [self cellViewWithIcon:@"datat_icon_huaiyunjilv.png" title:IMYString(@"显示怀孕几率") switchButton:YES atTop:descLabel.imy_bottom + 24];
        descLabel = [self descLabelAtTop:view.imy_bottom + 10 text:IMYString(@"关闭后，美柚首页和今日密报将不再展示怀孕几率相关信息。")];
        currentBottom = descLabel.imy_bottom;
        
        changedBtn.imy_top = currentBottom + 30;
        contentSize = CGSizeMake(0, MAX(self.view.imy_height + 0.5, (changedBtn ? changedBtn.imy_bottom : descLabel.imy_bottom) + 10));
    }
        
    // https://www.tapd.cn/21039721/bugtrace/bugs/view?bug_id=1121039721001080104
    // 应UI要求，1.默认置于底部；2.当提示文案和按钮的间距小于80时，页面滑动展示
    CGFloat marginBottom = (SCREEN_HEIGHT <= 568) ? 40 : 80;
    CGFloat addContentSizeHeight = (SCREEN_HEIGHT <= 568) ? 58 : 30;
    // 小屏适配
    UIView *alignmentView = changedBtn;
    if (!changedBtn && (SCREEN_HEIGHT <= 568)) {
        alignmentView = descLabel;
    }
    if (alignmentView && ((alignmentView.imy_bottom + marginBottom + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT) > SCREEN_HEIGHT)) {
        self.bottomTipsView.autoresizingMask = UIViewAutoresizingNone;
        self.bottomTipsView.imy_top = alignmentView.imy_bottom + marginBottom;
        [self.scrollView addSubview:self.bottomTipsView];
        contentSize.height += addContentSizeHeight;
    }

    self.scrollView.contentSize = contentSize;
}


- (UILabel *)descLabelAtTop:(CGFloat)top text:(NSString *)text {
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectMake(24, top, SCREEN_WIDTH - 39, 15)];
    descLabel.font = [UIFont systemFontOfSize:12];
    [descLabel imy_setTextColorForKey:kCK_Black_D];
    descLabel.backgroundColor = [UIColor clearColor];
    descLabel.numberOfLines = 0;
    descLabel.text = text;
    [descLabel sizeToFit];
    [self.scrollView addSubview:descLabel];

    return descLabel;
}
        
- (UIView *)cellViewWithIcon:(NSString *)icon title:(NSString *)title switchButton:(BOOL)haveSwitch atTop:(CGFloat)top {
    UIButton *view = [[UIButton alloc] initWithFrame:CGRectMake(12, top, SCREEN_WIDTH - 24, 48)];
    [view addTarget:self action:@selector(pickerButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [view setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_White_AN) andSize:CGSizeMake(10, 10)] forState:UIControlStateNormal];
    [view setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_H) andSize:CGSizeMake(10, 10)] forState:UIControlStateHighlighted];
    [view imy_addThemeChangedBlock:^(UIButton *weakObject) {
        [weakObject setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_White_AN) andSize:CGSizeMake(10, 10)] forState:UIControlStateNormal];
        [weakObject setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_H) andSize:CGSizeMake(10, 10)] forState:UIControlStateHighlighted];
    }];

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(16, 12, 24, 24)];
    imageView.image = [UIImage imy_imageForKey:icon];
    [view addSubview:imageView];

    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(imageView.imy_right + 15, 0, 200, 48)];
    label.font = [UIFont systemFontOfSize:17];
    label.backgroundColor = [UIColor clearColor];
    [label imy_setTextColorForKey:kCK_Black_A];
    label.text = title;
    [view addSubview:label];
    [view imy_drawAllCornerRadius:12];
    
    UIImageView *arrow = [[UIImageView alloc] initWithImage:[UIImage imy_imageForKey:@"all_icon_arrow.png"]];
    arrow.imy_right = view.imy_width - 15;
    arrow.imy_centerY = 48 / 2;
    [view addSubview:arrow];

    [self.scrollView addSubview:view];
    if (haveSwitch) {
     if ([title isEqualToString:@"显示怀孕几率"]) {
         IMYSwitch *visibleSwitchButton = [[IMYSwitch alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 53 - 15 - 24, 6, 53, 32)];
         arrow.hidden = YES;
         [view addSubview:visibleSwitchButton];
         __block NSString *visiblekey = [self getNotVisiblekey];
         
         //切换模式下 怀孕几率 switch 同步本地
         //点击事件 仅修改状态,不做实际操作
         BOOL isVisible = ![[[IMYUserDefaults standardUserDefaults] objectForKey:visiblekey] boolValue];
         visibleSwitchButton.on = isVisible;
         [self bi_action:1 withType:isVisible];
         if(self.fromChange == YES){
             self.switchVisible = isVisible;
         }
         self.switchVisibleButton = visibleSwitchButton;
         @weakify(self);
         [visibleSwitchButton setOnDidStateChanged:^(IMYSwitch *swi, BOOL isON) {
             @strongify(self);
             if(self.fromChange == YES){
                 self.switchVisible = isON;
             }else {
                 [[IMYUserDefaults standardUserDefaults] setBool:!isON forKey:visiblekey];
                 [[NSNotificationCenter defaultCenter] postNotificationName:@"IMYRecordDRPPChangeNotification" object:nil];
             }
             [self bi_action:2 withType:!isON];
               
         }];   
     } else {
        IMYSwitch *switchButton = [[IMYSwitch alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 53 - 15 - 24, 6, 53, 32)];
#if __has_include(<IMYRecord/IMYRecord.h>)
        switchButton.on = [IMYCalendarJSHelper JSHelper].userAvgJsSwitch;
#else
        switchButton.on = YES;
#endif
        self.switchOriginalOn = switchButton.on;
        if (self.fromChange && [IMYPublicAppHelper shareAppHelper].hasLogin) {
#if __has_include(<IMYRecord/IMYRecord.h>)
            if (![IMYCalendarJSHelper JSHelper].userAvgJsSwitch) {
#else
            if (NO) {
#endif
                [self changeUserAvgJsSwitch:YES recalculate:YES needPostBI:YES];
                switchButton.on = YES;
                self.switchOriginalOn = NO;
            }
        }
        @weakify(self);
        [switchButton setOnDidStateChanged:^(IMYSwitch *swi, BOOL isON) {
            @strongify(self);
            [self switchValueChanage:swi];
        }];
        arrow.hidden = YES;
        [view addSubview:switchButton];
        self.switchButton = switchButton;
      }
    }

    return view;
}
/*怀孕几率
 1：曝光
 2：点击
 */
- (void)bi_action:(NSInteger)action withType:(BOOL)isVisible {
    NSString *publicType = isVisible? IMYString(@"开"): IMYString(@"关");
    NSDictionary *gaParams = @{@"event": @"jq_jqszy_hyjlan", @"action": @(action), @"public_type":publicType};
    [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
}
    
    
- (NSString *)getNotVisiblekey {
    NSString *visiblekey = [NSString stringWithFormat:@"IMYRecordDRPPCell_NotVisible_%@", [IMYPublicAppHelper shareAppHelper].userid];
    return visiblekey;
}

- (void)reloadViews {
    [self.view imy_removeAllSubviews];
    _datePicker = nil;
    _menseDayPicker = nil;
    _menseCirclePicker = nil;
    _identifierTextField = nil;
    _dueDateTextField = nil;
    _lamaTextField = nil;
    _birthTypeTextField = nil;
    _babyWeightTextField = nil;
    _babyNickNameTextField = nil;
    _menseDayTextField = nil;
    _menseCircleTextField = nil;
    _switchButton = nil;
    _scrollView = nil;
    _babySexPicker = nil;
    _babySexTextField = nil;
    _birthTypePicker = nil;
    _tipBgView = nil;
    [self initViews];
}

#pragma mark - UI

- (TPKeyboardAvoidingScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[TPKeyboardAvoidingScrollView alloc] initWithFrame:self.view.bounds];
        _scrollView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.view addSubview:_scrollView];
    }
    return _scrollView;
}

- (UITextField *)createTextField {
    UITextField *textField = [[UITextField alloc] initWithFrame:CGRectMake(100, 0, SCREEN_WIDTH - 100 - 53, 48)];
    textField.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
    textField.font = [UIFont systemFontOfSize:17];
    textField.backgroundColor = [UIColor clearColor];
    textField.textAlignment = NSTextAlignmentRight;
    [textField imy_setTextColorForKey:kCK_Black_D];
    textField.userInteractionEnabled = NO;
    return textField;
}

- (UITextField *)identifierTextField {
    if (!_identifierTextField) {
        _identifierTextField = [self createTextField];
        _identifierTextField.userInteractionEnabled = NO;
        _identifierTextField.text = [SYUserHelper sharedHelper].s_userModelTypeString;
    }
    return _identifierTextField;
}

- (UITextField *)dueDateTextField {
    if (!_dueDateTextField) {
        _dueDateTextField = [self createTextField];
#if __has_include(<IMYRecord/IMYRecord.h>)
        NSDate *date = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate];
#else
        NSDate *date = [[IMYPublicAppHelper shareAppHelper].pregnancy imy_getOnlyDate];
#endif
        NSString *dateString = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
        _dueDateTextField.text = dateString;
    }
    return _dueDateTextField;
}

- (UITextField *)lamaTextField {
    if (!_lamaTextField) {
        _lamaTextField = [self createTextField];
        NSDate *date = [IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeLama ? [[IMYPublicAppHelper shareAppHelper].babyBirthday imy_getOnlyDate] : [NSDate imy_today];
        _lamaTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];;
    }
    return _lamaTextField;
}
    
- (UITextField *)birthTypeTextField{
    if(!_birthTypeTextField){
        _birthTypeTextField = [self createTextField];
        _birthTypeTextField.text = @"选填";
    }
    return _birthTypeTextField;
}
    
- (UITextField *)babyWeightTextField{
    if(!_babyWeightTextField){
        _babyWeightTextField = [self createTextField];
        _babyWeightTextField.text = @"选填";
    }
    return _babyWeightTextField;
}
    
- (UITextField *)babyNickNameTextField{
    if(!_babyNickNameTextField){
        _babyNickNameTextField = [self createTextField];
        _babyNickNameTextField.text = @"选填";
    }
    return _babyNickNameTextField;
}

- (UITextField *)menseDayTextField {
    if (!_menseDayTextField) {
        _menseDayTextField = [self createTextField];
        NSInteger parsMensesDay = [IMYPublicAppHelper shareAppHelper].localMensesDay;
        _menseDayTextField.text = [NSString stringWithFormat:IMYString(@"%ld天"), parsMensesDay];
    }
    return _menseDayTextField;
}

- (UITextField *)menseCircleTextField {
    if (!_menseCircleTextField) {
        _menseCircleTextField = [self createTextField];
#if __has_include(<IMYRecord/IMYRecord.h>)
        NSInteger parsInterval = [IMYCalendarUserHelper sharedHelper].parsInterval;
#else
        NSInteger parsInterval = [IMYPublicAppHelper shareAppHelper].parsInterval;
#endif
        _menseCircleTextField.text = [NSString stringWithFormat:IMYString(@"%ld天"), parsInterval];
    }
    return _menseCircleTextField;
}

- (UITextField *)babySexTextField {
    if (!_babySexTextField) {
        _babySexTextField = [self createTextField];
        _babySexTextField.text = self.tmpData.babySex == 1 ? IMYString(@"小王子") : IMYString(@"小公主");
    }
    return _babySexTextField;
}

- (UITextField *)appleHealthField {
    if (!_appleHealthField) {
        _appleHealthField = [self createTextField];
        _appleHealthField.text = self.canShowAuth ? IMYString(@"未连接") : IMYString(@"已连接");
    }
    return _appleHealthField;
}

- (IMYPickerView *)datePicker {
    if (!_datePicker) {
        NSArray *data = self.type == IMYVKUserModePregnancy ? @[[self.viewModel miniPregnacyPickerDate], [[NSDate imy_today] dateByAddingDays:280]] : @[[@"2000-1-1" imy_getOnlyDate], [NSDate imy_today]];
        ///怀孕切辣妈新逻辑
        if (IMYPublicAppHelper.shareAppHelper.userMode == IMYVKUserModePregnancy && self.type == IMYVKUserModeLama) {
#if __has_include(<IMYRecord/IMYRecord.h>)
            NSDate *pregnacySart = [IMYDayRecordModel getCurrentPregnancyStartDate];
            NSInteger pregnancyDays = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
            ///无宝宝,切孕期小于140天,不限制日期
            if (pregnancyDays < 140 && [IMYPublicAppHelper shareAppHelper].babyCount == 0) {
                pregnacySart = [@"2000-1-1" imy_getOnlyDate];
            }
#else
            NSDate *pregnacySart = [@"2000-1-1" imy_getOnlyDate];
#endif
            data = @[pregnacySart, [NSDate imy_today]];
        }
        @weakify(self);
        _datePicker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
            dataArray:data
            pickerViewTpe:IMYPickerViewTypeDate
            confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                @strongify(self);
                if (self.type == IMYVKUserModePregnancy) {
                    NSDate *resultDate = result.resultDate;
                    if ([self checkPregnancyValidate:resultDate]) {
                        if ([self makeSureShortPregnacy:resultDate]) {
                            void (^block)(void) = ^{
                                @strongify(self);
                                [self.viewModel savePregancyWithDate:resultDate];
                            };
                            [self.followUpForChange setObject:block forKey:@"datePicker_Pregancy"];
                        }
                        self.dueDateTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:resultDate];
                    } else {
#if __has_include(<IMYRecord/IMYRecord.h>)
                        NSDate *date = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate];
                        self.tmpData.date = date;
                        self.dueDateTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
#endif
                    }
                } else {
                    if ([self checkBabyBirthdayValidate:result.resultDate]) {
                        NSDate *resultDate = result.resultDate;
                        self.lamaTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:resultDate];
                        self.tmpData.babyBirthday = resultDate;
                        if (!self.fromChange) {
                            [self setLamaFollowUpForChange:NO];
                        }
                        [self updateWithDidSelectedAction];
                        if (self.autoShowBirthTypePicker) {
                            self.autoShowBirthTypePicker = NO;
                            [self showPicker:7];
                        }
                    } else {
                        if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeLama) {
                            self.tmpData.babyBirthday = [NSDate imy_today];
                            self.lamaTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:[NSDate imy_today]];;
                            [self updateWithDidSelectedAction];
                        }
                        [self.datePicker hide];
#if __has_include(<IMYRecord/IMYRecord.h>)
//                        [[IMYURIManager shareURIManager] runActionWithString:kURIIMYPregnacyManager];
                        IMYURI *uri = [IMYURI uriWithPath:kURIIMYPregnacyManager params:nil info:nil];
                        IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
                        actionObject.implCallbackBlock = ^(id result2, NSError *error, NSString *eventName) {
                            @strongify(self);
                            NSDate *resultDate = result.resultDate;
                            self.tmpData.babyBirthday = result.resultDate;
                            self.lamaTextField.text = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:resultDate];
                            [self updateWithDidSelectedAction];
                        };
                        [[IMYURIManager shareURIManager] runActionWithActionObject:actionObject completed:nil];
#endif
                        return;
                    }
                }
                [self.datePicker hide];
            }
            cancelBlock:^{
                @strongify(self);
                [self.datePicker hide];
            }];
        _datePicker.title = self.type == IMYVKUserModePregnancy ? IMYString(@"选择预产期")
                                                                : IMYString(@"选择宝宝出生日");
        _datePicker.outDismissControl = YES;
    }
    return _datePicker;
}

- (IMYPickerView *)menseDayPicker {
    if (!_menseDayPicker) {
        @weakify(self)
            NSMutableArray *data = [NSMutableArray array];
#if __has_include(<IMYRecord/IMYRecord.h>)
        NSInteger minDay = IMYRecordMenseLengthMin;
        NSInteger maxDay = MIN(IMYRecordMenseLengthMax, self.tmpData.pars_interval - 4);
        for (int i = minDay; i <= maxDay; i++) {
#else
        for (int i = 17; i <= 60; i++) {
#endif
            [data addObject:[NSString stringWithFormat:IMYString(@"%d天"), i]];
        }
        _menseDayPicker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
            dataArray:@[data]
            pickerViewTpe:IMYPickerViewTypeCustom
            confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                @strongify(self);
                //BI点击
                NSDictionary *gaParams = @{@"event": @"jq_jqszy_jqcdbg", @"action": @(2), @"public_type":self.menseDayTextField.text, @"public_info":result.resultString};
                [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
            
                NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
                NSInteger pars_menses_day = day.integerValue;
                self.tmpData.pars_menses_day = pars_menses_day;
                self.menseDayTextField.text = result.resultString;
                [self.menseDayPicker hide];
                // 选项值有重叠部分，需要重新初始化
                self.menseCirclePicker = nil;
                void (^block)(void) = ^{
                    [SYUserHelper sharedHelper].pars_menses_day = pars_menses_day;
                    [[SYUserHelper sharedHelper] saveToDB];
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"kChangeParsIntervalNotify" object:nil];
                };
                [self.followUpForChange setObject:block forKey:@"menseDayPicker"];
            }
            cancelBlock:^{
                @strongify(self);
                [self.menseDayPicker hide];
            }];
        _menseDayPicker.title = IMYString(@"选择经期天数");
        _menseDayPicker.outDismissControl = YES;
    }
    return _menseDayPicker;
}

- (IMYPickerView *)menseCirclePicker {
    if (!_menseCirclePicker) {

        NSMutableArray *data = [NSMutableArray array];
#if __has_include(<IMYRecord/IMYRecord.h>)
        NSInteger minDay = MAX(IMYRecordMenseCycleLengthMin, self.tmpData.pars_menses_day + 4);
        NSInteger maxDay = IMYRecordMenseCycleLengthMax;
        for (int i = minDay; i <= maxDay; i++) {
#else
        for (int i = 17; i <= 60; i++) {
#endif
            [data addObject:[NSString stringWithFormat:IMYString(@"%d天"), i]];
        }
        @weakify(self)
            _menseCirclePicker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                dataArray:@[data]
                pickerViewTpe:IMYPickerViewTypeCustom
                confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                    @strongify(self);
                    //BI点击
                    NSDictionary *gaParams = @{@"event": @"jq_jqszy_zqcdbg", @"action": @(2), @"public_type":self.menseCircleTextField.text, @"public_info":result.resultString};
                    [IMYGAEventHelper postWithPath:@"event" params:gaParams headers:nil completed:nil];
                
                    NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
                    NSInteger pars_interval = day.integerValue;
                    if (day.integerValue != self.tmpData.pars_interval && [IMYPublicAppHelper shareAppHelper].hasLogin) {
                        [self closeSwitchAlert];
                    }
                    self.tmpData.pars_interval = pars_interval;
                    self.menseCircleTextField.text = result.resultString;
                    [self.menseCirclePicker hide];
                    // 选项值有重叠部分，需要重新初始化
                    self.menseDayPicker = nil;
                    void (^block)(void) = ^{
                        [SYUserHelper sharedHelper].pars_interval = pars_interval;
                        [[SYUserHelper sharedHelper] saveToDB];
                        [[NSNotificationCenter defaultCenter] postNotificationName:@"kChangeParsIntervalNotify" object:nil];
                    };
                    [self.followUpForChange setObject:block forKey:@"menseCirclePicker"];
                }
                cancelBlock:^{
                    @strongify(self);
                    [self.menseCirclePicker hide];
                }];
        _menseCirclePicker.title = IMYString(@"选择周期天数");
        _menseCirclePicker.outDismissControl = YES;
    }
    return _menseCirclePicker;
}

- (IMYPickerView *)babySexPicker {
    if (!_babySexPicker) {
        NSArray *data = @[IMYString(@"小公主"), IMYString(@"小王子")];
        @weakify(self)
            _babySexPicker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                dataArray:@[data]
                pickerViewTpe:IMYPickerViewTypeCustom
                confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                    @strongify(self);
                    self.babySexTextField.text = result.resultString;
                    [self.babySexPicker hide];
                    if (self.autoShowBirthdayPicker) {
                        self.autoShowBirthdayPicker = NO;
                        [self showPicker:4];
                    }
                    NSInteger baby_sex = [result.resultString isEqualToString:IMYString(@"小公主")] ? 2 : 1;
                    self.tmpData.babySex = baby_sex;
                    if (!self.fromChange) {
                        [self setLamaFollowUpForChange:NO];
                    }
                }
                cancelBlock:^{
                    @strongify(self);
                    [self.babySexPicker hide];
                }];
        _babySexPicker.title = IMYString(@"选择宝宝性别");
        _babySexPicker.outDismissControl = YES;
    }
    return _babySexPicker;
}
    
- (IMYPickerView *)birthTypePicker {
    if (!_birthTypePicker) {
        NSArray *data = @[IMYString(@"顺产"), IMYString(@"剖宫产")];
        @weakify(self)
        _birthTypePicker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
            dataArray:@[data]
            pickerViewTpe:IMYPickerViewTypeCustom
            confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                @strongify(self);
                self.birthTypeTextField.text = result.resultString;
                [self.birthTypePicker hide];
                if (self.autoShowBabyWeightPicker) {
                    self.autoShowBabyWeightPicker = NO;
                    [self showPicker:8];
                }
                NSInteger birthType = [result.resultString isEqualToString:IMYString(@"剖宫产")] ? 2 : 1;
                self.tmpData.birthType = birthType;
            }
            cancelBlock:^{
                @strongify(self);
                [self.birthTypePicker hide];
            }];
        _birthTypePicker.title = IMYString(@"选择生产方式");
        _birthTypePicker.outDismissControl = YES;
    }
    return _birthTypePicker;
}
    

- (UIView *)bottomTipsView {
    if (!_bottomTipsView) {
        NSInteger position = 0;
        if (SCREEN_TABBAR_SAFEBOTTOM_MARGIN > 0) {
            position = self.view.imy_height - 20 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        } else {
            position = self.view.imy_height - 40;
        }
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, position, SCREEN_WIDTH - 20, 14)];
        titleLabel.autoresizingMask = UIViewAutoresizingFlexibleTopMargin;
        
        titleLabel.font = [UIFont systemFontOfSize:12];
        titleLabel.textColor = IMY_COLOR_KEY(kCK_Black_D);
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.minimumScaleFactor = 0.5;
        titleLabel.adjustsFontSizeToFitWidth = YES;
        switch (self.type) {
            case IMYVKUserModePregnancy: {
                titleLabel.text = @"您所填写的生理信息将用于孕期记录、分析及产检报告备份";
            } break;
            case IMYVKUserModeLama: {
                titleLabel.text = @"您所填写的生理信息将用于经期记录、分析、预测及宝宝健康分析";
            } break;
            default: {
                titleLabel.text = @"您所填写的生理信息将用于经期记录、分析及预测";
            } break;
        }
        titleLabel.imy_centerX  = self.view.imy_width / 2;
        _bottomTipsView = titleLabel;
    }
    return _bottomTipsView;
}
    

/// 切换到首页, 原来逻辑是0.25秒后切换到首页
/// 797且辣妈, 需要添加判断是否要切换完直接跳到智能识别页面,需要异步请求数据.所以加了gcd
/// @param mode 当前模式
- (void)turnToHomeTabWithMode:(IMYVKUserMode)mode {
    @weakify(self);

    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    dispatch_group_async(group, dispatch_get_main_queue(), ^{
        @weakify(self);
        imy_asyncMainBlock(0.25, ^{
            @strongify(self);
            [self positionToFirstHomeTab:YES];
            imy_asyncMainBlock(0.1, ^{
                [[UIApplication sharedApplication] endIgnoringInteractionEvents];
                NSString *modeStr = [IMYPublicAppHelper shareAppHelper].userModeName;
                [MBProgressHUD imy_showTextHUD:[NSString stringWithFormat:@"已切换至%@模式", modeStr]];
            });
            dispatch_group_leave(group);
        });
       
    });

    __block BOOL canAiRecognition = NO;
    dispatch_group_enter(group);
    dispatch_group_async(group, dispatch_get_main_queue(), ^{
        ///切换到辣妈身份直接开始启动智能识别
        ///新增宝宝才走智能识别判断
        if (self.isAddNewBaby && mode == IMYVKUserModeLama) {
            canAiRecognition = YES;
            dispatch_group_leave(group);
            return ;
        } else {
            canAiRecognition = NO;
            dispatch_group_leave(group);
            return;
        }
    });
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (!canAiRecognition) {
            return ;
        }
        ///跳转到智能识别
        NSMutableDictionary *params = [[NSMutableDictionary alloc]  init];
        [params imy_setNonNilObject:[NSDate new] forKey:@"dueDate"];
        [params imy_setNonNilObject:@(-1) forKey:@"baby_id"];
        [params imy_setNonNilObject:@(9) forKey:@"position"];
        [params imy_setNonNilObject:@(self.viewModel.public_info) forKey:@"public_info"];
        NSString *uriString = @"seeyoubaby/photo/show_leadpage_v2";
        IMYURI *uri = [IMYURI uriWithPath:uriString params:params info:nil];
        IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
        actionObject.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
            BOOL downloadFailed = [[result objectForKey:@"downloadFailed"] boolValue];
            if (downloadFailed) {
                
            }
        };
        [[IMYURIManager shareURIManager] runActionWithActionObject:actionObject completed:nil];
    });
}
    
    
- (UIView *)tipBgView{
    if (!_tipBgView) {
        UIView *view = [UIView new];
        view.backgroundColor = UIColor.clearColor;
        _tipBgView = view;
    }
    return _tipBgView;
}
    
#if __has_include(<IMYYunyuHome/IMYYunyuBabyInfoBirthdayTipView.h>)
- (IMYYunyuBabyInfoBirthdayTipView *)zodiacSignView{
    if (!_zodiacSignView) {
        IMYYunyuBabyInfoBirthdayTipView *view = [IMYYunyuBabyInfoBirthdayTipView new];
        [view setupViewWithIconName:@"imy_icon_star" text:@"" textColor:[UIColor imy_colorForKey:@"#D88BFF"] bgColor:[[UIColor imy_colorForKey:@"#D88BFF"] colorWithAlphaComponent:0.1]];
        [view imy_drawAllCornerRadius:4];
        _zodiacSignView = view;
    }
    return _zodiacSignView;
}

- (IMYYunyuBabyInfoBirthdayTipView *)lunarView{
    if (!_lunarView) {
        IMYYunyuBabyInfoBirthdayTipView *view = [IMYYunyuBabyInfoBirthdayTipView new];
        [view setupViewWithIconName:@"imy_icon_calendar" text:@"" textColor:[UIColor imy_colorForKey:@"#F7C064"] bgColor:[[UIColor imy_colorForKey:@"#F7C064"] colorWithAlphaComponent:0.1]];
        [view imy_drawAllCornerRadius:4];
        _lunarView = view;
    }
    return _lunarView;
}

#endif
@end
  
