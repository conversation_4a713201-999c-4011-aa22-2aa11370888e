//
//  SYModeConfirmViewController.m
//  Seeyou
//
//  Created by 林云峰 on 16/6/30.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "SYModeConfirmViewController.h"
#import "SYModeSelectModel.h"
#import "SYUseModeTableViewCell.h"
#import "IMYMe.h"
#import "IMYMeMineABManager.h"
#import "IMYCKLoadingTextButton.h"
#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#import <IMYRecord/IMYRecordPregnancyBabyManager.h>
#endif
#if __has_include("SYBaseTabBarController.h")
#import "SYBaseTabBarController.h"
#endif

@interface SYModeConfirmViewController () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, assign) BOOL isExpended;
@property (nonatomic, strong) NSDate *defaultPregnacyDate;
@property (nonatomic, strong) NSDate *pregnacyStaredDate;
@property (nonatomic, strong) SYUserModeViewModel *viewModel;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *items;
@property (nonatomic, strong) IMYPickerView *pickerView;
@property (nonatomic, strong) SYModeSelectModel *model;
@property (nonatomic, assign) BOOL isUseCalculate;
@property (nonatomic, strong) NSMutableDictionary *followUpForChange;
@property (nonatomic, assign) BOOL isBack;

@end

@implementation SYModeConfirmViewController

- (instancetype)initWithUserModeViewModel:(SYUserModeViewModel *)viewModel {
    if (self = [super init]) {
        SYUserModeViewModel *tmpViewModel = [SYUserModeViewModel new];
        tmpViewModel.changeCompleteBlock = viewModel.changeCompleteBlock;
        tmpViewModel.controller = viewModel.controller;
        tmpViewModel.position = viewModel.position; //统计用
        @weakify(self);
        tmpViewModel.extraChangeCompleteBlock = ^(IMYVKUserMode mode) {
            @strongify(self);
            NSArray *blocks = self.followUpForChange.allValues;
            for (void (^block)(void) in blocks) {
                block();
            }
            IMY_POST_NOTIFY(@"PregnacyDataChange");
            imy_asyncMainBlock(0.1, ^{
                [self positionToFirstHomeTab:YES];
                imy_asyncMainBlock(0.1, ^{
                    NSString *modeStr = [IMYPublicAppHelper shareAppHelper].userModeName;
                    [MBProgressHUD imy_showTextHUD:[NSString stringWithFormat:IMYString(@"已切换至%@模式"), modeStr]];
                });
            });
        };
        _viewModel = tmpViewModel;
    }
    return self;
}

- (instancetype)initWithViewModel:(SYUserModeViewModel *)viewModel {
    if (self = [super init]) {
        self.viewModel = viewModel;
    }
    return self;
}

- (void)dealloc {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYMensesDao getMensesAll].count == 0) {
        NSDate *date = [IMYPregnanceModel getLastPregnancyModel].startDate;
        IMYDayRecordModel *daymodel = [IMYDayRecordDao searchDayRecordWithDate:date];
        if (!daymodel) {
            daymodel = [IMYDayRecordModel new];
            daymodel.date = date;
        }
        daymodel.isEnd = NO;
        daymodel.isBegin = YES;
        [IMYDayRecordDao mensesInsertToDB:daymodel];
    }
    if ([[[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate] compare:self.defaultPregnacyDate] != NSOrderedSame) {
        [SYUserHelper sharedHelper].pars_is_sync = NO;
        //如果命中实验和开关，立刻上传 妊娠流水数据
        //修改了宝宝数据，及时同步数据
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordUploadDataImmediately object:nil];
        //修改预产期后刷新AB
        [[IMYABTestManager sharedInstance] refresh];
    }
#endif
}
- (void)updatePregnacyDate {
    NSArray *blocks = self.followUpForChange.allValues;
    for (void (^block)(void) in blocks) {
        block();
    }
    IMY_POST_NOTIFY(@"PregnacyDataChange");
}
- (void)changePregnancePostDataToServer {
    if (!self.isChange) {
        NSArray *blocks = self.followUpForChange.allValues;
        for (void (^block)(void) in blocks) {
            block();
        }
        IMY_POST_NOTIFY(@"PregnacyDataChange");
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
}

- (void)viewDidLoad {

    [super viewDidLoad];
    [self initModel];
    [self.view addSubview:self.tableView];
    if (self.viewModel == nil) {
        self.viewModel = [SYUserModeViewModel new];
    }
    //辣妈不走这里了，先强制换成怀孕；
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.defaultPregnacyDate = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate];
#endif

    self.navigationItem.title = self.isChange ? IMYString(@"怀孕模式设置") : IMYString(@"预产期设置");
    
    // 添加底部提示文案
    [self addBottomTipsView];
}

- (void)addBottomTipsView {
    // 魔法性 UI，根据 UI设计稿，随意编码
    NSInteger position = 0;
    if (SCREEN_TABBAR_SAFEBOTTOM_MARGIN > 0) {
        position = self.view.imy_height - 20 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    } else {
        position = self.view.imy_height - 40;
    }
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, position, SCREEN_WIDTH - 20, 14)];
    titleLabel.autoresizingMask = UIViewAutoresizingFlexibleTopMargin;
    
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.textColor = IMY_COLOR_KEY(kCK_Black_B);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.minimumScaleFactor = 0.5;
    titleLabel.adjustsFontSizeToFitWidth = YES;
    titleLabel.text = IMYString(@"您所填写的生理信息将用于孕期记录、分析及产检报告备份");
    titleLabel.imy_centerX  = self.view.imy_width / 2;
    [self.view addSubview:titleLabel];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)imy_topLeftButtonTouchupInside {
    self.isBack = YES;
    [self goBackAction];
    [super imy_topLeftButtonTouchupInside];
}
- (void)didMoveToParentViewController:(UIViewController *)parent {
    [super didMoveToParentViewController:parent];

   if(!parent && !self.isBack) {
       [self goBackAction];
   }
}

- (void)goBackAction {
    if (!self.isChange) {
        NSArray *blocks = self.followUpForChange.allValues;
        for (void (^block)(void) in blocks) {
            block();
        }
        IMY_POST_NOTIFY(@"PregnacyDataChange");
        if (self.backBlock) {
            self.backBlock();
        }
    }
}

#pragma mark - change Mode
- (void)changeMode:(id)sender {
    [IMYEventHelper event:@"sfsz_ksandj" attributes:@{@"leixing": IMYString(@"开始怀孕模式")}];
    
    NSDate *dueDate = self.model.date;
    //是否有冲突的孕期数据
    NSArray *conflictArray = [self.viewModel conflictPregnacyDatesWithNewDueDate:dueDate];
    if (conflictArray.count > 0) {
        [self handlePregnacyConflictWithConflictArray:conflictArray dueDate:dueDate result:nil type:SYPregnacyModeSettingTypeSeting];
        return;
    }
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
        [IMYActionMessageBox showBoxWithTitle:IMYString(@"已达到宝宝上限")
                                      message:IMYString(@"切换为怀孕身份前需至少删除一个宝宝")
                                        style:IMYMessageBoxStyleFlat
                            isShowCloseButton:NO
                                textAlignment:NSTextAlignmentCenter
                            cancelButtonTitle:IMYString(@"取消")
                             otherButtonTitle:IMYString(@"去删除")
                                       action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
            if (sender == messageBox.rightButton) {
                [[IMYURIManager shareURIManager] runActionWithString:@"user/baby/list"];
            }
            if (sender) {
                [messageBox dismiss];
            }
        }];
        return;
    }
#endif
    
    [self.viewModel continueChangeToMode:IMYVKUserModePregnancy];
}

#pragma mark - 跳转到首页
- (void)positionToFirstHomeTab:(BOOL)shouldPopToRoot {
    id appDelegate = [[UIApplication sharedApplication] delegate];
    UIWindow *window = [appDelegate valueForKey:@"window"];
    UITabBarController *tabbarController = (UITabBarController *)window.rootViewController;
    if ([tabbarController isKindOfClass:UITabBarController.class]) {
        NSInteger circleIndex = 0;
        if (circleIndex >= 0 && tabbarController.selectedIndex != circleIndex) {
#if __has_include("SYBaseTabBarController.h")
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = SYTabBarIndexTypeHome;
#endif
        }
    }
    if (shouldPopToRoot) {
        //【ios】我页面，小工具列表切换身份到首页后，点击“我”tab，不应该进入小工具列表页面
        //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001176177
        
        //当前身份选择是通过present方式展示的，需要将 presentingViewController popToRoot
        UIViewController *tabbarController = self.navigationController.presentingViewController;
        if (tabbarController) {
            UINavigationController *navVC = (UINavigationController *)[(UITabBarController *)tabbarController selectedViewController];
            [navVC popToRootViewControllerAnimated:NO];
            
            //“我”tab，回首页
            [[IMYURIManager shareURIManager] runActionWithString:@"homepage/mine"];
        } else {
            [self.navigationController popToRootViewControllerAnimated:NO];
        }
    }
}

#pragma mark - pregnacy

- (BOOL)checkPregnancyValidate:(NSDate *)pregnancyDate {
    BOOL result = [self.viewModel checkPregnancyValid:pregnancyDate
                                        completeBlock:nil];
    return result;
}

- (void)savePregancyWithDate:(NSDate *)date {
    
    [self.viewModel savePregancyWithDate:date];
}

- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate {
    BOOL result = [self.viewModel makeSureShortPregnacy:dueDate];
    return result;
}

#pragma mark - pickers
- (void)showMenseIntervalPicker {
    NSMutableArray *array = [[NSMutableArray alloc] init];
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSInteger min = MAX(IMYRecordMenseCycleLengthMin, IMYPublicAppHelper.shareAppHelper.localMensesDay + 4);
    NSInteger max = IMYRecordMenseCycleLengthMax;
    for (int j = min; j <= max; j++) {
#else
    for (int j = 17; j <= 60; j++) {
#endif
        [array addObject:[NSString stringWithFormat:IMYString(@"%d天"), j]];
    }
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                      dataArray:@[array]
                                                                  pickerViewTpe:IMYPickerViewTypeCustom
                                                                   confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                                                                       @strongify(self);
                                                                       NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
                                                                       NSDate *dueDate = [self.model getDueDateWithCalculateDate:self.model.calculateDate calculateInterval:day.integerValue];
                                                                        NSDate *startDate = startDate = [dueDate dateByAddingDays:-280];
                                                                        //是否有冲突的孕期数据
                                                                        NSArray *conflictArray = [self.viewModel conflictPregnacyDatesWithNewDueDate:dueDate];
                                                                        if (conflictArray.count > 0) {
                                                                            [self handlePregnacyConflictWithConflictArray:conflictArray dueDate:dueDate result:result type:SYPregnacyModeSettingTypeTmpMenseInterval];
                                                                            return;
                                                                        }
        [IMYRecordPregnancyBabyManager changeGestationWithWithStartDate:startDate
                                                                dueDate:dueDate
                                                            lastDueDate:self.model.date
                                                          completeBlock:^(NSError * _Nullable error, BOOL isToServer) {
            @strongify(self);
            if (!error) {
                self.model.calculateInterval = day.integerValue;
                IMYSimpleCellModel *cellModel = [self cellModelForType:SYPregnacyModeSettingTypeTmpMenseInterval];
                cellModel.content = self.model.tmpMenseIntervalString;

                if ([self.viewModel isCrossAnotherPregnacy:self.model.date]) {
                    //预产期与上个孕期重叠
    #if __has_include(<IMYRecord/IMYRecord.h>)
                    [[IMYURIManager shareURIManager] runActionWithString:kURIIMYPregnacyManager];
    #endif
                } else {
                    cellModel = [self cellModelForType:SYPregnacyModeSettingTypeDueDate];
                    cellModel.content = [self dueDateStringWithCalculate:YES];
                }
                [self showCalculateResulte];

                [self.tableView reloadData];
                [self.pickerView hide];
                if (isToServer) {
                    [self changePregnancePostDataToServer];
                }
            }
        }];

                                                                   }
                                                                    cancelBlock:^{

                                                                    }];
    picker.title = IMYString(@"选择周期天数");
    picker.outDismissControl = YES;
    self.pickerView = picker;
    NSInteger length = self.model.calculateInterval;
    [self.pickerView setSelectWithString:[NSString stringWithFormat:IMYString(@"%ld天"), length]];
    [self.pickerView show];
#endif
}

- (void)showDueDatePicker {
    @weakify(self);

    NSArray *data = @[[self.viewModel miniPregnacyPickerDate], [[NSDate imy_today] dateByAddingDays:280]];
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                      dataArray:data
                                                                  pickerViewTpe:IMYPickerViewTypeDate
                                                                   confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                                                                       @strongify(self);
                                                                        //验证预产期是否有效
                                                                       if ([self checkPregnancyValidate:result.resultDate]) {
                                                                           //是否有冲突的孕期数据
                                                                           NSArray *conflictArray = [self.viewModel conflictPregnacyDatesWithNewDueDate:result.resultDate];
                                                                           if (conflictArray.count > 0) {
                                                                               [self handlePregnacyConflictWithConflictArray:conflictArray dueDate:result.resultDate result:result type:SYPregnacyModeSettingTypeDueDate];
                                                                           } else {
                                                                               if ([self.viewModel isCrossAnotherPregnacy:result.resultDate]) {
                                                                                   //预产期与上个孕期重叠
#if __has_include(<IMYRecord/IMYRecord.h>)
                                                                                   [[IMYURIManager shareURIManager] runActionWithString:kURIIMYPregnacyManager];
#endif
                                                                               } else {
                                                                                   [self updateDueDateWithDate:result.resultDate completeBlock:^(NSError * _Nullable error) {
                                                                                       @strongify(self);
                                                                                       if (!error) {
                                                                                                                                                                [self.pickerView hide];
                                                                                           [self.tableView reloadData];
                                                                                       }
                                                                                   }];
                                                                                   return;
//                                                                                   [self updateDueDateWithDate:result.resultDate];
                                                                               }
                                                                               [self.pickerView hide];
                                                                               [self.tableView reloadData];
                                                                           }
                                                                       } else {
                                                                           [self.pickerView setSelectWithDate:result.resultDate];
                                                                           [self.pickerView hide];
                                                                           [self.tableView reloadData];
                                                                       }
                                                                   }
                                                                    cancelBlock:^{
                                                                    }];
    [picker setSelectedRowDidChangedBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray){

    }];
    picker.title = IMYString(@"选择预产期");
    picker.outDismissControl = YES;
    self.pickerView = picker;
    if (self.isShowOKButton) {
        [self.pickerView setSelectWithDate:[NSDate imy_today]];
    } else {
        [self.pickerView setSelectWithDate:self.model.date];
    }
    [self.pickerView show];
}

- (void)showCalculatePicker {
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                      dataArray:@[[self.viewModel calculateMinDate], [NSDate imy_today]]
                                                                  pickerViewTpe:IMYPickerViewTypeDate
                                                                   confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
                                                                       @strongify(self);
                                                                        NSDate *dueDate = [self.model getDueDateWithCalculateDate:result.resultDate calculateInterval:self.model.calculateInterval];
        
                                                                        //是否有冲突的孕期数据
                                                                        NSArray *conflictArray = [self.viewModel conflictPregnacyDatesWithNewDueDate:dueDate];
                                                                        if (conflictArray.count > 0) {
                                                                            [self handlePregnacyConflictWithConflictArray:conflictArray dueDate:dueDate result:result type:SYPregnacyModeSettingTypeTmpLastMense];
                                                                            return;
                                                                        }
        NSDate *startDate = startDate = [dueDate dateByAddingDays:-280];
        [IMYRecordPregnancyBabyManager changeGestationWithWithStartDate:startDate
                                                                dueDate:dueDate
                                                            lastDueDate:self.model.date
                                                          completeBlock:^(NSError * _Nullable error, BOOL isToServer) {
            @strongify(self);
            if (!error) {
                self.model.calculateDate = result.resultDate;
                IMYSimpleCellModel *cellModel = [self cellModelForType:SYPregnacyModeSettingTypeTmpLastMense];
                 NSDate *date = self.model.calculateDate;
                 NSString *dateString = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
                 cellModel.content = dateString;
                if ([self.viewModel isCrossAnotherPregnacy:self.model.date]) {
                    //预产期与上个孕期重叠
#if __has_include(<IMYRecord/IMYRecord.h>)
                    [[IMYURIManager shareURIManager] runActionWithString:kURIIMYPregnacyManager];
#endif
                } else {
                    cellModel = [self cellModelForType:SYPregnacyModeSettingTypeDueDate];
                    cellModel.content = [self dueDateStringWithCalculate:YES];
                }
                [self showCalculateResulte];
                [self.tableView reloadData];
                [self.pickerView hide];
                if (isToServer) {
                    [self changePregnancePostDataToServer];
                }
            }
        }];

                                                                   }
                                                                    cancelBlock:^{
                                                                    }];
    picker.title = IMYString(@"最后一次经期开始日");
    picker.outDismissControl = YES;
    self.pickerView = picker;
    [self.pickerView setSelectWithDate:self.model.calculateDate];
    [self.pickerView show];
#endif
}

- (void)showCalculateResulte {
    //    根据推算您的预产期时间为：xxxx年xx月xx日
    NSDate *date = self.model.date;
    NSString *dateString = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
    NSString *result = [NSString stringWithFormat:@"推算预产期为\n%@", dateString];
    [self imy_showTextHUD:result];
}

/// 处理设置预产期与旧孕期数据冲突
- (void)handlePregnacyConflictWithConflictArray:(NSArray *)conflictArray dueDate:(NSDate *)dueDate result:(IMYPickerViewResultModel *)result type:(SYPregnacyModeSettingType)type {
#if __has_include(<IMYRecord/IMYRecord.h>)
    [self.pickerView hide];
    NSDateFormatter *format = [NSDateFormatter imy_getCN_DateFormater2];
    NSString *dueDateString = [format stringFromDate:dueDate];
    NSMutableArray *array = [NSMutableArray array];
    for (IMYPregnanceModel *model in conflictArray) {
        [array addObject:[NSString stringWithFormat:@"%@-%@",[self getDateStringWithDate:model.startDate],[self getDateStringWithDate:model.endDate]]];
    }
    NSString *message = [NSString stringWithFormat:IMYString(@"确认预产期设置为%@吗？确认后将删除冲突历史孕期记录（%@）"),dueDateString,[array componentsJoinedByString:@"、"]];
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:message];
    [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor imy_colorForKey:kCK_Red_A] range:[message rangeOfString:dueDateString]];
    
    UILabel *contentLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, [IMYActionMessageBox contentWidth], 20)];
    [contentLabel imy_setTextColorForKey:kCK_Black_A];
    contentLabel.textAlignment = NSTextAlignmentCenter;
    contentLabel.numberOfLines = 0;
    contentLabel.font = [UIFont systemFontOfSize:14];
    contentLabel.attributedText = attributedString;
    [contentLabel sizeToFit];
    contentLabel.imy_height += 5;
    
    @weakify(self);
    IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"请确认预产期")
                                                                view:contentLabel
                                                              action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        @strongify(self);
        if (sender == messageBox.rightButton) {
            NSMutableArray *gidArray = [[NSMutableArray alloc] init];
            for (IMYPregnanceModel *model in conflictArray) {
                [gidArray addObject:@(model.gestation_id)];
            }
            [IMYRecordPregnancyBabyManager deleteGestationWithGestationIdList:gidArray isConflict:YES loadingBtn:self.viewModel.loadingBtn completeBlock:^(NSError * _Nullable error) {
                @strongify(self);
                if (!error) {
                    //立刻上传妊娠流水数据为 NO
                    for (IMYPregnanceModel *model in conflictArray) {
                        [IMYDayRecordModel deletePregnancyWithPModel:model];
                    }
                    [IMYPregnanceModel refreshAllPregnances];
                    NSDate *startDate = startDate = [dueDate dateByAddingDays:-280];
//                    [IMYRecordPregnancyBabyManager addPregnancyBabyToServerWithDueDate:dueDate loadingBtn:nil scene:@"conflictArray" completed:^(NSError * _Nonnull error) {
//                        if (!error) {

                            [self.pickerView hide];
                            [messageBox dismiss];
//                            [UIWindow imy_showTextHUD:IMYString(@"预产期设置成功")];
                            if (type == SYPregnacyModeSettingTypeDueDate) {
                                [self updateDueDateWithDate:dueDate];
                            } else if (type == SYPregnacyModeSettingTypeSeting) {
                                [self changeMode:nil];
                            } else {
                                [self updatePeriodInfoWithResult:result type:type];
                            }
//                        }
//
//                    }];
          
                }
            }];
     
        } else if (sender == messageBox.leftButton) {
            if (type == SYPregnacyModeSettingTypeSeting || type == SYPregnacyModeSettingTypeDueDate) {
                [self showDueDatePicker];
            } else if (type == SYPregnacyModeSettingTypeTmpLastMense) {
                [self showCalculatePicker];
            } else if (type == SYPregnacyModeSettingTypeTmpMenseInterval) {
                [self showMenseIntervalPicker];
            }
            [messageBox dismiss];
        }
    }];
    box.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    [box.leftButton imy_setTitle:IMYString(@"重新选择")];
    [box.leftButton imy_setTitleColor:kCK_Black_B];
    [box.rightButton imy_setTitle:IMYString(@"确认设置")];
#endif
}

- (NSString *)getDateStringWithDate:(NSDate *)date {
    return [NSString stringWithFormat:@"%ld.%ld.%ld",date.year,date.month,date.day];
}

- (void)updateDueDateWithDate:(NSDate *)resultDate completeBlock:(void (^)(NSError *_Nullable error))completeBlock  {
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    [IMYRecordPregnancyBabyManager changeGestationWithWithStartDate:[resultDate dateByAddingDays:-280]
                                                            dueDate:resultDate
                                                        lastDueDate:self.model.date
                                                      completeBlock:^(NSError * _Nullable error, BOOL isToServer) {
        @strongify(self);
        if (!error) {
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
                [UIWindow imy_showTextHUD:@"保存成功"];
            }
            self.model.date = resultDate;
            self.pregnacyStaredDate = [resultDate dateByAddingDays:-280];
            BOOL isShortPregnacy = ![self makeSureShortPregnacy:resultDate];
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYPregnacyModeSettingTypeDueDate];
            cellModel.content = [self dueDateStringWithCalculate:NO];
            if (isShortPregnacy) {
                //孕期内有经期数据
        #if __has_include(<IMYRecord/IMYRecord.h>)
                [[IMYURIManager shareURIManager] runActionWithPath:kURIIMYMensesManager params:@{@"lastPregnanceStartDate": [resultDate dateByAddingDays:-280]} info:nil];
        #endif
            }
            [self.tableView reloadData];
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
                [self changePregnancePostDataToServer];
                [self updatePregnacyDate];
            }
        }
        !completeBlock? : completeBlock(error);
    }];
#endif
}

- (void)updateDueDateWithDate:(NSDate *)resultDate {
    BOOL isShortPregnacy = ![self makeSureShortPregnacy:resultDate];
    self.model.date = resultDate;
    self.pregnacyStaredDate = [resultDate dateByAddingDays:-280];
    IMYSimpleCellModel *cellModel = [self cellModelForType:SYPregnacyModeSettingTypeDueDate];
    cellModel.content = [self dueDateStringWithCalculate:NO];
    if (isShortPregnacy) {
        //孕期内有经期数据
#if __has_include(<IMYRecord/IMYRecord.h>)
        [[IMYURIManager shareURIManager] runActionWithPath:kURIIMYMensesManager params:@{@"lastPregnanceStartDate": [resultDate dateByAddingDays:-280]} info:nil];
#endif
    }
    [self.tableView reloadData];
}

- (void)updatePeriodInfoWithResult:(IMYPickerViewResultModel *)result type:(SYPregnacyModeSettingType)type {
    IMYSimpleCellModel *cellModel = [self cellModelForType:type];
    if (type == SYPregnacyModeSettingTypeTmpLastMense) {
        self.model.calculateDate = result.resultDate;
        NSDate *date = self.model.calculateDate;
        NSString *dateString = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
        cellModel.content = dateString;
    } else if (type == SYPregnacyModeSettingTypeTmpMenseInterval) {
        NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
        self.model.calculateInterval = day.integerValue;
        cellModel.content = self.model.tmpMenseIntervalString;
    }
    cellModel = [self cellModelForType:SYPregnacyModeSettingTypeDueDate];
    cellModel.content = [self dueDateStringWithCalculate:YES];
    [self showCalculateResulte];
    [self.tableView reloadData];
}

#pragma mark - method
- (IMYSimpleCellModel *)cellModelForType:(NSInteger)type {
    return [self.items match:^BOOL(IMYSimpleCellModel *_Nonnull element) {
        if ([element isKindOfClass:[IMYSimpleCellModel class]]) {
            return element.type == type;
        }
        return NO;
    }];
}

- (void)releasePickerView {
    if (self.pickerView) {
        [self.pickerView freePicker];
        self.pickerView = nil;
    }
}

- (void)showPicker:(IMYSimpleCellModel *)model {
    [self releasePickerView];
    if (model.type == SYPregnacyModeSettingTypeDueDate) {
        [self showDueDatePicker];
    } else if (model.type == SYPregnacyModeSettingTypeTmpMenseInterval) {
        [self showMenseIntervalPicker];
    } else if (model.type == SYPregnacyModeSettingTypeTmpLastMense) {
        [self showCalculatePicker];
    }
}

#pragma mark - datasource

- (void)initModel {
    self.model = [SYModeSelectModel new];
    self.model.calculateInterval = [IMYPublicAppHelper shareAppHelper].parsInterval;
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYMensesModel *mense = [IMYMensesDao getLastMenses];
    if (!mense) {
        self.model.calculateDate = [NSDate imy_today];
    } else {
        NSDate *minDate = [[NSDate imy_today] dateByAddingDays:-280];
        if ([mense.mstartdate compare:minDate] == NSOrderedAscending) {
            self.model.calculateDate = [NSDate imy_today];
        } else {
            self.model.calculateDate = mense.mstartdate;
        }
    }
    self.model.date = [[IMYCalendarUserHelper sharedHelper].pregnancy_for294 imy_getOnlyDate];
    if (self.model.date == nil) {
        NSArray *dates = [SYUserModeViewModel dueDatedOfChildbirth];
        self.model.date = dates.lastObject;
        self.pregnacyStaredDate = dates.firstObject;
    } else {
        self.pregnacyStaredDate = [IMYPregnanceModel getPregnancyModelWithDate:[NSDate imy_today]].startDate;
    }
    //超过42周了 做个兜底数据
    if ([self.pregnacyStaredDate distanceInDaysToDate:[NSDate imy_today]] > 294) {
        self.pregnacyStaredDate = [[NSDate imy_today] dateByAddingDays:-30];
    }
#endif
}

- (NSArray *)items {
    if (!_items) {
        NSMutableArray *array = [NSMutableArray array];

        [array addObject:IMYString(@"预产期是哪天？")];
        IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
        cellModel.icon = @"data_icon_yuchanqi.png";
        cellModel.title = IMYString(@"设置预产期");
        cellModel.content = [self dueDateStringWithCalculate:NO];
        cellModel.type = SYPregnacyModeSettingTypeDueDate;
        [array addObject:cellModel];
        //插入一个空格
        [array addObject:[NSNull null]];

        cellModel = [IMYSimpleCellModel new];
        cellModel.icon = @"mine_icon_jisuan.png";
        cellModel.title = IMYString(@"计算预产期");
        cellModel.type = SYPregnacyModeSettingTypeExpand;
        [array addObject:cellModel];

        cellModel = [IMYSimpleCellModel new];
        cellModel.title = IMYString(@"预产期计算说明");
        cellModel.type = SYPregnacyModeSettingTypeInstructions;
        [array addObject:cellModel];

        if (self.isChange) {
            //插入一个空格
            [array addObject:[NSNull null]];

            cellModel = [IMYSimpleCellModel new];
            cellModel.title = IMYString(@"开始怀孕模式");
            if ([IMYMeMineABManager sharedInstance].isMytabStyle1) {
                cellModel.title = IMYString(@"确认");
            }
            cellModel.type = SYPregnacyModeSettingTypeSeting;
            [array addObject:cellModel];
        }
        _items = [NSMutableArray arrayWithArray:array];
    }
    return _items;
}

- (void)reloadPregnacyItem {
    NSMutableArray *array = (NSMutableArray *)self.items;
    SYUseModeTableViewCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:3 inSection:0]];
    NSInteger instructionIndex = self.items.count - 1;
    if (self.isChange) {
        instructionIndex -= 2;
    }
    if (self.isExpended) {
        IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
        cellModel.title = IMYString(@"最后一次经期开始日");
        NSDate *date = self.model.calculateDate;
        NSString *dateString = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:date];
        cellModel.content = dateString;
        cellModel.type = SYPregnacyModeSettingTypeTmpLastMense;
        [array insertObject:cellModel atIndex:instructionIndex];

        cellModel = [IMYSimpleCellModel new];
        cellModel.title = IMYString(@"周期长度");
        cellModel.content = [NSString stringWithFormat:IMYString(@"%ld天"), [IMYPublicAppHelper shareAppHelper].parsInterval];
        cellModel.type = SYPregnacyModeSettingTypeTmpMenseInterval;
        [array insertObject:cellModel atIndex:instructionIndex + 1];

        cell.arrow.transform = CGAffineTransformRotate(CGAffineTransformIdentity, M_PI_2);
    } else {

        [array removeObjectAtIndex:instructionIndex - 1];
        [array removeObjectAtIndex:instructionIndex - 2];
        cell.arrow.transform = CGAffineTransformIdentity;
    }
    [self.tableView reloadData];
}


- (NSString *)dueDateStringWithCalculate:(BOOL)isCalculate {
    NSInteger week = 0;
    NSInteger day = 1;
    self.isUseCalculate = isCalculate;
    NSDate *modeDate = self.model.date;
    NSDate *startDate = self.pregnacyStaredDate ? self.pregnacyStaredDate : [modeDate dateByAddingDays:-280];
    if (isCalculate) {
        startDate = [self.model.date dateByAddingDays:-280];
        NSInteger daydiff = [startDate daysBeforeDate:[NSDate imy_today]];
        week = daydiff / 7;
        day = daydiff % 7;
        if (daydiff == 0) {
            day = 1;
        }
    } else {
        //取包含今天的孕期记录
        NSInteger daydiff = [[NSDate imy_today] daysAfterDate:startDate];
        if (daydiff <= 0) {
            daydiff = 1;
        }
        week = daydiff / 7;
        day = daydiff % 7;
    }
    NSString *dateStr = [[NSDateFormatter imy_getCN_DateFormater2] stringFromDate:modeDate];
    dateStr = dateStr ?: @"";
    if (day == 0) {
        return [NSString stringWithFormat:IMYString(@"%@（孕%d周）"), dateStr, week];
    }
    if (week > 41) {
        return [NSString stringWithFormat:IMYString(@"%@（孕42周）"), dateStr];
    }
    return [NSString stringWithFormat:IMYString(@"%@（孕%d周%d天）"), dateStr, week, day];
}

#pragma mark - TableView delegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.items.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    id model = self.items[indexPath.row];
    IMYSimpleCellModel *simpleModel = nil;
    if ([model isKindOfClass:[IMYSimpleCellModel class]]) {
        simpleModel = model;
    }
    if (simpleModel.type == SYPregnacyModeSettingTypeInstructions) {
        return 24;
    }
    if ([model isKindOfClass:[NSString class]]) {
        return 54;
    }
    if ([model isKindOfClass:[NSNull class]]) {
        return 24;
    }
    IMYSimpleCellModel *cellModel = model;
    if (cellModel.type == SYPregnacyModeSettingTypeDueDate) {
        return 70;
    }
    return 48;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    id obj = self.items[indexPath.row];
    IMYSimpleCellModel *model = nil;
    if ([obj isKindOfClass:[IMYSimpleCellModel class]]) {
        model = obj;
    }
    if ([obj isKindOfClass:[NSString class]] || model.type == SYPregnacyModeSettingTypeInstructions) {
        static NSString *textCell = @"textCell";
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:textCell];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:textCell];
            [cell imy_setupClearBackground];
            UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(24, 0, SCREEN_WIDTH - 48, 16)];
            label.font = [UIFont systemFontOfSize:12];
            label.tag = 101;
            [cell.contentView addSubview:label];
        }
        UILabel *label = (id)[cell.contentView viewWithTag:101];
        if (model.type == SYPregnacyModeSettingTypeInstructions) {
            [label imy_setTextColorForKey:kCK_Red_B];
            label.textAlignment = NSTextAlignmentRight;
            label.text = model.title;
            label.imy_top = 10;
            cell.selectionStyle = UITableViewCellSelectionStyleGray;
            [cell setSelectedBackgroundView:[UIView new]];
        } else {
            label.imy_top = 30;
            [label imy_setTextColorForKey:kCK_Black_B];
            label.textAlignment = NSTextAlignmentLeft;
            label.text = obj;
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }

        return cell;
    } else if ([obj isKindOfClass:[NSNull class]]) {
        static NSString *emtpyCell = @"emptyCell";
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:emtpyCell];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:emtpyCell];
            [cell imy_setupClearBackground];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }
        return cell;
    } else if (model.type == SYPregnacyModeSettingTypeSeting) {
        static NSString *settingCell = @"settingCell";
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:settingCell];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:settingCell];

            IMYCKLoadingTextButton *loadingBtn = [[IMYCKLoadingTextButton alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 48)];
            loadingBtn.titleLabel.font = [UIFont systemFontOfSize:17];
            [loadingBtn setTitle:IMYString(@"开始怀孕模式") buttonType:IMYCKLoadingButtonRedType];
            [loadingBtn imy_drawAllCornerRadius:12];
            
            loadingBtn.titleColor = nil;
            loadingBtn.contentColor = nil;
            loadingBtn.borderColor = nil;
            [loadingBtn imy_addThemeChangedBlock:^(IMYCKLoadingTextButton *weakObject) {
                [weakObject setTitleColor:[UIColor imy_colorForKey:kCK_Red_E] forState:UIControlStateNormal];
                weakObject.backgroundColor = [UIColor imy_colorForKey:kCK_White_AN];
                weakObject.indicatorView.activityIndicatorViewStyle = IMYPublicAppHelper.shareAppHelper.isNight ? UIActivityIndicatorViewStyleWhite : UIActivityIndicatorViewStyleGray;
            }];
            
            loadingBtn.userInteractionEnabled = NO;
            loadingBtn.tag = 101;
            self.viewModel.loadingBtn = loadingBtn;
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            cell.contentView.backgroundColor = [UIColor clearColor];
            cell.backgroundColor = [UIColor clearColor];
            [cell.contentView addSubview:loadingBtn];
            @weakify(self);
            [[loadingBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
                @strongify(self);
                self.viewModel.dueDate = self.model.date;
                [self changeMode:nil];
            }];
            cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"SYModeConfirmViewController-change-%f", floor([[NSDate date] timeIntervalSince1970])];
            cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                [IMYEventHelper event:@"sfsz_ksanbg" attributes:@{@"leixing": IMYString(@"开始怀孕模式")}];
            };
        }
        IMYCKLoadingTextButton *loadingBtnTemp = (id)[cell.contentView viewWithTag:101];
        [loadingBtnTemp setTitle:model.title buttonType:IMYCKLoadingButtonRedType];
        return cell;
    } else {
        NSString *identifier = [NSString stringWithFormat:@"SYUseModeTableViewCell_%d_%d", indexPath.section, indexPath.row];
        SYUseModeTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
        if (!cell) {
            cell = [[SYUseModeTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
        }
        [cell setCellTypeTwoRow:model.type == SYPregnacyModeSettingTypeDueDate];
        [cell.icon imy_setImageForKey:model.icon];
        cell.title.imy_left = model.icon ? 55 : 16;
        cell.title.text = model.title;
        cell.detailLabel.font = [UIFont systemFontOfSize:model.icon?14:17];
        cell.detailLabel.text = model.content;
        if (model.type == SYPregnacyModeSettingTypeExpand) {
            [cell updateArrowMarginIsLeft:YES];
        } else {
            [cell updateArrowMarginIsLeft:NO];
        }
        IMYLineView *line;
        if (model.type == SYPregnacyModeSettingTypeTmpLastMense || model.type == SYPregnacyModeSettingTypeTmpMenseInterval) {
            if (model.type == SYPregnacyModeSettingTypeTmpLastMense) {
                [cell showCardStyleNOCorner];
                line = [cell.cardView imy_lineViewWithDirection:IMYDirectionUp show:YES margin:0];
            } else {
                [cell showCardStyleBottomCorner];
                line = [cell.cardView imy_lineViewWithDirection:IMYDirectionUp show:YES margin:12];
            }
            line.hidden = NO;
        } else {
            line.hidden = YES;
            if (self.isExpended && model.type != SYPregnacyModeSettingTypeDueDate) {
                [cell showCardStyleTopCorner];
            } else {
                [cell showCardStyleAllCorner];
            }
        }
        return cell;
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    id obj = self.items[indexPath.row];
    if ([obj isKindOfClass:[IMYSimpleCellModel class]]) {
        IMYSimpleCellModel *item = (IMYSimpleCellModel *)obj;
        if (item.type == SYPregnacyModeSettingTypeExpand) {
            self.isExpended = !self.isExpended;
            [self reloadPregnacyItem];
        } else if (item.type == SYPregnacyModeSettingTypeInstructions) {
            [self helperView];
        } else if (item.type == SYPregnacyModeSettingTypeSeting) {
            self.viewModel.dueDate = self.model.date;
            [self changeMode:nil];
        } else {
            [self showPicker:item];
        }
    }
}

- (void)helperView {
    [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": [NSString stringWithFormat:@"%@/help/yuchanqi.html", view_seeyouima_com]} info:nil];
}

#pragma mark - UI

- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
        [tableView imy_makeTransparent];
        tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        tableView.delegate = self;
        tableView.dataSource = self;
        [tableView registerClass:[SYUseModeTableViewCell class] forCellReuseIdentifier:@"SYUseModeTableViewCell"];
        _tableView = tableView;
    }
    return _tableView;
}

- (NSMutableDictionary *)followUpForChange {
    if (!_followUpForChange) {
        _followUpForChange = [NSMutableDictionary dictionary];
        @weakify(self);
        void (^block)(void) = ^{
            @strongify(self);
            [self savePregancyWithDate:self.model.date];
        };
        [_followUpForChange setObject:block forKey:@"DueDatePicker"];
    }
    return _followUpForChange;
}

@end
