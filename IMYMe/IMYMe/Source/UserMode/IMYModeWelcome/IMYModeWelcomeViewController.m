//
//  IMYModeWelcomeViewController.m
//  IMYMe
//
//  Created by lgw on 2022/8/5.
//

#import "IMYModeWelcomeViewController.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYModeWelcomeContentView.h"
#import "IMYModeWelcomeContentView_V2.h"
#import "SYUserModeSelectViewController.h"
#import "SYModeConfirmViewController.h"
#import "SYUserHelper.h"
#import "SYUserModeViewModel.h"
#import "SYUserModeChangeAction.h"
#import "SYModeSelectModel.h"
#import "IMYMeGlobalMacros.h"
#import "SYModeDetailViewController.h"
#import "IMYMeURIRegister.h"
#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#endif

#if __has_include( <ZZIMYMain/SYQuickStartBYInfoVC.h>)
#import <ZZIMYMain/SYQuickStartBYInfoVC.h>
#endif

#if __has_include( <ZZIMYMain/SYQuickStartYQInfoVC.h>)
#import <ZZIMYMain/SYQuickStartYQInfoVC.h>
#endif

#import "IMYCKLoadingTextButton.h"
#if __has_include(<IMYRecord/IMYRecord.h>)
#import "IMYRecordPregnancyBabyManager.h"
#endif

#if __has_include(<IMYYunyuHome/IMYYunyuChangeToLamaVC.h>)
#import <IMYYunyuHome/IMYYunyuChangeToLamaVC.h>
#endif



@interface IMYModeWelcomeViewController ()<UINavigationBarDelegate>
@property (nonatomic, strong) UIView *fakeBackCoverView;
@property (nonatomic, strong) IMYModeWelcomeContentView *contentView;
@property (nonatomic, strong) IMYModeWelcomeContentView_V2 *contentViewV2;
@property (nonatomic, strong) SYUserModeViewModel *viewModel;
@property (nonatomic, strong) NSDate *lastPregnanceDue; //
@property (nonatomic, assign) BOOL isAddNewBaby;///< 是否添加新宝宝


@property (nonatomic, strong) SYModeSelectModel *tmpData;
@property (nonatomic, strong) NSString *originalPregnacy;
@property (nonatomic, strong) SYModeSelectModel *originalData;
@property (nonatomic, assign) BOOL needBabyFirstResponse;

@property (nonatomic, strong) NSDate *defaultPregnacyDate;

@end

@implementation IMYModeWelcomeViewController


- (BOOL)isNavigationBarHidden {
    return YES;
}

- (BOOL)isWhiteNavigationBar {
    return YES;
}

- (void)dealloc {
    if (self.type == IMYVKUserModePregnancy) {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYMensesDao getMensesAll].count == 0) {
        NSDate *date = [IMYPregnanceModel getLastPregnancyModel].startDate;
        IMYDayRecordModel *daymodel = [IMYDayRecordDao searchDayRecordWithDate:date];
        if (!daymodel) {
            daymodel = [IMYDayRecordModel new];
            daymodel.date = date;
        }
        daymodel.isEnd = NO;
        daymodel.isBegin = YES;
        [IMYDayRecordDao mensesInsertToDB:daymodel];
    }
    if ([[[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate] compare:self.defaultPregnacyDate] != NSOrderedSame) {
        [SYUserHelper sharedHelper].pars_is_sync = NO;
        //修改预产期后刷新AB
        [[IMYABTestManager sharedInstance] refresh];
        //修改了宝宝数据，及时同步数据
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordUploadDataImmediately object:nil];
        
    }
#endif
    }
}

-(UIStatusBarAnimation)preferredStatusBarUpdateAnimation {
    return UIStatusBarAnimationFade;
}

- (BOOL)shouldFakeNavigationBarTransition {
    return YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    [self.view imy_setBackgroundColor:kCK_Clear_A];
    self.hideNavBarBottomLine = YES;
    UIView *fakeBackCoverView = [[UIView alloc] initWithFrame:self.view.bounds];
    fakeBackCoverView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    [self.view addSubview:fakeBackCoverView];
    [fakeBackCoverView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.top.equalTo(self.view).offset(-SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
    }];
    self.fakeBackCoverView = fakeBackCoverView;
    self.fakeBackCoverView.alpha = 0;
    
    [self configContentView];
    [self initTmpData];
    
    if (self.toMode == IMYVKUserModeNormal) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(closeForChangeMode) name:@"SYChangeUserModelNotifi" object:nil];
    }

}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    [self.view imy_setBackgroundColor:kCK_Black_F];
    self.fakeBackCoverView.alpha = 1;
    
    if (self.backCoverView) {
        [self.backCoverView removeFromSuperview];
        self.backCoverView = nil;
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)closeAction{
    [UIView animateWithDuration:0.2 animations:^{
        self.fakeBackCoverView.alpha = 0;
    }];
    [self dismissViewControllerAnimated:YES completion:^{
        //...
    }];
}

- (void)closeNotAnimAction{
    [UIView animateWithDuration:0.2 animations:^{
        self.fakeBackCoverView.alpha = 0;
    }];
    [self dismissViewControllerAnimated:NO completion:^{
        //...
    }];
}

- (void)closeForChangeMode{
    [UIView animateWithDuration:0.2 animations:^{
        self.fakeBackCoverView.alpha = 0;
    }];
    if (self.hasTwoPresent) {
        @weakify(self);
        [self dismissViewControllerAnimated:NO completion:^{
            @strongify(self);
            [self dismissViewControllerAnimated:NO completion:^{
                //...
            }];
        }];
    }else{
        [self dismissViewControllerAnimated:YES completion:^{
            //...
        }];
    }
}

- (void)configContentView {
    if ([IMYMeURIRegister isNewModeChangeWelcomePage]) {
        [self configContentViewV2];
        return;
    }
    self.contentView = [IMYModeWelcomeContentView new];
    self.contentView.type = self.type;
    self.contentView.fromName = @"recordSelect";
    self.viewModel.loadingBtn = self.contentView.nextBtn;
    @weakify(self);
    [self.contentView setCloseBlock:^{
        @strongify(self);
        [self closeAction];
    }];

    self.contentView.nextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        !self.nextActionBlock?:self.nextActionBlock(self.type);
        if (self.toMode == IMYVKUserModeForPregnant) {  //切换到备孕模式
            [self prepareChangeToForPregnantWithToMode:self.toMode lastMode:self.lastMode];
        }else if (self.toMode == IMYVKUserModePregnancy){ //切换到怀孕模式
            [self prepareChangeToPregnancyWithToMode:self.toMode lastMode:self.lastMode];
        }else if (self.toMode == IMYVKUserModeLama){//切换到育儿模式
            [self prepareChangeToLamaWithToMode:self.toMode lastMode:self.lastMode];
        }else{
            [self prepareChangeToForJingqiWithToMode:self.toMode lastMode:self.lastMode];
        }
    };

    [self.view addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.view);
        make.top.equalTo(self.view).offset(0);
    }];
}

- (void)configContentViewV2 {
    self.contentViewV2 = [IMYModeWelcomeContentView_V2 new];
    self.contentViewV2.type = self.type;
    self.contentViewV2.fromName = @"recordSelect";
    self.viewModel.loadingBtn = self.contentViewV2.nextBtn;

    @weakify(self);
    [self.contentViewV2 setCloseBlock:^{
        @strongify(self);
        [self closeAction];
    }];
    [self.contentViewV2 setCloseForChangeModeBlock:^{
        @strongify(self);
        [self closeNotAnimAction];
    }];

    self.contentViewV2.nextActionBlock = ^(id  _Nonnull sender) {
        @strongify(self);
        !self.nextActionBlock?:self.nextActionBlock(self.type);
        if (self.toMode == IMYVKUserModeForPregnant) {  //切换到备孕模式
            [self prepareChangeToForPregnantWithToMode:self.toMode lastMode:self.lastMode];
        }else if (self.toMode == IMYVKUserModePregnancy){ //切换到怀孕模式
            [self prepareChangeToPregnancyWithToMode:self.toMode lastMode:self.lastMode];
        }else if (self.toMode == IMYVKUserModeLama){//切换到育儿模式
            [self prepareChangeToLamaWithToMode:self.toMode lastMode:self.lastMode];
        }else{
            [self prepareChangeToForJingqiWithToMode:self.toMode lastMode:self.lastMode];
        }
    };

    [self.view addSubview:self.contentViewV2];
    [self.contentViewV2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(SCREEN_HEIGHT - SCREEN_STATUSBAR_HEIGHT);
        make.top.equalTo(self.view).offset(0);
    }];
}



#pragma mark 切换到经期模式

//处理切换到经期模式
- (void)prepareChangeToForJingqiWithToMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode {
    SYModeDetailViewController *vc = [[SYModeDetailViewController alloc] initWithUserModeViewModel:self.viewModel prepareMode:toMode];
    vc.fromChange = YES;
    [self imy_push:vc];
}

#pragma mark 切换到备孕模式

//处理切换到备孕模式
- (void)prepareChangeToForPregnantWithToMode:(IMYVKUserMode)toMode 
                                    lastMode:(IMYVKUserMode)lastMode {
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    void(^checkChangedBlock)(IMYVKUserMode toMode,IMYVKUserMode lastMode,BOOL deletePregnancy) = ^(IMYVKUserMode toMode,IMYVKUserMode lastMode,BOOL deletePregnancy){
        @strongify(self);
        if (lastMode == IMYVKUserModePregnancy && deletePregnancy) {//怀孕身份 并且需要删除妊娠
            [IMYRecordPregnancyBabyManager deleteGestation:NO
                                                loadingBtn:self.contentView.nextBtn ?:self.contentViewV2.nextBtn
                                            completeBlock :^(NSError * _Nullable error) {
                @strongify(self);
                if (!error) {
                    [self checkChanged:toMode lastMode:lastMode deletePregnancy:deletePregnancy];
                }
            }];
        } else {
            [self checkChanged:toMode lastMode:lastMode deletePregnancy:deletePregnancy];
        }
    };
    if (lastMode == IMYVKUserModeNormal) {  //非孕期身份
        checkChangedBlock(toMode,lastMode,NO);
    }else{  //孕期身份切换到备孕
        @weakify(self);
        [self.viewModel welcomeStyleCheckAlterConfirmChangeToMode:toMode
                                                changeToModeBlock:^(IMYVKUserMode toMode) {
            @strongify(self);
            checkChangedBlock(toMode,lastMode,NO);
        } deletePregnancyAndChangeMode:^(IMYVKUserMode toMode) {
            checkChangedBlock(toMode,lastMode,YES);
        }];
    }
#endif
}
- (void)checkChanged:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode deletePregnancy:(BOOL)deletePregnancy {
#if __has_include(<IMYRecord/IMYRecord.h>)
        IMYMensesModel *lastMense = [IMYMensesDao getMensesAll].firstObject;
        //判断是否有经期记录。
        if (lastMense) { //有：当前页面向下收起。 切换到 -备孕首页
            if (deletePregnancy) {
                //清除次记录
                [IMYDayRecordModel deleteCurrentPregnancy];
            }
            [self closeForChangeMode];
            [self.viewModel changeToMode:toMode];
            [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:YES];
        }else{ //无 。进入备孕信息填写（快速设定）
#if __has_include( <ZZIMYMain/SYQuickStartBYInfoVC.h>)
            SYQuickStartBYInfoVC *qsVc = [SYQuickStartBYInfoVC new];
            qsVc.isFromWelcome = YES;
            @weakify(self);
            [qsVc setFinishedBlock:^(NSDate *date) {
                @strongify(self);
                if (deletePregnancy) {
                    //清除次记录
                    [IMYDayRecordModel deleteCurrentPregnancy];
                }
                [self closeForChangeMode];
                [SYUserHelper sharedHelper].bMarry = YES;
                if (date) {
                    IMYDayRecordModel *daymodel = [IMYDayRecordDao searchDayRecordWithDate:date];
                    if (!daymodel) {
                        daymodel = [IMYDayRecordModel new];
                        daymodel.date = date;
                    }
                    daymodel.isEnd = NO;
                    daymodel.isBegin = YES;
                    [IMYDayRecordDao mensesInsertToDB:daymodel];
                }
                [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
                [self.viewModel changeToMode:toMode];
                [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:YES];
            }];
            [self imy_push:qsVc];
#endif
        }
#endif
}

#pragma mark 切换到怀孕模式

//处理切换到怀孕模式
- (void)prepareChangeToPregnancyWithToMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode {
#if __has_include(<IMYRecord/IMYRecord.h>)
        if ([[IMYRecordBabyManager sharedInstance] babyList].count >= 5) {
            NSString *message = @"宝宝数量已达上限，开始怀孕模式，需至少删除一个宝宝";
            [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                     otherTitles:@[IMYString(@"管理宝宝")]
                                         summary:message
                                      showInView:[UIWindow imy_getShowTopWindow]
                                          action:^(NSInteger index) {
                if (index) {
                    IMYURI *uri = [IMYURI uriWithPath:@"user/baby/list" params:@{@"hideAdd":@(YES)} info:nil];
                    [IMYURIManager.shareURIManager runActionWithURI:uri];
                }
            }];
            return;
        }
#endif
    @weakify(self);
    void(^checkChangedBlock)(IMYVKUserMode toMode,IMYVKUserMode lastMode, NSDate *dueDate, IMYCKLoadingTextButton *loadingButton) = ^(IMYVKUserMode toMode,IMYVKUserMode lastMode,NSDate *dueDate, IMYCKLoadingTextButton *loadingButton){
        @strongify(self);
        BOOL isShortPregnacy = ![self makeSureShortPregnacy:dueDate];
        if (isShortPregnacy && self.lastPregnanceDue && [self.lastPregnanceDue isEqualToDate:dueDate]) {
            isShortPregnacy = NO; //上次已经判断了 本次设置不在判断 经期冲突
        }
        if (isShortPregnacy) {
            //孕期内有经期数据
#if __has_include(<IMYRecord/IMYRecord.h>)
            [[IMYURIManager shareURIManager] runActionWithPath:kURIIMYMensesManager params:@{@"lastPregnanceStartDate": [dueDate dateByAddingDays:-280]} info:nil];
#endif
            self.lastPregnanceDue = dueDate;
            return;
        }
        self.viewModel.dueDate = dueDate;
        @weakify(self);
        [self.viewModel welcomeStyleCheckAlterConfirmChangeToMode:toMode
                                                changeToModeBlock:^(IMYVKUserMode toMode) {
            @strongify(self);
            [self.viewModel changeToPregnacyWithDueDate:dueDate scene:@"welcomeStyle" loadingBtn:loadingButton completeBlock:^(BOOL result) {
                @strongify(self);
                if (result) {
                    [self closeForChangeMode];
                    [self savePregancyWithDate:dueDate];
                    [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:YES];
                }
            }];

        } deletePregnancyAndChangeMode:^(IMYVKUserMode toMode) {
            @strongify(self);
            [self.viewModel changeToPregnacyWithDueDate:dueDate scene:@"deletePregnancy" loadingBtn:loadingButton completeBlock:^(BOOL result) {
                @strongify(self);
                if (result) {
                    //清除次记录
        #if __has_include(<IMYRecord/IMYRecord.h>)
                    [IMYDayRecordModel deleteCurrentPregnancy];
        #endif
                    [self closeForChangeMode];
                    [self savePregancyWithDate:dueDate];
                    [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:YES];
                }

            }];
                    
        }];
    };
#if __has_include( <ZZIMYMain/SYQuickStartBYInfoVC.h>)
    SYQuickStartYQInfoVC *qsVc = [SYQuickStartYQInfoVC new];
    qsVc.isFromWelcome = YES;
    [qsVc setFinishedSelectedDueBlock:^(NSDate *dueDate,IMYCKLoadingTextButton *loadingButton) {
        @strongify(self);
        //是否有冲突的孕期数据
        NSArray *conflictArray = [self.viewModel conflictPregnacyDatesWithNewDueDate:dueDate];
        if (conflictArray.count > 0) {
            @strongify(self);
            [self handlePregnacyConflictWithConflictArray:conflictArray dueDate:dueDate result:nil sureBlock:^{
                @strongify(self);
                checkChangedBlock(toMode,lastMode,dueDate,loadingButton);
            }];
            return;
        }

        checkChangedBlock(toMode,lastMode,dueDate,loadingButton);
    }];
    [self imy_push:qsVc];
#endif
}

- (void)savePregancyWithDate:(NSDate *)date {
    [self.viewModel savePregancyWithDate:date];
}

/// 处理设置预产期与旧孕期数据冲突
- (void)handlePregnacyConflictWithConflictArray:(NSArray *)conflictArray dueDate:(NSDate *)dueDate result:(IMYPickerViewResultModel *)result sureBlock:(void(^)(void))sureBlock{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDateFormatter *format = [NSDateFormatter imy_getCN_DateFormater2];
    NSString *dueDateString = [format stringFromDate:dueDate];
    NSMutableArray *array = [NSMutableArray array];
    for (IMYPregnanceModel *model in conflictArray) {
        [array addObject:[NSString stringWithFormat:@"%@-%@",[self getDateStringWithDate:model.startDate],[self getDateStringWithDate:model.endDate]]];
    }
    NSString *message = [NSString stringWithFormat:IMYString(@"确认预产期设置为%@吗？确认后将删除冲突历史孕期记录（%@）"),dueDateString,[array componentsJoinedByString:@"、"]];
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:message];
    [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor imy_colorForKey:kCK_Red_A] range:[message rangeOfString:dueDateString]];
    
    UILabel *contentLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, [IMYActionMessageBox contentWidth], 20)];
    [contentLabel imy_setTextColorForKey:kCK_Black_A];
    contentLabel.textAlignment = NSTextAlignmentCenter;
    contentLabel.numberOfLines = 0;
    contentLabel.font = [UIFont systemFontOfSize:14];
    contentLabel.attributedText = attributedString;
    [contentLabel sizeToFit];
    contentLabel.imy_height += 5;
    
    @weakify(self);
    IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:IMYString(@"孕期时间冲突")
                                                                view:contentLabel
                                                              action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        @strongify(self);
        if (sender == messageBox.rightButton) {
            NSMutableArray *gidArray = [[NSMutableArray alloc] init];
            for (IMYPregnanceModel *model in conflictArray) {
                [gidArray addObject:@(model.gestation_id)];
//                [IMYDayRecordModel deletePregnancyWithPModel:model];
            }
            [IMYRecordPregnancyBabyManager deleteGestationWithGestationIdList:gidArray
                                                                   isConflict:YES
                                                                   loadingBtn:self.contentView.nextBtn ?:self.contentViewV2.nextBtn
                                                                completeBlock:^(NSError * _Nullable error) {
//                if (!error) {
                    [messageBox dismiss];
                    !sureBlock?:sureBlock();
//                }
            }];

        } else if (sender == messageBox.leftButton) {  // 取消
            [messageBox dismiss];
        }
    }];
    box.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    [box.leftButton imy_setTitle:IMYString(@"重新选择")];
    [box.leftButton imy_setTitleColor:kCK_Black_B];
    [box.rightButton imy_setTitle:IMYString(@"确认设置")];
#endif
}



#pragma mark 换到育儿模式
//处理切换到育儿模式
- (void)prepareChangeToLamaWithToMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode {
    ///怀孕切辣妈-7.9.3版本
    if (IMYPublicAppHelper.shareAppHelper.userMode == IMYVKUserModePregnancy) {
        [self handlePregnancyToLamaMode:toMode];
        return ;
    }
    if (![SYUserModeSelectViewController canGotoLamaVC]) {
        [self closeForChangeMode];
        [SYUserModeSelectViewController gotoLamaSetVC];
        [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:NO];
        return;
    }
    [self handlePregnancyToQuickStartBabyMode:toMode lastMode:lastMode];
}

- (void)handlePregnancyToLamaMode:(IMYVKUserMode)toMode{
    IMYVKUserMode lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
    NSInteger pregnancyDays = 0;
    BOOL hasBaby = NO;
#if __has_include(<IMYRecord/IMYRecord.h>)
    pregnancyDays = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
    hasBaby = [IMYPublicAppHelper shareAppHelper].babyCount > 0;
#endif
   if (hasBaby == NO && pregnancyDays >= 140){ // 孕期 无宝宝 孕期天数 >= 140  直接进入 信息填写页面
       [self handlePregnancyToQuickStartBabyMode:toMode lastMode:lastMode];
   }else{
       __block IMYVKUserMode userMode = toMode;
       @weakify(self);
        [SYUserModeChangeAction checkPregnancyForWelcomeToLamaMode:self.viewModel.loadingBtn completionBlk:^(BOOL result, BOOL needAdd, NSArray * _Nonnull deleteBabyList) {
           @strongify(self);
           if (!result) {
               return ;
           }
           ///< 1. 不需要添加宝宝的直接原地切换身份
           if (!needAdd) {
               ///< 2.无宝宝的直接切换为经期身份
               if ([IMYPublicAppHelper shareAppHelper].babyCount == 0) {
                   userMode = IMYVKUserModeNormal;
                   imy_asyncMainBlock(0.3, ^{
                       [UIWindow imy_showTextHUD:IMYString(@"当前无宝宝，已切至经期身份")];
                   });
               }
               if (userMode == IMYVKUserModeLama) {
                   [SYUserModeSelectViewController gotoLamaSetVC];
               }
               [self closeForChangeMode];
               [self.viewModel changeToMode:userMode];
               [self changeToModeActionWithToMode:toMode lastMode:lastMode showToast:NO];
               return ;
           }
           /// 3.跳转到设置身份详情页面
   //        if (userMode == IMYVKUserModeLama) {
   //            if (![SYUserModeSelectViewController canGotoLamaVC]) {
   //                [SYUserModeSelectViewController gotoLamaSetVC];
   //            }
   //        }
           [self handlePregnancyToQuickStartBabyMode:toMode lastMode:lastMode];
       }];
   }

}

- (void)handlePregnancyToQuickStartBabyMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode {
#if __has_include(<IMYRecord/IMYRecord.h>)
    [self sync_handlePregnancyToQuickStartBabyMode:toMode lastMode:lastMode];
#endif
 }

- (void)sync_handlePregnancyToQuickStartBabyMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode {
    self.isAddNewBaby = NO;
    self.needBabyFirstResponse = YES;
#if __has_include(<IMYYunyuHome/IMYYunyuChangeToLamaVC.h>)
    IMYYunyuChangeToLamaVC *qsVc = [IMYYunyuChangeToLamaVC new];
    @weakify(self);
    [qsVc setFinishedSelectedBlock:^(NSString * _Nonnull birthdayString, NSInteger sex,IMYCKLoadingTextButton *loadingButton) {
        @strongify(self);
        NSDate *birthday = [birthdayString imy_getOnlyDate];
        self.tmpData.babyBirthday = birthday;
        @weakify(self);
        [SYUserModeChangeAction sync_turnOnLamaModeForWelcomeWithBabyBirthday:birthday completion:^(BOOL result, BOOL addNew, NSArray *deleteBabyList) {
            @strongify(self);
            if (!result) {
                return ;
            }
            self.isAddNewBaby = addNew;
            if (addNew) {
                @weakify(self);
                [self.viewModel welcomeStyleCheckAlterConfirmChangeToMode:toMode
                                                        changeToModeBlock:^(IMYVKUserMode toMode) {
                    @strongify(self);
                    [UIWindow imy_showTextHUDWithoutUI];
                    [loadingButton showLoading:YES];
                    [self.viewModel sync_saveLamaWithBabyBirthday:birthday sex:sex isNew:addNew andDeleteBabyList:nil isFromForcedHandoverLama:NO completion:^(BOOL result) {
                        [loadingButton showLoading:NO];
                        [UIWindow imy_hideHUD];
                        if (result) {
                            BOOL noBabyAndPregnancyDayLessThan140Days = NO;
                            NSInteger pregnancyDay = [SYUserModeChangeAction getPregnancyStartDayDiff:birthday];
                            NSInteger pregnancyDays2 = [IMYDayRecordModel getPregnancyStartDayDiffForParam];
                            if (pregnancyDays2 < 140){
                                if ([IMYRecordBabyManager sharedInstance].babyList.count <= 1 && pregnancyDay >= 0 && pregnancyDay < 140) {
                                    [IMYDayRecordModel deleteCurrentPregnancy];
                                    noBabyAndPregnancyDayLessThan140Days = YES;
                                }
                            }
                            [self closeForChangeMode];
                            [self.viewModel changeToMode:toMode];
                            if (self.needBabyFirstResponse) {
                    #if __has_include(<IMYRecord/IMYRecord.h>)
                                [IMYDayRecordModel resetBabyBirthDay:self.tmpData.babyBirthday inPregnan:[IMYPregnanceModel getLastPregnancyModel]];
                    #endif
                            }
                            [self defaultChanged];
                            [self turnToHomeTabWithMode:toMode];///跳转到首页逻辑
                        }
                    }];
                } deletePregnancyAndChangeMode:^(IMYVKUserMode toMode) {
                    @strongify(self);
                    [UIWindow imy_showTextHUDWithoutUI];
                    [loadingButton showLoading:YES];
                    [self.viewModel sync_saveLamaWithBabyBirthday:birthday sex:sex isNew:addNew andDeleteBabyList:nil isFromForcedHandoverLama:NO completion:^(BOOL result) {
                        [loadingButton showLoading:NO];
                        [UIWindow imy_hideHUD];
                        if (result) {
                            //清除次记录
                            [IMYDayRecordModel deleteCurrentPregnancy];
                            [self closeForChangeMode];
                            [self.viewModel changeToMode:toMode];
                            if (self.needBabyFirstResponse) {
                    #if __has_include(<IMYRecord/IMYRecord.h>)
                                [IMYDayRecordModel resetBabyBirthDay:self.tmpData.babyBirthday inPregnan:[IMYPregnanceModel getLastPregnancyModel]];
                    #endif
                            }
                            [self defaultChanged];
                            [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
                            [self turnToHomeTabWithMode:toMode];///跳转到首页逻辑
                        }
                    }];
                }];
            }
        }];
    }];
    
    [self imy_push:qsVc];
#endif
}

- (void)defaultChanged {
#if __has_include(<IMYRecord/IMYRecord.h>)
    if ([IMYPublicAppHelper shareAppHelper].hasLogin) {
        [[IMYCalendarJSHelper JSHelper] updateServerUserSwitch];
    }
    [IMYMensesModel refreshMenses];
    [self checkTmpDataHasChanged];
#endif
}

- (void)initTmpData {
    self.tmpData = [SYModeSelectModel new];
    self.tmpData.mode = [SYUserHelper sharedHelper].userModelType;
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.tmpData.pars_interval = [IMYCalendarUserHelper sharedHelper].parsInterval;
    self.tmpData.pars_menses_day = [IMYCalendarUserHelper sharedHelper].parsMensesDay;
#endif
    self.tmpData.babyBirthday = [[IMYPublicAppHelper shareAppHelper].babyBirthday imy_getOnlyDate];
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeLama || !self.tmpData.babyBirthday) {
        self.tmpData.babyBirthday = [NSDate imy_today];
    }
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.tmpData.date = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getDateZero];
    self.originalPregnacy = [IMYCalendarUserHelper sharedHelper].pregnancy;
#endif
    self.tmpData.babySex = [IMYPublicAppHelper shareAppHelper].baby_sex;
    self.originalData = [self.tmpData copy];
    
    if (self.type == IMYVKUserModePregnancy) {
        //辣妈不走这里了，先强制换成怀孕；
    #if __has_include(<IMYRecord/IMYRecord.h>)
        self.defaultPregnacyDate = [[IMYCalendarUserHelper sharedHelper].pregnancy imy_getOnlyDate];
    #endif
    }

}

- (void)checkTmpDataHasChanged {
    BOOL pushNoti = NO;
    if (![self.tmpData isEqual:self.originalData]) {
        pushNoti = YES;
    }
//    if (self.switchButton.on != self.switchOriginalOn) {
//        pushNoti = YES;
//    }
    // 开关
    if (pushNoti) {
#if __has_include(<IMYRecord/IMYRecord.h>)
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationRecordCalendarMenseChange object:nil];
#endif
    }

    [self checkBabyInfoChangeAndPostNotification];
}

- (void)checkBabyInfoChangeAndPostNotification {

    BOOL equalBabySex = self.tmpData.babySex == self.originalData.babySex;
    BOOL equalBabyBirthday = (!self.tmpData.babyBirthdayStr && !self.originalData.babyBirthdayStr) || [self.tmpData.babyBirthdayStr isEqualToString:self.originalData.babyBirthdayStr];

    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (!equalBabySex) {
        params[@"changedBabySex"] = @(YES);
    }

    if (!equalBabyBirthday) {
        params[@"changeBabyBirthday"] = @(YES);
        //修改宝宝生日后刷新AB
        [[IMYABTestManager sharedInstance] refresh];
    }

    if (params.count) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kSYUserBabyInfoChangedNotification object:params];
    }
}

/// 切换到首页, 原来逻辑是0.25秒后切换到首页
/// 797且辣妈, 需要添加判断是否要切换完直接跳到智能识别页面,需要异步请求数据.所以加了gcd
/// @param mode 当前模式
- (void)turnToHomeTabWithMode:(IMYVKUserMode)mode {
    @weakify(self);

    dispatch_group_t group = dispatch_group_create();
    dispatch_group_enter(group);
    dispatch_group_async(group, dispatch_get_main_queue(), ^{
        [self changeToModeActionWithToMode:self.toMode lastMode:self.lastMode showToast:YES];
        imy_asyncMainBlock(0.25, ^{
            dispatch_group_leave(group);
        });
    });

    __block BOOL canAiRecognition = NO;
    dispatch_group_enter(group);
    dispatch_group_async(group, dispatch_get_main_queue(), ^{
        ///切换到辣妈身份直接开始启动智能识别
        ///新增宝宝才走智能识别判断
        if (self.isAddNewBaby && mode == IMYVKUserModeLama) {
            canAiRecognition = YES;
            dispatch_group_leave(group);
            return ;
        } else {
            canAiRecognition = NO;
            dispatch_group_leave(group);
            return;
        }
    });
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (!canAiRecognition) {
            return ;
        }
        ///跳转到智能识别
        NSMutableDictionary *params = [[NSMutableDictionary alloc]  init];
        [params imy_setNonNilObject:[NSDate new] forKey:@"dueDate"];
        [params imy_setNonNilObject:@(-1) forKey:@"baby_id"];
        [params imy_setNonNilObject:@(9) forKey:@"position"];
        NSString *uriString = @"seeyoubaby/photo/show_leadpage_v2";
        IMYURI *uri = [IMYURI uriWithPath:uriString params:params info:nil];
        IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
        actionObject.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
            BOOL downloadFailed = [[result objectForKey:@"downloadFailed"] boolValue];
            if (downloadFailed) {
                
            }
        };
        [[IMYURIManager shareURIManager] runActionWithActionObject:actionObject completed:nil];
    });
}

- (NSString *)getDateStringWithDate:(NSDate *)date {
    return [NSString stringWithFormat:@"%ld.%ld.%ld",date.year,date.month,date.day];
}

- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate {
    BOOL result = [self.viewModel makeSureShortPregnacy:dueDate];
    return result;
}

- (void)changeToModeActionWithToMode:(IMYVKUserMode)toMode lastMode:(IMYVKUserMode)lastMode showToast:(BOOL)showToast{
    !self.changeToModeBlock?:self.changeToModeBlock(toMode,lastMode);
    if (showToast) {
        imy_asyncMainBlock(0.4, ^{
            [[UIApplication sharedApplication] endIgnoringInteractionEvents];
            NSString *modeStr = [IMYPublicAppHelper shareAppHelper].userModeName;
            [MBProgressHUD imy_showTextHUD:[NSString stringWithFormat:@"已切换至%@模式", modeStr]];
        });
    }
}


- (SYUserModeViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[SYUserModeViewModel alloc] init];
        _viewModel.position = self.position;
        _viewModel.loadingBtn = self.contentView.nextBtn ?:self.contentViewV2.nextBtn;
    }
    return _viewModel;
}

#pragma mark - Ga
- (NSString *)ga_pageName {
    if (self.toMode == IMYVKUserModeNormal) {///普通模式
        return @"IMYModeWelcomeViewControllerForJingqi";
    } else if (self.toMode == IMYVKUserModeForPregnant) {///备孕
        return @"IMYModeWelcomeViewControllerForPregnant";
    } else if (self.toMode == IMYVKUserModePregnancy) {///怀孕
        return @"IMYModeWelcomeViewControllerPregnancy";
    } else if (self.toMode == IMYVKUserModeLama) {///辣妈
        return @"IMYModeWelcomeViewControllerLama";
    }
    return @"IMYModeWelcomeViewController";
}



@end
