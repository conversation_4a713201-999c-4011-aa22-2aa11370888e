//
//  IMYModeWelcomeTipTagView.m
//  IMYMe
//
//  Created by lgw on 2025/8/25.
//

#import "IMYModeWelcomeTipTagView.h"
#import <IMYBaseKit/IMYBaseKit.h>


#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
#import <BBJViewKit/BBJGradientMaskView.h>
#endif


@interface IMYModeWelcomeTipTagView ()
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
@property (nonatomic, strong) BBJGradientMaskView *bgMaskView;
#endif
@property (nonatomic, strong) UILabel *titleLabel;
@end

@implementation IMYModeWelcomeTipTagView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self prepareUI];
    }
    return self;
}

- (void)prepareUI{
    self.backgroundColor = UIColor.clearColor;
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
    [self addSubview:self.bgMaskView];
    [self.bgMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
#endif
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}


+ (BOOL)switchTipTag{
    BOOL isSwitch = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"womens_health2.new_baby_package_welcome_page_label.switch"];
    return isSwitch;
}

+ (NSString *)tipTagText{
    NSString *text = @" 免费领取新生礼 ";
    NSString *content = [[IMYConfigsCenter sharedInstance] stringForKeyPath:@"womens_health2.new_baby_package_welcome_page_label.content"];
    if (imy_isNotBlankString(content)) {
        text = [NSString stringWithFormat:@" %@ ",content];
    }
    return text;
}
    
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
- (BBJGradientMaskView *)bgMaskView{
    if (!_bgMaskView) {
        BBJGradientMaskView *view = [BBJGradientMaskView new];
        id color1 = (__bridge id)[UIColor imy_colorForKey:@"#FFCB3F"].CGColor;
        id color2 = (__bridge id)[UIColor imy_colorForKey:@"#FF9F37"].CGColor;
        view.gradientLayer.colors = @[color1, color2];
        view.gradientLayer.locations =  @[@0.0, @1.0];
        view.gradientLayer.startPoint = CGPointMake(0, 0.5);
        view.gradientLayer.endPoint = CGPointMake(1, 0.5);
        // 设置渐变方向（从左到右）
//        gradientLayer.startPoint = CGPointMake(0, 0.5); // 左侧中间
//        gradientLayer.endPoint = CGPointMake(1, 0.5);   // 右侧中间
        _bgMaskView = view;
    }
    return _bgMaskView;
}

#endif

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        UILabel *lab = [UILabel new];
        lab.font = [UIFont imy_mediumWith:9];
        [lab imy_setTextColorForKey:kCK_White_A];
        lab.text = [IMYModeWelcomeTipTagView tipTagText];
        _titleLabel = lab;
    }
    return _titleLabel;
}

@end

