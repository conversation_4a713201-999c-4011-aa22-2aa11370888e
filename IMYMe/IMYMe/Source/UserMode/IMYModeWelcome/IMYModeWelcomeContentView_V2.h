//
//  IMYModeWelcomeContentView_V2.h
//  IMYMe
//
//  Created by lgw on 2022/8/5.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYPublic.h>

NS_ASSUME_NONNULL_BEGIN
@class IMYCKLoadingTextButton;
@interface IMYModeWelcomeContentView_V2 : UIView
@property (nonatomic, copy) void (^closeBlock)(void);
@property (nonatomic, copy) void (^closeForChangeModeBlock)(void);
@property (nonatomic, copy) void (^willNextActionBlock)(id sender);
@property (nonatomic, copy) void (^nextActionBlock)(id sender);
//显示身份类型的welcome
@property (nonatomic, assign) IMYVKUserMode type;
@property(nonatomic, strong) IMYCKLoadingTextButton *nextBtn;
//来源不同 (记录:recordSelect / 我的tab:mytabSelect)
@property(nonatomic, strong) NSString *fromName;
@end

NS_ASSUME_NONNULL_END
