//
//  IMYModeWelcomeContentView_V2.m
//  IMYMe
//
//  Created by lgw on 2022/8/5.
//

#import "IMYModeWelcomeContentView_V2.h"
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYCKLoadingTextButton.h"
#import "IMYModeWelcomeTipTagView.h"
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
#import <IMYRecord/IMYRecordBabyManager.h>
#endif

#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
#import <BBJViewKit/BBJGradientMaskView.h>
#endif
@interface IMYModeWelcomeContentView_V2 ()
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
@property (nonatomic, strong) BBJGradientMaskView *myMaskView;
#endif
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UIImageView *centerImageView;
@property (nonatomic, strong) IMYPAGView *pagView;
@property (nonatomic, strong) IMYButton *guideBtn;
@property (nonatomic, strong) IMYModeWelcomeTipTagView *tipTagView;

@property (nonatomic, assign) IMYVKUserMode lasUserMode;

@end

@interface IMYModeWelcomeContentView_V2 (BiAnalytics)

- (void)closeYouthModeActionSheetExposuredAnalytics:(NSInteger)entrance;

- (void)closeYouthModeActionSheetClickAnalytics:(NSInteger)entrance
                                     publicType:(NSString *)publicType;

@end

@implementation IMYModeWelcomeContentView_V2

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self prepareUI];
        [self imy_drawTopCornerRadius:12];
        //        self.viewModel = [IMYModeChangeContentModel_V2 defaultConfig];
        //        self.titleLabel.text = self.viewModel.title;
    }
    return self;
}

#pragma mark - Action

- (void)close {
    !self.closeBlock ? : self.closeBlock();
}

#pragma mark - private method
- (void)prepareUI {
    [self imy_addThemeChangedBlock:^(UIView *weakObject) {
        if (IMYPublicAppHelper.shareAppHelper.isNight) {
            weakObject.backgroundColor = [UIColor imy_colorForKey:@"#222222"];
        } else {
            weakObject.backgroundColor = [UIColor imy_colorForKey:@"#FFFFFF"];
        }
    }];
    //背景
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
    [self addSubview:self.myMaskView];
    [self.myMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(340);
    }];
#endif
    //头部
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.mas_equalTo(44);
        make.height.mas_equalTo(33);
    }];
    
    CGFloat topImageViewHeight = ((SCREEN_WIDTH - 2*24)/327.0) * 219;
    [self addSubview:self.topImageView];
    [self.topImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(12);
        make.left.mas_equalTo(24);
        make.right.mas_equalTo(-24);
        make.height.mas_equalTo(topImageViewHeight);
    }];
    
    [self addSubview:self.centerImageView];
    CGFloat nextBtnBottom = 20 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    [self addSubview:self.nextBtn];
    [self.nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40);
        make.height.mas_equalTo(48);
        make.right.mas_equalTo(-40);
        make.bottom.mas_equalTo(-nextBtnBottom);
    }];
    
    [self addSubview:self.guideBtn];
    [self.guideBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.nextBtn.mas_top).offset(-12);
        make.centerX.equalTo(self.nextBtn);
    }];
    CGFloat textWidth = [[IMYModeWelcomeTipTagView tipTagText] imy_getDrawSizeWithFont:[UIFont imy_mediumWith:9] maxSize:CGSizeMake(CGFLOAT_MAX, 14)].width;
    [self addSubview:self.tipTagView];
    [self.tipTagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.nextBtn.mas_right);
        make.top.mas_equalTo(self.nextBtn.mas_top).offset(-4);
        make.height.mas_equalTo(14);
        make.width.mas_equalTo(textWidth);
    }];
    
    //4 插画  怀孕351*245，经期、备孕、育儿统一351*280
    CGFloat centerImageViewHeight = ((SCREEN_WIDTH - 24)/351.0) * 280;
    [self.centerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.nextBtn.mas_top).offset(-38);
        make.left.mas_equalTo(12);
        make.right.mas_equalTo(-12);
        make.height.mas_equalTo(centerImageViewHeight);
    }];
    
    [self addSubview:self.pagView];
    [self.pagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.centerImageView);
    }];
    
    //关闭按钮
    IMYButton *closeButton = [IMYButton new];
    closeButton.imageAtDirection = IMYDirectionUp;
    [closeButton addTarget:self action:@selector(close) forControlEvents:UIControlEventTouchUpInside];
    [closeButton imy_addThemeChangedBlock:^(IMYButton *weakObject) {
        BOOL isNight = [IMYPublicAppHelper shareAppHelper].isNight;
        NSString *imageName;
        if (isNight) {
            imageName = @"icon_pop_close_yejian";
        } else {
            imageName = @"icon_pop_close";
        }
        IMYIcon *icon = [IMYIcon iconWithImage:[UIImage imageNamed:imageName]];
        icon.size = CGSizeMake(20, 20);
        weakObject.rightIcon = icon;
    }];
    
    [self addSubview:closeButton];
    @weakify(self);
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.width.height.equalTo(@20);
        make.right.equalTo(self).offset(-16);
        make.top.mas_offset(16);
    }];
    
}

- (void)checkModeChange{
    self.lasUserMode = IMYPublicAppHelper.shareAppHelper.userMode;
    @weakify(self);
    [[[IMYPublicAppHelper shareAppHelper].userModeChangedSignal deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        if (self.lasUserMode != IMYPublicAppHelper.shareAppHelper.userMode) {
            !self.closeForChangeModeBlock ? : self.closeForChangeModeBlock();
        }
    }];
}

- (void)setType:(IMYVKUserMode)type{
    _type = type;
    [self setupView];
}

- (void)setupView{
    //设置内容
    self.guideBtn.hidden = YES;
    self.tipTagView.hidden = YES;
    NSMutableArray *tipsArray = [NSMutableArray new];
    
    NSString *targetModeString = [IMYPublicAppHelper userModeNameForMode:self.type];
    self.titleLabel.text = [NSString stringWithFormat:@"%@模式",targetModeString];
    [self.nextBtn setTitle:[NSString stringWithFormat:@"开启%@模式",targetModeString] forState:UIControlStateNormal];
    if (self.type == IMYVKUserModeLama) {  //育儿
        self.guideBtn.hidden = NO;
        self.tipTagView.hidden =![IMYModeWelcomeTipTagView switchTipTag];
        self.tipTagView.hidden = YES; //896 先不上线
    }
    @weakify(self);
    [self.topImageView imy_addThemeChangedBlock:^(UIImageView *weakObject) {
        @strongify(self);
        NSString *imageUrl = [self topImageConfigUrl];
        if (imy_isNotBlankString(imageUrl)) {
            [weakObject imy_setImageURL:imageUrl];
        } else {
            [weakObject imy_setImage:[self topImageName]];
        }
    }];
    NSString *centerImageUrl = [self centerImageConfigUrl];
    if (imy_isNotBlankString(centerImageUrl) && [centerImageUrl containsString:@".pag"]) {
        CGFloat centerImageViewHeight = ((SCREEN_WIDTH - 24)/351.0) * 280;
        if (self.type == IMYVKUserModePregnancy) {
            centerImageViewHeight = ((SCREEN_WIDTH - 24)/351.0) * 245;
        }
        self.pagView.hidden = NO;
        self.centerImageView.hidden = YES;
        @weakify(self);
        [self.pagView imy_addThemeChangedBlock:^(IMYPAGView *weakObject) {
            @strongify(self);
            NSString *imageUrl = [self centerImageConfigUrl];
            UIImage *placeholder = [UIImage imy_imageForKey:[self centerImageName]];
            [weakObject loadWithURL:[NSURL URLWithString:imageUrl] placeholder:placeholder completed:^(BOOL loaded) {
            }];
        }];
        self.pagView.imy_size = CGSizeMake((SCREEN_WIDTH - 24), centerImageViewHeight);
    } else {
        self.pagView.hidden = YES;
        self.centerImageView.hidden = NO;
        @weakify(self);
        [self.centerImageView imy_addThemeChangedBlock:^(UIImageView *weakObject) {
            @strongify(self);
            NSString *imageUrl = [self centerImageConfigUrl];
            if (imy_isNotBlankString(imageUrl)) {
                [weakObject imy_setImageURL:imageUrl];
            } else {
                [weakObject imy_setImage:[self centerImageName]];
            }
        }];
    }
 
#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
    CGFloat topBgImageViewHeight = self.type == IMYVKUserModePregnancy ? 390 : 340;
    [self.myMaskView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(topBgImageViewHeight);
    }];
#endif
    
    //4 插画  怀孕351*245，经期、备孕、育儿统一351*280
    CGFloat topImageViewHeight = ((SCREEN_WIDTH - 2*24)/327.0) * 219;
    if (self.type == IMYVKUserModePregnancy) {
        topImageViewHeight = ((SCREEN_WIDTH - 2*24)/327.0) * 269;
    }
    [self.topImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(topImageViewHeight);
    }];
    //4 插画  怀孕351*245，经期、备孕、育儿统一351*280
    CGFloat centerImageViewHeight = ((SCREEN_WIDTH - 24)/351.0) * 280;
    if (self.type == IMYVKUserModePregnancy) {
        centerImageViewHeight = ((SCREEN_WIDTH - 24)/351.0) * 245;
    }
    [self.centerImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(centerImageViewHeight);
    }];
    
}

- (NSString *)topImageConfigUrl{
    NSString *pre_name = @"jq_";
    if (self.type == IMYVKUserModeLama) {  //育儿
        pre_name = @"ye_";
    }else if (self.type == IMYVKUserModePregnancy){  //怀孕
        pre_name = @"hy_";
    }else if (self.type == IMYVKUserModeForPregnant){ //备孕
        pre_name = @"by_";
    }
    NSString *guide_img = [[IMYConfigsCenter sharedInstance] stringForKeyPath:[NSString stringWithFormat:@"womens_health2.mode_change_welcome_page.%@text_img",pre_name]];
    if (IMYPublicAppHelper.shareAppHelper.isNight) {
        guide_img = [[IMYConfigsCenter sharedInstance] stringForKeyPath:[NSString stringWithFormat:@"womens_health2.mode_change_welcome_page.%@text_img_dark",pre_name]];
    }
    return guide_img;
}

- (NSString *)topImageName{
    if (self.type == IMYVKUserModeLama) {  //育儿
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_ye_text_dark" : @"imyme_icon_ye_text";
    }else if (self.type == IMYVKUserModePregnancy){  //怀孕
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_hy_text_dark" : @"imyme_icon_hy_text";
    }else if (self.type == IMYVKUserModeForPregnant){ //备孕
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_by_text_dark" : @"imyme_icon_by_text";
    }else{//经期
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_jq_text_dark" : @"imyme_icon_jq_text";
    }
}


- (NSString *)centerImageConfigUrl{
    NSString *pre_name = @"jq_";
    if (self.type == IMYVKUserModeLama) {  //育儿
        pre_name = @"ye_";
    }else if (self.type == IMYVKUserModePregnancy){  //怀孕
        pre_name = @"hy_";
    }else if (self.type == IMYVKUserModeForPregnant){ //备孕
        pre_name = @"by_";
    }
    NSString *guide_img = [[IMYConfigsCenter sharedInstance] stringForKeyPath:[NSString stringWithFormat:@"womens_health2.mode_change_welcome_page.%@live_img",pre_name]];
    if (IMYPublicAppHelper.shareAppHelper.isNight) {
        guide_img = [[IMYConfigsCenter sharedInstance] stringForKeyPath:[NSString stringWithFormat:@"womens_health2.mode_change_welcome_page.%@live_img_dark",pre_name]];
    }
    return guide_img;
}

- (NSString *)centerImageName{
    if (self.type == IMYVKUserModeLama) {  //育儿
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_ye_img_dark" : @"imyme_icon_ye_img";
    }else if (self.type == IMYVKUserModePregnancy){  //怀孕
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_hy_img_dark" : @"imyme_icon_hy_img";
    }else if (self.type == IMYVKUserModeForPregnant){ //备孕
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_by_img_dark" : @"imyme_icon_by_img";
    }else{//经期
        return IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_jq_img_dark" : @"imyme_icon_jq_img";
    }
}

- (void)nextBtnClickAction{
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        NSArray *toModeNameArray = @[IMYString(@"经期"), IMYString(@"怀孕"), IMYString(@"备孕"), IMYString(@"育儿")];
        NSString *toModeName = [toModeNameArray imy_objectAtIndex:self.type];
        NSString *summary = [NSString stringWithFormat:IMYString(@"你正在青少年模式中，暂时无法开启%@模式"), toModeName];
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消") otherTitles:@[IMYString(@"关闭青少年模式")] summary:summary showInView:nil action:^(NSInteger index) {
            NSInteger entrance = 0;
            if ([self.fromName isEqualToString:@"recordSelect"]) {
                entrance = 27;
            } else if ([self.fromName isEqualToString:@"mytabSelect"]) {
                entrance = 28;
            }
            
            if (index == 1) {
                [self closeYouthModeActionSheetClickAnalytics:entrance
                                                   publicType:@"点击青少年模式按钮" ];
                if ([IMYNetState networkEnable]) {
                    !self.willNextActionBlock?:self.willNextActionBlock(self.nextBtn);
                }
                
                //关闭青少年模式
                /// 27：首页_记录页面切换身份
                /// 28：我的tab_切换身份
                void (^finishBlock)(NSDictionary *info) = ^void(NSDictionary *info) {
                    imy_asyncMainBlock(^{
                        NSInteger result_key = [info[@"result_key"] integerValue];
                        if (result_key == 1) {
                            !self.nextActionBlock?:self.nextActionBlock(self.nextBtn);
                        } else if (result_key == 3) {
                            // 如果是取消操作要销毁我 tab 页面的 modeInfoView，否则会无法切换身份
                            [[NSNotificationCenter defaultCenter] postNotificationName:@"K_freeModeInfoView" object:nil];
                        }
                    });
                };
                
                NSMutableDictionary *params = [NSMutableDictionary dictionary];
                [params imy_setNonNilObject:finishBlock forKey:@"finishBlock"];
                [params imy_setNonNilObject:@(entrance) forKey:@"entrance"];
                //模拟调用 result_key: 1 成功, 2 失败, 3 取消
                //[params imy_setNonNilObject:@(1) forKey:@"result_key"];
                [[IMYURIManager sharedInstance] runActionWithPath:@"youthmode/deactivate" params:params info:nil];
            } else {
                //取消
                [self closeYouthModeActionSheetClickAnalytics:entrance
                                                   publicType:@"取消"];
            }
        }];
        
        NSInteger entrance = 0;
        if ([self.fromName isEqualToString:@"recordSelect"]) {
            entrance = 27;
        } else if ([self.fromName isEqualToString:@"mytabSelect"]) {
            entrance = 28;
        }
        [self closeYouthModeActionSheetExposuredAnalytics:entrance];
        
    } else {
        !self.willNextActionBlock?:self.willNextActionBlock(self.nextBtn);
        !self.nextActionBlock?:self.nextActionBlock(self.nextBtn);
    }
}


/// 场景1:当用户无已出生宝宝
/// 场景2:当用户是怀孕模式且有已出生宝宝
- (void)updateLamaUI {
    
    @weakify(self);
    imy_asyncBlock(^{
        @strongify(self);
        
        BOOL hasBaby = NO;
#if __has_include(<IMYRecord/IMYRecordBabyManager.h>)
        hasBaby = [[IMYRecordBabyManager sharedInstance] babyList].count > 0;
#endif
        imy_asyncMainBlock(^{
            @strongify(self);
            IMYVKUserMode userMode = [IMYPublicAppHelper shareAppHelper].userMode;
            if (!hasBaby || (userMode == IMYVKUserModePregnancy && hasBaby)) {
                if ([self.fromName isEqualToString:@"recordSelect"]) {
                    [self.nextBtn setTitle:IMYString(@"下一步") forState:UIControlStateNormal];
                }
            }
        });
    });
}

#pragma mark - UI
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 30 , 28)];
        _titleLabel.textColor = [UIColor imy_colorForKey:kCK_Black_A];
        [_titleLabel imy_addThemeChangedBlock:^(UILabel *weakObject) {
            weakObject.textColor = [UIColor imy_colorForKey:IMYPublicAppHelper.shareAppHelper.isNight ?@"#EBEBEB":kCK_Black_A];
        }];
        _titleLabel.font = [UIFont boldSystemFontOfSize:24];
    }
    return _titleLabel;
}

#if __has_include(<BBJViewKit/BBJGradientMaskView.h>)
- (BBJGradientMaskView *)myMaskView{
    if (!_myMaskView) {
        BBJGradientMaskView *view = [[BBJGradientMaskView alloc] initWithFrame:CGRectZero];
        id color1 = (__bridge id)[UIColor imy_colorWithHexString:@"#FFEDF3"].CGColor;
        id color2 = (__bridge id)[UIColor imy_colorWithHexString:@"#FFFAFC"].CGColor;
        id color3 = (__bridge id)[UIColor imy_colorWithHexString:@"#FFFFFF"].CGColor;
        view.gradientLayer.colors = @[color1,color2,color3];
        view.gradientLayer.locations = @[@0.0f, @0.9f, @1.0f];
        // 设置渐变方向（从上到下）
        view.gradientLayer.startPoint = CGPointMake(0.5, 0);  // 水平居中，顶部
        view.gradientLayer.endPoint = CGPointMake(0.5, 1);    // 水平居中，底部
        [view imy_addThemeChangedBlock:^(BBJGradientMaskView *weakObject) {
            weakObject.hidden = IMYPublicAppHelper.shareAppHelper.isNight;
        }];
        _myMaskView = view;
    }
    return _myMaskView;
}
#endif

- (UIImageView *)topImageView{
    if (!_topImageView) {
        UIImageView *imgView = [UIImageView new];
        _topImageView = imgView;
    }
    return _topImageView;
}


- (UIImageView *)centerImageView{
    if (!_centerImageView) {
        UIImageView *imgView = [UIImageView new];
        _centerImageView = imgView;
    }
    return _centerImageView;
}

- (IMYPAGView *)pagView{
    if (!_pagView) {
        CGFloat width = (SCREEN_WIDTH - 24);
        CGFloat height = ((SCREEN_WIDTH - 24)/351.0) * 280;
        _pagView = [[IMYPAGView alloc] initWithFrame:CGRectMake(0, 0, width, height)];
        _pagView.hidden = YES;
    }
    return _pagView;
}

- (IMYCKLoadingTextButton *)nextBtn{
    if (!_nextBtn) {
        IMYCKLoadingTextButton *btn = [[IMYCKLoadingTextButton alloc] init];
        btn.titleLabel.font = [UIFont boldSystemFontOfSize:17];
        btn.layer.cornerRadius = 24;
        btn.layer.masksToBounds = YES;
        [btn setTitle:IMYString(@"下一步") buttonType:IMYCKLoadingButtonRedType];
        btn.titleColor = nil;
        btn.contentColor = nil;
        btn.borderColor = nil;
        
        [btn imy_setTitleColor:kCK_White_A];
        [btn imy_setBackgroundColor:kCK_Red_B];
        btn.indicatorView.activityIndicatorViewStyle = UIActivityIndicatorViewStyleWhite;
        
        [btn addTarget:self action:@selector(nextBtnClickAction) forControlEvents:UIControlEventTouchUpInside];
        _nextBtn = btn;
    }
    return _nextBtn;
}

- (IMYButton *)guideBtn{
    if (!_guideBtn) {
        IMYButton *btn = [IMYButton new];
        [btn imy_setTitle:@""];
        [btn imy_setBackgroundColorForKey:kIMYClearColorKey];
        btn.imageAtDirection = IMYDirectionRight;
        [btn imy_setTitle:@"未出生？育儿模式逛一逛"];
        [btn imy_setImage:@"imyme_icon_arrow"];
        [btn imy_addThemeChangedBlock:^(IMYButton *weakObject) {
            [weakObject imy_setImage:IMYPublicAppHelper.shareAppHelper.isNight ? @"imyme_icon_arrow_dark" : @"imyme_icon_arrow"];
        }];
        btn.titleLabel.font = [UIFont imy_regularWith:13];
        [btn imy_setTitleColor:kCK_Black_M];
        btn.hidden = YES;
        @weakify(self);
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            NSString *uriString = @"meiyou:///web/pure/common?params=eyJ1cmwiOiJodHRwczovL3Rvb2xzLW5vZGUuc2VleW91eWltYS5jb20veXVlci1ndWlkZS9pbmRleC5odG1sIn0=";
            [[IMYURIManager shareURIManager] runActionWithString:uriString];
            if ([self.fromName isEqualToString:@"mytabSelect"]) {
                !self.closeForChangeModeBlock ? : self.closeForChangeModeBlock();
            }else{
                [self checkModeChange];
            }
        }];
        _guideBtn = btn;
    }
    return _guideBtn;
}

- (IMYModeWelcomeTipTagView *)tipTagView{
    if (!_tipTagView) {
        IMYModeWelcomeTipTagView *view = [IMYModeWelcomeTipTagView new];
        view.hidden = YES;
        view.layer.borderColor = [UIColor imy_colorForKey:@"#FFFFFF"].CGColor;
        view.layer.borderWidth = 0.5;
        CACornerMask masks = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner;
        [view imy_drawAnyCornerRadius:7 masks:masks];
        _tipTagView = view;
    }
    return _tipTagView;
}


@end


@implementation IMYModeWelcomeContentView_V2 (BiAnalytics)

- (void)closeYouthModeActionSheetExposuredAnalytics:(NSInteger)entrance {
    /*
     27：首页_记录页面切换身份
     28：我的tab_切换身份
     */
    NSDictionary *params = @{
        @"event" : @"wd_qsnms_kg",
        @"action": @1,
        @"entrance": @(entrance),
        @"public_info": @"关闭青少年模式",
        @"position": @(158)
    };
    [IMYGAEventHelper postWithPath:@"event"
                            params:params
                           headers:nil
                         completed:nil];
}



- (void)closeYouthModeActionSheetClickAnalytics:(NSInteger)entrance
                                     publicType:(NSString *)publicType {
    
    /*
     27：首页_记录页面切换身份
     28：我的tab_切换身份
     */
    
    NSDictionary *params = @{
        @"event" : @"wd_qsnms_kg",
        @"action": @2,
        @"entrance": @(entrance),
        @"public_type": publicType,
        @"public_info": @"关闭青少年模式",
        @"position": @(158)
    };
    [IMYGAEventHelper postWithPath:@"event"
                            params:params
                           headers:nil
                         completed:nil];
}

@end
