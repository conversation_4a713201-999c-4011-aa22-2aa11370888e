//
//  SYUserModeViewModel.m
//  Seeyou
//
//  Created by 林云峰 on 16/8/9.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "SYUserModeViewModel.h"
#import <IMYBaseKit/IMY_ViewKit.h>
#import "SYUserHelper.h"
#import "SYPublicFun.h"
#import "IMYMeGlobalMacros.h"

#if __has_include(<IMYCommonKit/IMYCKABTestManager.h>)
#import <IMYCommonKit/IMYCKABTestManager.h>
#endif

#if __has_include("SYBaseTabBarController.h")
#import "SYBaseTabBarController.h"
#endif


#if __has_include(<IMYRecord/IMYRecord.h>)
#import <IMYRecord/IMYRecord.h>
#endif

#if __has_include(<IMYMSG/IMYMSG.h>)
#import <IMYMSG/IMYMsgNotifyManager.h>
#endif

#if __has_include(<BBJBabyHome/BBJBabyHome.h>)
#import "BBJUploader.h"
//#import <BBJBabyHome/BBJFloatingWindowManager.h>
#endif

#if __has_include(<IMYRecord/IMYRecordPregnancyBabyManager.h>)
#import "IMYCKLoadingTextButton.h"
#import "IMYRecordPregnancyBabyManager.h"
#endif


@interface SYUserModeViewModel ()
@property (nonatomic, strong) IMYPickerView *pickerView;
@property (nonatomic, assign) IMYVKUserMode lastMode;
@end

@implementation SYUserModeViewModel

- (BOOL)checkPregnancyValid:(NSDate *)pregnancyDate completeBlock:(CheckValidResultBlock)block {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *lastMense = [IMYMensesDao getLastMenses].mstartdate; //最后一次月经
#else
    NSDate *lastMense = nil;
#endif
    if (lastMense) {
        //        NSInteger dayDif = [pregnancyDate daysAfterDate:lastMense];
        //        if (dayDif <= 259) {
        //            [UIWindow imy_showTextHUD:IMYString(@"最近一次经期距宝宝出生不足37周，请核对数据")];
        //            block(NO, [lastMense dateByAddingDays:260]);
        //            return NO;
        //        }
        if ([pregnancyDate daysAfterDate:[NSDate date]] > 280) {
            [UIWindow imy_showTextHUD:IMYString(@"您输入的预产期距离今天超过280天，不能填入未来的怀孕记录")];
            if (block) {
                block(NO, [lastMense dateByAddingDays:280]);
            }
            return NO;
        }
    }
    //    NSDate *lastPregnan = ((IMYPregnanceModel *)[IMYPregnanceModel getLastPregnancyModel]).birthDate; //最后一次预产期
    //    if ([lastPregnan laterDate:[pregnancyDate dateByAddingDays:-280]]) {
    //        [UIWindow imy_showTextHUD:IMYString(@"您输入的预产期，怀孕记录在上一次孕期中，不能重复怀孕记录！")];
    //        return NO;
    //    }

    return YES;
}
- (BOOL)isCrossAnotherPregnacy:(NSDate *)pregnancyDate {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *lastPregnan = ((IMYPregnanceModel *)[IMYPregnanceModel getLastCompletePregnancy]).endDate; //最后一次预产期
#else
    NSDate *lastPregnan = nil;
#endif
    if ([lastPregnan isLaterOrEqualToDate:[pregnancyDate dateByAddingDays:-280]]) {
        return YES;
    }
    return NO;
}

- (NSArray *)conflictPregnacyDatesWithNewDueDate:(NSDate *)dueDate {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *pregnancyDate = [dueDate dateByAddingDays:-280];
    NSArray *array = [IMYPregnanceModel getAllPregnances];
    return [array filter:^BOOL(IMYPregnanceModel *element) { //历史孕期结束日要比今天小
        return (element.endDate != nil  && [[NSDate imy_today] daysAfterDate:element.endDate] >= 0 && [pregnancyDate daysAfterDate:element.endDate] <= 14);
    }];
#else
    return nil;
#endif
}

- (void)savePregancyWithDate:(NSDate *)date {
#if __has_include(<IMYRecord/IMYRecord.h>)
//    [SYUserHelper sharedHelper].babyBirthday = nil;
    if ([IMYDayRecordModel dateInsidePregnance:[NSDate imy_today]]) {
        //当前出于孕期的走重置逻辑
        [IMYDayRecordModel resetPregnancyDueDate:date];
    } else {
        //因为删除了当前了孕期的，需要重新生成孕期
        [IMYDayRecordModel setPregnancyDueDate:date];
        [IMYPublicAppHelper shareAppHelper].userMode = IMYVKUserModePregnancy;
    }
    [[SYUserHelper sharedHelper] saveToDB];
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
#endif
}

- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate sheetCompleteBlock:(CheckValidResultBlock)block {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *lastMenses = [IMYMensesDao getLastMenses].mstartdate; //最后一次月经1
    NSInteger pregnancyDays = [lastMenses imy_calendarGetDayDiff:dueDate];
    if (pregnancyDays < 280 && lastMenses) {
        imy_asyncMainBlock(0.3, ^{
            [self savePregancyWithDate:dueDate];
            if (block) {
                block(YES, dueDate);
            }
        });
        return NO;
    }
#endif
    return YES;
}

- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *lastMenses = [IMYMensesDao getLastMenses].mstartdate; //最后一次月经1
    NSInteger pregnancyDays = [lastMenses imy_calendarGetDayDiff:dueDate];
    if (pregnancyDays < 280 && lastMenses) {
        return NO;
    }
#endif
    return YES;
}

- (UIView *)getShowInView {
    //兼容经期8.0.8新的用户模式切换
    UIView *showInView = nil;
#if __has_include("SYBaseTabBarController.h")
    showInView = [SYBaseTabBarController shareTabbarController].view;
#endif
    NSArray *vcArray = [[UIViewController imy_currentTopViewController].navigationController childViewControllers];
    UIViewController * vc = [vcArray firstObject];
    if ([vc isKindOfClass: NSClassFromString(@"IMYModeChangeVC_V2")]) {
        showInView = [UIViewController imy_currentTopViewController].view;
    }
    if ([vc isKindOfClass: NSClassFromString(@"IMYModeWelcomeViewController")]) {
        showInView = [UIViewController imy_currentTopViewController].view;
    }
    return showInView;
}
#pragma mark - 辣妈设置
- (BOOL)checkBabyBirthdayValidate:(NSDate *)babyBirthday {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYPregnanceModel *pregnacy = [IMYPregnanceModel getLastPregnancyModel];
    if (pregnacy && [babyBirthday compare:pregnacy.startDate] != NSOrderedDescending) {
        [UIWindow imy_showTextHUD:IMYString(@"宝宝出生日小于最近孕期开始日，请重新确认")];
        return NO;
    }
#endif
    return YES;
}

- (void)saveLamaWithBabyBirthday:(NSDate *)babyBirthday sex:(NSInteger)sex isNew:(BOOL)isNew {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYRecordBabyModel *baby;
    if (!isNew) {
//        baby = [IMYRecordBabyModel searchSingleWithWhere:[NSString stringWithFormat:@"birthday = '%@' and is_deleted = 0", [babyBirthday imy_getOnlyDateString]] orderBy:nil];
        baby = [IMYRecordBabyManager.sharedInstance myBabySearchWithBirthday:[babyBirthday imy_getOnlyDateString]];
    }
    if (!baby) {
        // 新增宝宝
        baby = [[IMYRecordBabyModel alloc] init];
    }
    baby.birthday = [babyBirthday imy_getOnlyDateString];
    baby.gender = sex;
    [baby saveToDB];
    
    // 埋点相关
    IMYRecordBabyModel *lastSelectedBaby = [IMYRecordBabyManager sharedInstance].currentBaby;
    IMYVKUserMode exMode = (lastSelectedBaby && self.lastMode == IMYVKUserModePregnancy) ? IMYVKUserModeLama : self.lastMode;
    [self postBabyChangeBabyId:baby.baby_id mode:3 exBabyId:lastSelectedBaby.baby_id exMode:exMode];
    
    BOOL homeIsBBJEntrance = [[[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"yqhomeAB/lama/homeIsBBJEntrance" params:nil] boolValue];
    if (homeIsBBJEntrance) {
        NSString *key = [NSString stringWithFormat:@"userChangeFromLamaModeBabyidtemp-%@",[IMYPublicAppHelper shareAppHelper].userid];
        NSInteger babyId = [[IMYUserDefaults standardUserDefaults] integerForKey:key];
        if (babyId > 0) {
//            NSDictionary *params = @{@"baby_id":@(babyId),@"is_deleted":@(0)};
//            IMYRecordBabyModel *lastDBSelectBaby = [[IMYRecordBabyManager sharedInstance] searchBabyWithParams:params];
            IMYRecordBabyModel *lastDBSelectBaby = [IMYRecordBabyManager.sharedInstance getBabyWithId:babyId];
            if (lastDBSelectBaby && lastSelectedBaby.is_deleted == 0) {
                lastSelectedBaby = lastDBSelectBaby;
            }
        }
        if (self.lastMode == IMYVKUserModePregnancy) {
            [[IMYRecordBabyManager sharedInstance] selectBaby:baby.baby_id];
        } else {
            [[IMYRecordBabyManager sharedInstance] selectBaby:lastSelectedBaby ? lastSelectedBaby.baby_id : baby.baby_id];
        }
    } else {
        [[IMYRecordBabyManager sharedInstance] selectBaby:baby.baby_id];
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (lastSelectedBaby.gender != baby.gender) {
        params[@"changedBabySex"] = @(YES);
    }
    if (![lastSelectedBaby.birthday isEqualToString:baby.birthday]) {
        params[@"changeBabyBirthday"] = @(YES);
    }
    if (params.count) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kSYUserBabyInfoChangedNotification" object:params];
    }
    //修改了宝宝数据，及时同步数据
    [SYPublicFun uploadUserInfoData];
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
    //删除冲突的手动排卵日
    NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:babyBirthday];
    if (manualOvlution) {
        [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
    }
    //bbj 有监听 kSYUserBabyInfoChangedNotification
    //bugfix 辣妈身份创建宝宝的时候很大概率会创建2个一样的宝宝
    //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001217186
//    [[IMYURIManager shareURIManager] runActionWithString:@"user/baby/update/babyList"];
#endif
}

- (void)sync_saveLamaWithBabyBirthday:(NSDate *)babyBirthday
                                  sex:(NSInteger)sex
                                isNew:(BOOL)isNew
                    andDeleteBabyList:(NSArray *)deleteBabyList
             isFromForcedHandoverLama:(BOOL)isFromForcedHandoverLama
                           completion:(SYUserModeSaveLamaBlk)completionBlk{
    [self sync_saveLamaWithBabyBirthday:babyBirthday sex:sex nickName:nil isNew:isNew andDeleteBabyList:deleteBabyList isFromForcedHandoverLama:isFromForcedHandoverLama completion:completionBlk];
}

- (void)sync_saveLamaWithBabyBirthday:(NSDate *)babyBirthday
                                  sex:(NSInteger)sex
                             nickName:(NSString *)nickName
                                isNew:(BOOL)isNew
                    andDeleteBabyList:(NSArray *)deleteBabyList
             isFromForcedHandoverLama:(BOOL)isFromForcedHandoverLama
                           completion:(SYUserModeSaveLamaBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSString *scene_type = nil;
    IMYRecordBabyModel *baby;
    if (!isNew && babyBirthday) {
//        baby = [IMYRecordBabyModel searchSingleWithWhere:[NSString stringWithFormat:@"birthday = '%@' and is_deleted = 0", [babyBirthday imy_getOnlyDateString]] orderBy:nil];
        baby = [IMYRecordBabyManager.sharedInstance myBabySearchWithBirthday:[babyBirthday imy_getOnlyDateString]];
    }
    if (!baby && babyBirthday) {
        // 新增宝宝 如快速点击，多次重复请求接口，这里的baby.clentid就不一样了，就会有创建多个宝宝？
        baby = [[IMYRecordBabyModel alloc] init];
        //超过42周 取妊娠 id
        NSInteger gestation_id = 0;
        IMYPregnancyFlowModel *model = [IMYPregnancyFlowModel getBornBabyPregnancingWithStartDate:[[babyBirthday dateBySubtractingDays:280] imy_getDateTimeString]];
        if (model.gestation_id > 0) {
            gestation_id = model.gestation_id;
        } else {
            gestation_id = [IMYRecordPregnancyBabyManager sharedInstance].gestation_id;
        }
        baby.gestation_id = gestation_id;
        scene_type = @"add&scene_type=1";
    }
    NSMutableArray *serverBabyList = [NSMutableArray new];
    if (babyBirthday) {
        baby.birthday = [babyBirthday imy_getOnlyDateString];
        baby.gender = sex;
        if (imy_isNotBlankString(nickName)) {
            baby.nickname = nickName;
        }
        [serverBabyList addObject:baby];
    }
    [serverBabyList addObjectsFromArray:deleteBabyList];
    
    //宝宝 is_checked 要放在接口里面
    IMYRecordBabyModel *lastSelectedBaby = [IMYRecordBabyManager sharedInstance].currentBaby;
    IMYVKUserMode exMode = (lastSelectedBaby && self.lastMode == IMYVKUserModePregnancy) ? IMYVKUserModeLama : self.lastMode;
    
    BOOL homeIsBBJEntrance = [[[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"yqhomeAB/lama/homeIsBBJEntrance" params:nil] boolValue];
    if (homeIsBBJEntrance) {
        NSString *key = [NSString stringWithFormat:@"userChangeFromLamaModeBabyidtemp-%@",[IMYPublicAppHelper shareAppHelper].userid];
        NSInteger babyId = [[IMYUserDefaults standardUserDefaults] integerForKey:key];
        if (babyId > 0) {
//            NSDictionary *params = @{@"baby_id":@(babyId),@"is_deleted":@(0)};
//            IMYRecordBabyModel *lastDBSelectBaby = [[IMYRecordBabyManager sharedInstance] searchBabyWithParams:params];
            IMYRecordBabyModel *lastDBSelectBaby = [IMYRecordBabyManager.sharedInstance getBabyWithId:babyId];
            if (lastDBSelectBaby && lastSelectedBaby.is_deleted == 0) {
                lastSelectedBaby = lastDBSelectBaby;
            }
        }
        if (self.lastMode == IMYVKUserModePregnancy && baby) {
            baby.is_checked = YES;
        } else {
            if (lastSelectedBaby) {
                lastSelectedBaby.is_checked = YES;
                //最后的选中不变
                [serverBabyList addObject:lastSelectedBaby];
            } else {
                baby.is_checked = YES;
            }
        }
    } else {
        baby.is_checked = YES;
    }
    @weakify(self);
    if (serverBabyList.count == 0) {
        [IMYRecordBabyManager syncBabyInfoToServerWithNONewBaby:^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            if (success) {
                [self babyBornChangeAction:baby lastSelectedBaby:lastSelectedBaby exMode:exMode babyBirthday:babyBirthday completion:completionBlk];
            } else {
                [self babyBornChangeFailAction:completionBlk];
            }
        }];
    } else {
        [IMYRecordBabyManager syncBabyInfoToServerWithBabyModel:serverBabyList
                                                       position: (30 + self.position)
                                                          scene:scene_type
                                                  andCompletion:^(BOOL success, NSString *errorMessage) {
            @strongify(self);
            if (success) {
                [self babyBornChangeAction:baby lastSelectedBaby:lastSelectedBaby exMode:exMode babyBirthday:babyBirthday completion:completionBlk];
            } else {
                [self babyBornChangeFailAction:completionBlk];
            }
        }];

    }

#endif
}

- (void)syncBabyInfoWithBirthType:(NSInteger)birthType weight:(NSString *)weight bbj_baby_id:(NSInteger)bbj_baby_id completion:(void (^)(BOOL success,NSString *errorMessage))completion {
    if (bbj_baby_id) {
        NSMutableDictionary *dict = [NSMutableDictionary new];
        if (birthType) {
            [dict imy_setNonNilObject:@(birthType) forKey:@"birth_type"];
        }
        if (imy_isNotBlankString(weight)) {
            NSDecimalNumber *decimalNumber = [NSDecimalNumber decimalNumberWithString:weight];
            [dict imy_setNonNilObject:decimalNumber forKey:@"weight"];//体重
        }
        [dict imy_setNonNilObject:@(bbj_baby_id) forKey:@"baby_id"];
        [[IMYServerRequest putPath:@"baby" host:api_bbj_meiyou_com params:dict headers:nil] subscribeNext:^(id x) {
            if (completion) {
                completion(YES, nil);
            }
        } error:^(NSError *error) {
            if (completion) {
                completion(NO, nil);
            }
        }];
    }
}

#if __has_include(<IMYRecord/IMYRecord.h>)
- (void)babyBornChangeAction:(IMYRecordBabyModel *)baby
            lastSelectedBaby:(IMYRecordBabyModel *)lastSelectedBaby
                      exMode:(NSInteger)exMode
                babyBirthday:(NSDate *)babyBirthday
                  completion:(SYUserModeSaveLamaBlk)completionBlk {
    // 埋点相关
    [self postBabyChangeBabyId:baby.baby_id mode:3 exBabyId:lastSelectedBaby.baby_id exMode:exMode];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (lastSelectedBaby.gender != baby.gender) {
        params[@"changedBabySex"] = @(YES);
    }
    if (![lastSelectedBaby.birthday isEqualToString:baby.birthday]) {
        params[@"changeBabyBirthday"] = @(YES);
    }
    if (params.count) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kSYUserBabyInfoChangedNotification" object:params];
    }
    //修改了宝宝数据，及时同步数据
    [SYPublicFun uploadUserInfoData];
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
    //删除冲突的手动排卵日
    NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:babyBirthday];
    if (manualOvlution) {
        [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
    }
    !completionBlk?:completionBlk(YES);
    [[IMYRecordBabyManager sharedInstance] selectBaby:baby.baby_id];
}
#endif

- (void)babyBornChangeFailAction:(SYUserModeSaveLamaBlk)completionBlk {
    !completionBlk?:completionBlk(NO);
    if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
    } else {
        [UIWindow imy_showTextHUD:IMYString(@"加载失败，请重试")];
    }
}
#pragma mark - change Mode  --欢迎页面点击 下一步 弹出的alter 逻辑

- (void)welcomeStyleCheckAlterConfirmChangeToMode:(IMYVKUserMode)mode changeToModeBlock:(void(^)(IMYVKUserMode toMode))changeToModeBlock deletePregnancyAndChangeMode:(void(^)(IMYVKUserMode toMode))deletePregnancyAndChangeMode {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYVKUserMode lastMode = self.useLastModeForBi ? self.lastModeForBi : [IMYPublicAppHelper shareAppHelper].userMode;
    if (mode == IMYVKUserModePregnancy) {
        if ([IMYDayRecordModel getLastPregnancyEnd].isToday) {
            [self lastPregnancyEndIsToday];
            return;
        }
    } else {
        if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
            if (mode == IMYVKUserModeLama) {
//                NSDate *manualOvulationDate = [IMYMensesDao findManualOvulatinDateFromSomeday:]
            } else {
                NSDate *pEndDate = [IMYDayRecordModel getLastPregnancyEnd];
                NSDate *pDate = [IMYDayRecordModel getCurrentPregnancyStartDate];
                NSInteger pDay2Today = [pDate distanceInDaysToDate:NSDate.date];
                NSString *modeString = [self stringForMode:mode];
                NSDateFormatter *df = [[NSDateFormatter alloc] init];
                df.dateFormat = @"yyyy.MM.dd";
                NSString *pEndDateString = [df stringFromDate:pEndDate];
                NSString *title = [NSString stringWithFormat:IMYString(@"确定开始%@模式吗？"),modeString];
                NSString *message = [NSString stringWithFormat:IMYString(@"开始%@模式，将删除小于14天的孕期记录（预产期%@）"),modeString,pEndDateString];
                NSString *message2 = [NSString stringWithFormat:IMYString(@"请管理当前小于14天的孕期记录（预产期%@），\n操作后将开始%@模式"),pEndDateString,modeString];
                NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前孕期记录（预产期%@），\n操作后将开始%@模式"),pEndDateString,modeString];
                if (pDay2Today <= 14) {
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[@"删除此次孕期"]
                                                 summary:message2
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                        if (index) {
                            //怀孕切换其他身份_孕天过小
                            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_ytgx", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                            imy_asyncMainBlock(0.1, ^{
                                if (deletePregnancyAndChangeMode) {
                                    deletePregnancyAndChangeMode(mode);
                                }
                                
                                [self umEventTrackWithLastMode:lastMode mode:mode];
                            });
                        }
                    }];
                } else if (pDay2Today >= 140) {
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"),IMYString(@"宝宝出生了"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode toBlock:YES changeToModeBlock:changeToModeBlock];
                                                      } else if (index == 2) {
                                                          //添加宝宝
                                                          [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"user/baby/add" params:@{@"minBirthday":[pDate dateByAddingDays:140],@"manualChangeMode":@(YES)} callbackBlock:^(id result, NSError *error, NSString *eventName) {
                                                              //怀孕切换其他身份_宝宝出生了
                                                              [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_bbcsl", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                              [IMYDayRecordModel birthCurrentPregnancy:result];
                                                              NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:result];
                                                              if (manualOvlution) {
                                                                  [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
                                                              }
                                                              if (changeToModeBlock) {
                                                                  changeToModeBlock(mode);
                                                              }
                                                          }];
                                                      } else if (index == 3) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          if (deletePregnancyAndChangeMode) {
                                                              deletePregnancyAndChangeMode(mode);
                                                          }
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                } else {
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode toBlock:YES changeToModeBlock:changeToModeBlock];
                                                      } else if (index == 2) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          if (deletePregnancyAndChangeMode) {
                                                              deletePregnancyAndChangeMode(mode);
                                                          }
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                }
                return;
            }
        }
    }
    
    // 当前模式为辣妈模式，切换到其他模式
    // 校验宝宝记小工具目前是否有正在上传的图片
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeLama) {
        NSUInteger picCount = 0;
#if __has_include(<BBJBabyHome/BBJBabyHome.h>)
        picCount = [BBJUploader sharedInstance].publishModels.count;
#endif
        if (picCount) {
            NSString *modeString = IMYString(@"经期模式");
            if (mode == IMYVKUserModePregnancy) {
                modeString = IMYString(@"怀孕模式");
            } else if (mode == IMYVKUserModeForPregnant) {
                modeString = IMYString(@"备孕模式");
            }
            NSString *message = [NSString stringWithFormat:IMYString(@"宝宝记%lu条记录正在保存中，确定放弃保存并开始记%@吗？"), (unsigned long)picCount,modeString];
            [UIAlertController imy_showAlertViewWithTitle:nil
                                                  message:IMYString(message)
                                        cancelButtonTitle:IMYString(@"取消")
                                        otherButtonTitles:@[IMYString(@"确定")]
                                                  handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                if (buttonIndex == 1) {
                    IMY_POST_NOTIFY(@"BBJNotification_logout");
                    if (changeToModeBlock) {
                        changeToModeBlock(mode);
                    }
                }
            }];
            return;
        }
    }
    
    

    if (changeToModeBlock) {
        changeToModeBlock(mode);
    }

#endif
}



#pragma mark - change Mode
- (void)confirmChangeToMode:(IMYVKUserMode)mode consultable:(BOOL)consultable {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYVKUserMode lastMode = self.useLastModeForBi ? self.lastModeForBi : [IMYPublicAppHelper shareAppHelper].userMode;
    if (mode == IMYVKUserModePregnancy) {
        if ([IMYDayRecordModel getLastPregnancyEnd].isToday) {
            [self lastPregnancyEndIsToday];
            return;
        }
    } else {
        if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
            if (mode == IMYVKUserModeLama) {
//                NSDate *manualOvulationDate = [IMYMensesDao findManualOvulatinDateFromSomeday:]
            } else {
                NSDate *pEndDate = [IMYDayRecordModel getLastPregnancyEnd];
                NSDate *pDate = [IMYDayRecordModel getCurrentPregnancyStartDate];
                NSInteger pDay2Today = [pDate distanceInDaysToDate:NSDate.date];
                NSString *modeString = [self stringForMode:mode];
                NSDateFormatter *df = [[NSDateFormatter alloc] init];
                df.dateFormat = @"yyyy.MM.dd";
                NSString *pEndDateString = [df stringFromDate:pEndDate];
                NSString *title = [NSString stringWithFormat:IMYString(@"确定开始%@模式吗？"),modeString];
                NSString *message = [NSString stringWithFormat:IMYString(@"开始%@模式，将删除小于14天的孕期记录（预产期%@）"),modeString,pEndDateString];
                NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前孕期记录（预产期%@），\n操作后将开始%@模式"),pEndDateString,modeString];
                void (^deletePregnancyAndChangeMode)(void) = ^(){
                    [IMYRecordPregnancyBabyManager deleteGestation:NO loadingBtn:self.loadingBtn completeBlock:^(NSError * _Nullable error) {
                        if (!error) {
                            //清除次记录
                            [IMYDayRecordModel deleteCurrentPregnancy];
                            [self changeToMode:mode];
                            
                            //删除孕期要直接上报
                            [IMYGAEventHelper postWithPath:@"bi_mode"
                                                    params:@{ @"mode": @(mode),
                                                              @"exmode": @(lastMode),
                                                              @"position": @(self.position)
                                                    }
                                                   headers:nil
                                                 completed:nil];
                        }
                    }];

                    
                };
                
                if (pDay2Today <= 14) {
                    [IMYActionMessageBox showBoxWithTitle:title
                                                  message:message
                                                    style:IMYMessageBoxStyleFlat
                                        isShowCloseButton:NO
                                            textAlignment:NSTextAlignmentCenter
                                        cancelButtonTitle:IMYString(@"取消")
                                         otherButtonTitle:IMYString(@"确认")
                                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                        if (sender == messageBox.rightButton) {
                            //怀孕切换其他身份_孕天过小
                            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_ytgx", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                            imy_asyncMainBlock(0.1, ^{
                                deletePregnancyAndChangeMode();
                                [self umEventTrackWithLastMode:lastMode mode:mode];
                            });
                        }
                        if (sender) {
                            [messageBox dismiss];
                        }
                    }];
                } else if (pDay2Today >= 140) {
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"),IMYString(@"宝宝出生了"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode];
                                                      } else if (index == 2) {
                                                          //添加宝宝
                                                          [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"user/baby/add"
                                                                                                                 params:@{
                                                            @"minBirthday":[pDate dateByAddingDays:140],
                                                            @"manualChangeMode":@(YES),
                                                            @"needUnCheckedBaby":@(YES)
                                                          } callbackBlock:^(id result, NSError *error, NSString *eventName) {
                                                              //怀孕切换其他身份_宝宝出生了
                                                              [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_bbcsl", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                              [IMYDayRecordModel birthCurrentPregnancy:result];
                                                              NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:result];
                                                              if (manualOvlution) {
                                                                  [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
                                                              }
                                                              [self changeToMode:mode];
                                                          }];
                                                      } else if (index == 3) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          deletePregnancyAndChangeMode();
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                } else {
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode];
                                                      } else if (index == 2) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          deletePregnancyAndChangeMode();
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                }
                return;
            }
        }
    }
    
    @weakify(self)
    void(^changedBlock)(void) = ^{
        @strongify(self)
        if (mode == IMYVKUserModePregnancy) {
            [self changeToPregnancy];
        } else {
            [self changeToMode:mode];
        }
        
        [self umEventTrackWithLastMode:lastMode mode:mode];
        [IMYGAEventHelper postWithPath:@"bi_mode"
                                params:@{ @"mode": @(mode),
                                          @"exmode": @(lastMode),
                                          @"position": @(self.position)
                                          }
                               headers:nil
                             completed:nil];
    };
    
    // 当前模式为辣妈模式，切换到其他模式
    // 校验宝宝记小工具目前是否有正在上传的图片
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeLama) {
        NSUInteger picCount = 0;
#if __has_include(<BBJBabyHome/BBJBabyHome.h>)
        picCount = [BBJUploader sharedInstance].publishModels.count;
#endif
        if (picCount) {
            NSString *modeString = IMYString(@"经期模式");
            if (mode == IMYVKUserModePregnancy) {
                modeString = IMYString(@"怀孕模式");
            } else if (mode == IMYVKUserModeForPregnant) {
                modeString = IMYString(@"备孕模式");
            }
            NSString *message = [NSString stringWithFormat:IMYString(@"宝宝记%lu条记录正在保存中，确定放弃保存并开始记%@吗？"), (unsigned long)picCount,modeString];
            [UIAlertController imy_showAlertViewWithTitle:nil
                                                  message:IMYString(message)
                                        cancelButtonTitle:IMYString(@"取消")
                                        otherButtonTitles:@[IMYString(@"确定")]
                                                  handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                if (buttonIndex == 1) {
                    IMY_POST_NOTIFY(@"BBJNotification_logout");
                    if (changedBlock) {
                        changedBlock();
                    }
                }
            }];
            return;
        }
    }
    
    
    
    if (consultable) {
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示")
                                        message:[NSString stringWithFormat:IMYString(@"确认要切换至%@身份吗？"), [self stringForMode:mode]]
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"确定")]
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            if (buttonIndex == 1 && changedBlock) {
                                                changedBlock();
                                            }
                                        }];
    } else if (changedBlock) {
        changedBlock();
    }
#endif
}

- (void)sync_confirmChangeToMode:(IMYVKUserMode)mode
                     consultable:(BOOL)consultable
               andDeleteBabyList:(NSArray *)deleteList
                      completion:(SYUserModeSaveLamaBlk)completionBlk{
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYVKUserMode lastMode = self.useLastModeForBi ? self.lastModeForBi : [IMYPublicAppHelper shareAppHelper].userMode;
    if (mode == IMYVKUserModePregnancy) {
        if ([IMYDayRecordModel getLastPregnancyEnd].isToday) {
            [self lastPregnancyEndIsToday];
            !completionBlk?:completionBlk(NO);
            return;
        }
    } else {
        if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {//当前是怀孕身份，切换到非 辣妈身份，要判断是否能添加宝宝
            if (mode != IMYVKUserModeLama) {
                NSDate *pEndDate = [IMYDayRecordModel getLastPregnancyEnd];
                NSDate *pDate = [IMYDayRecordModel getCurrentPregnancyStartDate];
                NSInteger pDay2Today = [pDate distanceInDaysToDate:NSDate.date];
                NSString *modeString = [self stringForMode:mode];
                NSDateFormatter *df = [[NSDateFormatter alloc] init];
                df.dateFormat = @"yyyy.MM.dd";
                NSString *pEndDateString = [df stringFromDate:pEndDate];
                NSString *title = [NSString stringWithFormat:IMYString(@"确定开始%@模式吗？"),modeString];
                NSString *message = [NSString stringWithFormat:IMYString(@"开始%@模式，将删除小于14天的孕期记录（预产期%@）"),modeString,pEndDateString];
                NSString *summary = [NSString stringWithFormat:IMYString(@"请管理当前孕期记录（预产期%@），\n操作后将开始%@模式"),pEndDateString,modeString];
                void (^deletePregnancyAndChangeMode)(void) = ^(){ //这里没有宝宝出生，不需要异步
                    //清除次记录
                    [IMYDayRecordModel deleteCurrentPregnancy];
                    [self changeToMode:mode];
                    
                    //删除孕期要直接上报
                    [IMYGAEventHelper postWithPath:@"bi_mode"
                                            params:@{ @"mode": @(mode),
                                                      @"exmode": @(lastMode),
                                                      @"position": @(self.position)
                                            }
                                           headers:nil
                                         completed:nil];
                };
                
                if (pDay2Today <= 14) {//怀孕天数小于14天，提示删除 妊娠，切换身份
                    [IMYActionMessageBox showBoxWithTitle:title
                                                  message:message
                                                    style:IMYMessageBoxStyleFlat
                                        isShowCloseButton:NO
                                            textAlignment:NSTextAlignmentCenter
                                        cancelButtonTitle:IMYString(@"取消")
                                         otherButtonTitle:IMYString(@"确认")
                                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                        if (sender == messageBox.rightButton) {
                            //怀孕切换其他身份_孕天过小
                            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_ytgx", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                            imy_asyncMainBlock(0.1, ^{
                                deletePregnancyAndChangeMode();
                                [self umEventTrackWithLastMode:lastMode mode:mode];
                            });
                        }
                        if (sender) {
                            [messageBox dismiss];
                        }
                    }];
                } else if (pDay2Today >= 140) { //怀孕身份 大于140天，弹窗3个选择"妊娠终止"  @"宝宝出生了" @"删除此次孕期"
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"),IMYString(@"宝宝出生了"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode];
                                                      } else if (index == 2) {  // 宝宝出生，添加宝宝，切换到其他身份
                                                          //添加宝宝 完成后，回调处理切换身份
                                                          [IMYURIManager.shareURIManager runActionAndSyncResultWithPath:@"user/baby/add" params:@{@"minBirthday":[pDate dateByAddingDays:140],@"manualChangeMode":@(YES)} callbackBlock:^(id result, NSError *error, NSString *eventName) {
                                                              if (result) {
                                                                  //怀孕切换其他身份_宝宝出生了
                                                                  [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_bbcsl", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                                  [IMYDayRecordModel birthCurrentPregnancy:result];
                                                                  NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:result];
                                                                  if (manualOvlution) {
                                                                      [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
                                                                  }
                                                                  [self changeToMode:mode];
                                                              }
                                                  
                                                          }];
                                                      } else if (index == 3) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          deletePregnancyAndChangeMode();
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                } else {
                    @weakify(self);
                    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                             otherTitles:@[IMYString(@"妊娠终止"), IMYString(@"删除此次孕期")]
                                                 summary:summary
                                              showInView:[self getShowInView]
                                                  action:^(NSInteger index) {
                                                      @strongify(self);
                                                      if (index == 1) { //结束此次孕期
                                                          //弹出窗口
                                                          [self showEndPregnacyPicker:mode];
                                                      } else if (index == 2) { //删除此次孕期
                                                          //怀孕切换其他身份_删除此次孕期
                                                          [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_scccyq", @"action": @(2), @"public_type": @(mode)} headers:nil completed:nil];
                                                          deletePregnancyAndChangeMode();
                                                      }
                                                      [self umEventTrackWithLastMode:lastMode mode:mode];
                                                  }];
                }
                return;
            }
        }
    }
    
    @weakify(self)
    void(^changedBlock)(void) = ^{
        @strongify(self)
        if (mode == IMYVKUserModePregnancy) {
            [self changeToPregnancy];
            !completionBlk?:completionBlk(YES);
        } else {
            [self sync_changeToMode:mode andDeleteBabyList:deleteList completion:completionBlk];
        }
        
        [self umEventTrackWithLastMode:lastMode mode:mode];
        [IMYGAEventHelper postWithPath:@"bi_mode"
                                params:@{ @"mode": @(mode),
                                          @"exmode": @(lastMode),
                                          @"position": @(self.position)
                                          }
                               headers:nil
                             completed:nil];
    };
    
    // 当前模式为辣妈模式，切换到其他模式
    // 校验宝宝记小工具目前是否有正在上传的图片
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeLama) {
        NSUInteger picCount = 0;
#if __has_include(<BBJBabyHome/BBJBabyHome.h>)
        picCount = [BBJUploader sharedInstance].publishModels.count;
#endif
        if (picCount) {
            NSString *modeString = IMYString(@"经期模式");
            if (mode == IMYVKUserModePregnancy) {
                modeString = IMYString(@"怀孕模式");
            } else if (mode == IMYVKUserModeForPregnant) {
                modeString = IMYString(@"备孕模式");
            }
            NSString *message = [NSString stringWithFormat:IMYString(@"宝宝记%lu条记录正在保存中，确定放弃保存并开始记%@吗？"), (unsigned long)picCount,modeString];
            [UIAlertController imy_showAlertViewWithTitle:nil
                                                  message:IMYString(message)
                                        cancelButtonTitle:IMYString(@"取消")
                                        otherButtonTitles:@[IMYString(@"确定")]
                                                  handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                if (buttonIndex == 1) {
                    IMY_POST_NOTIFY(@"BBJNotification_logout");
                    if (changedBlock) {
                        changedBlock();
                    }
                }
            }];
            return;
        }
    }
        
    if (consultable) {
        [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示")
                                        message:[NSString stringWithFormat:IMYString(@"确认要切换至%@身份吗？"), [self stringForMode:mode]]
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"确定")]
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            if (buttonIndex == 1 && changedBlock) {
                                                changedBlock();
                                            }
                                        }];
    } else if (changedBlock) {
        changedBlock();
    }
#endif
}

- (void)confirmChangeToMode:(IMYVKUserMode)mode {
    [self confirmChangeToMode:mode consultable:YES];
}
- (void)continueChangeToMode:(IMYVKUserMode)mode {
    [self confirmChangeToMode:mode consultable:NO];
}
- (void)sync_continueChangeToMode:(IMYVKUserMode)mode completion:(SYUserModeSaveLamaBlk)completionBlk{
    [self sync_confirmChangeToMode:mode consultable:NO andDeleteBabyList:nil completion:completionBlk];
}

/**
 友盟事件统计

 @param lastMode 原身份
 @param mode 切换后的身份
 */
- (void)umEventTrackWithLastMode:(IMYVKUserMode)lastMode mode:(IMYVKUserMode)mode {
    NSString *event;
    NSDictionary *attributes = @{IMYString(@"原身份"): [self stringForMode:lastMode]};
    switch (mode) {
        case IMYVKUserModeNormal:
            event = @"jldb-jq";
            break;
        case IMYVKUserModePregnancy:
            event = @"jldb-hy";
            break;
        case IMYVKUserModeForPregnant:
            event = @"jldb-by";
            break;
        case IMYVKUserModeLama:
            event = @"jldb-ye";
            break;
        default:
            break;
    }
    [IMYEventHelper event:event attributes:attributes];
    
}
- (void)changeToMode:(IMYVKUserMode)mode {
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
    [[NSNotificationCenter defaultCenter] postNotificationName:WillChangedUserModeNotification object:@(mode)];
#if __has_include(<IMYMSG/IMYMSG.h>)
    [IMYMsgNotifyManager sharedManager].mode = mode;
    [IMYMsgNotifyManager sharedManager].isChangeMode = YES;
#endif
    if (mode == IMYVKUserModeLama) {
        NSDate *birtyday = self.babyBirthday;
//       这里的妊娠流水出生，已经无效。不处理 Levy
        [IMYDayRecordModel birthCurrentPregnancy:birtyday];
//        [SYUserHelper sharedHelper].babyBirthday = [[NSDate imy_today] imy_getOnlyDateString];
        //记录下用户手动切换到辣妈身份的时间
        NSTimeInterval time = [[NSDate imy_today] timeIntervalSince1970];
        NSString *key = [NSString stringWithFormat:@"userChangeToLamaModeTimestamp-%@",[IMYPublicAppHelper shareAppHelper].userid];
        [[IMYUserDefaults standardUserDefaults] setDouble:time forKey:key];
        
    } else {
        BOOL homeIsBBJEntrance = [[[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"yqhomeAB/lama/homeIsBBJEntrance" params:nil] boolValue];
        if (homeIsBBJEntrance) {
            // 切换到其他身份时，存储最后选中的宝宝ID
            NSInteger currentBabyId = [IMYPublicAppHelper shareAppHelper].currentBabyID;
            NSString *key = [NSString stringWithFormat:@"userChangeFromLamaModeBabyidtemp-%@",[IMYPublicAppHelper shareAppHelper].userid];
            [[IMYUserDefaults standardUserDefaults] setInteger:currentBabyId forKey:key];
        }
        // 去选所有宝宝
        [[IMYRecordBabyManager sharedInstance] unSelectAllBaby];

    }
//    if (mode == IMYVKUserModePregnancy) {
//        [SYUserHelper sharedHelper].babyBirthday = nil;
//    }
    [IMYPublicAppHelper shareAppHelper].userNewState = 2;
    [SYUserHelper sharedHelper].userModelType = [SYUserHelper usermodeFromPublic:mode];
    
    if (mode == IMYVKUserModeLama && [IMYCKABTestManager isYunyuHomeContainStyle]) {
        //切换身份后 辣妈选中最小的宝宝，
        IMYRecordBabyModel *lastBirthdayBaby = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby];
  
        if (lastBirthdayBaby) {
            //同步接口下，先判断当前是否已经is_checked 了
            if (lastBirthdayBaby.is_checked) {
//                [[IMYRecordBabyManager sharedInstance] selectBaby:lastBirthdayBaby.client_id];
            } else {
                [[IMYRecordBabyManager sharedInstance] selectBaby:lastBirthdayBaby.baby_id];
            }
        }
    }
    [[SYUserHelper sharedHelper] saveToDB];
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
    IMY_POST_NOTIFY(ChangedUserModeNotification);
    if (self.changeCompleteBlock) {
        self.changeCompleteBlock(mode);
    }
    if (self.extraChangeCompleteBlock) {
        self.extraChangeCompleteBlock(mode);
    }
#endif
}

/// 857新方法，同步接口
/// - Parameters:
///   - mode: mode description
///   - completionBlk: completionBlk description
- (void)sync_changeToMode:(IMYVKUserMode)mode andDeleteBabyList:(NSArray *)deleteBabyList completion:(SYUserModeSaveLamaBlk)completionBlk{
    //辣妈身份，宝宝同步方案
    @weakify(self)
    [self sync_saveLamaWithBabyBirthday:nil
                                    sex:nil
                                  isNew:NO
                      andDeleteBabyList:deleteBabyList
               isFromForcedHandoverLama:NO 
                             completion:^(BOOL result) {
        [UIWindow imy_hideHUD];
        @strongify(self)
        if (result) {
            [self sync_changeToModeSucess:mode];
        }
        !completionBlk?:completionBlk(result);
    }];
}

/// 切换成功后的操作
/// - Parameter mode: mode description
- (void)sync_changeToModeSucess:(IMYVKUserMode)mode  {
#if __has_include(<IMYRecord/IMYRecord.h>)
    self.lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
    [[NSNotificationCenter defaultCenter] postNotificationName:WillChangedUserModeNotification object:@(mode)];
#if __has_include(<IMYMSG/IMYMSG.h>)
    [IMYMsgNotifyManager sharedManager].mode = mode;
    [IMYMsgNotifyManager sharedManager].isChangeMode = YES;
#endif
    if (mode == IMYVKUserModeLama) {
        NSDate *birtyday = self.babyBirthday;
        //       这里的妊娠流水出生，已经无效。不处理 Levy
        [IMYDayRecordModel birthCurrentPregnancy:birtyday];
//        [SYUserHelper sharedHelper].babyBirthday = [[NSDate imy_today] imy_getOnlyDateString];
        //记录下用户手动切换到辣妈身份的时间
        NSTimeInterval time = [[NSDate imy_today] timeIntervalSince1970];
        NSString *key = [NSString stringWithFormat:@"userChangeToLamaModeTimestamp-%@",[IMYPublicAppHelper shareAppHelper].userid];
        [[IMYUserDefaults standardUserDefaults] setDouble:time forKey:key];
        
    } else {
        BOOL homeIsBBJEntrance = [[[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"yqhomeAB/lama/homeIsBBJEntrance" params:nil] boolValue];
        if (homeIsBBJEntrance) {
            // 切换到其他身份时，存储最后选中的宝宝ID
            NSInteger currentBabyId = [IMYPublicAppHelper shareAppHelper].currentBabyID;
            NSString *key = [NSString stringWithFormat:@"userChangeFromLamaModeBabyidtemp-%@",[IMYPublicAppHelper shareAppHelper].userid];
            [[IMYUserDefaults standardUserDefaults] setInteger:currentBabyId forKey:key];
        }
        // 去选所有宝宝
        [[IMYRecordBabyManager sharedInstance] unSelectAllBaby];

    }
//    if (mode == IMYVKUserModePregnancy) {
//        [SYUserHelper sharedHelper].babyBirthday = nil;
//    }
    [IMYPublicAppHelper shareAppHelper].userNewState = 2;
    [SYUserHelper sharedHelper].userModelType = [SYUserHelper usermodeFromPublic:mode];
    
    if (mode == IMYVKUserModeLama && [IMYCKABTestManager isYunyuHomeContainStyle]) {
        //切换身份后 辣妈选中最小的宝宝，
        IMYRecordBabyModel *lastBirthdayBaby = [[IMYRecordBabyManager sharedInstance] lastBirthdayBaby];
        if (lastBirthdayBaby) {
            [[IMYRecordBabyManager sharedInstance] selectBaby:lastBirthdayBaby.baby_id];
        }
    }
    [[SYUserHelper sharedHelper] saveToDB];
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
    IMY_POST_NOTIFY(ChangedUserModeNotification);
    if (self.changeCompleteBlock) {
        self.changeCompleteBlock(mode);
    }
#endif
}
- (NSString *)stringForMode:(IMYVKUserMode)mode {
    if (mode == IMYVKUserModeNormal) {
        return IMYString(@"经期");
    } else if (mode == IMYVKUserModeForPregnant) {
        return IMYString(@"备孕");
    } else if (mode == IMYVKUserModePregnancy) {
        return IMYString(@"怀孕");
    } else {
        return IMYString(@"辣妈");
    }
}

#pragma mark - 怀孕各种计算
- (void)lastPregnancyEndIsToday {
    @weakify(self)
        [UIAlertController imy_showAlertViewWithTitle:nil
                                        message:IMYString(@"今天是孕期结束日，不可能再次怀孕哦~是否删除现有的孕期记录后再重新记录孕期？")
                              cancelButtonTitle:IMYString(@"取消")
                              otherButtonTitles:@[IMYString(@"确定")]
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            @strongify(self) if (buttonIndex == 1) {
                                                IMYVKUserMode lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
                                                [self changeToPregnancy];
                                                [UIWindow imy_showTextHUD:IMYString(@"删除成功")];

                                                //上报
                                                [IMYGAEventHelper postWithPath:@"bi_mode"
                                                                        params:@{ @"mode": @(IMYVKUserModePregnancy),
                                                                                  @"exmode": @(lastMode),
                                                                                  @"position": @(self.position)
                                                                        }
                                                                       headers:nil
                                                                     completed:nil];
                                            }
                                        }];
}

+ (NSArray *)dueDatedOfChildbirth {
#if __has_include(<IMYRecord/IMYRecord.h>)
    NSDate *dueDate = nil;
    IMYMensesModel *model = [IMYMensesDao getLastMenses];
    NSDate *lastPregnan = ((IMYPregnanceModel *)[IMYPregnanceModel getLastPregnancyModel]).endDate; //最后一次预产期
    NSDate *startDate = nil;
    if (lastPregnan && model.mstartdate) {
        startDate = [model.mstartdate isLaterThanDate:lastPregnan] ? model.mstartdate : [lastPregnan dateByAddingDays:1];
    } else {
        startDate = lastPregnan ? [lastPregnan dateByAddingDays:1] : model.mstartdate;
    }
    if (startDate) {
        NSInteger days = [IMYCalendarUserHelper pregnantInterval];
        days = days > 294 ? 294 : days;
        dueDate = [startDate dateByAddingDays:days];
        if ([dueDate isEarlierThanDate:[[NSDate imy_today] dateByAddingDays:-13]]) {
            dueDate = [[NSDate imy_today] dateByAddingDays:250];
        }
        if ([dueDate daysAfterDate:[NSDate imy_today]] > 280) {
            dueDate = [[NSDate imy_today] dateByAddingDays:250];
        }
        startDate = [dueDate dateByAddingDays:-280];
        if (lastPregnan && [startDate compare:lastPregnan] != NSOrderedDescending) {
            //避免孕期交叉
            dueDate = [[NSDate imy_today] dateByAddingDays:280];
        }
    } else {
        startDate = [[NSDate imy_today] dateByAddingDays:-30];
        NSInteger days = [IMYCalendarUserHelper pregnantInterval];
        days = days > 294 ? 294 : days;
        dueDate = [startDate dateByAddingDays:days];
    }
    startDate = [dueDate dateByAddingDays:-280];
    return @[startDate, dueDate];
#else
    return nil;
#endif
}

- (void)changeToPregnancy {

#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    [IMYRecordPregnancyBabyManager addPregnancyBabyToServerWithDueDate:self.dueDate
                                                            loadingBtn:self.loadingBtn
                                                                 scene:@"changeToPregnancy"
                                                             completed:^(NSError * _Nonnull error) {
        @strongify(self);
        if (!error) {
            [self changeToPregnancySuccess];
        }
    }];

#endif
}
- (void)changeToPregnacyWithDueDate:(NSDate *)dueDate scene:(NSString *)scene loadingBtn:(IMYCKLoadingTextButton *)loadingBtn completeBlock:(SYUserModeSaveLamaBlk)block {
#if __has_include(<IMYRecord/IMYRecord.h>)
    @weakify(self);
    [IMYRecordPregnancyBabyManager addPregnancyBabyToServerWithDueDate:dueDate
                                                            loadingBtn:loadingBtn
                                                                 scene:scene
                                                             completed:^(NSError * _Nonnull error) {
        @strongify(self);
        if (!error) {
            [self changeToPregnancySuccess];
        }
        if (error && [error isKindOfClass:[NSError class]]) {
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"IMYModeWelcomeViewController"
                                category:IMYErrorTraceCategoryYunyu
                                 message:@"新增孕期胎宝宝 失败"
                                  detail:@{
                                           @"code":@(error.code),
                                           @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                         }];
        }
        !block?:block(!error);
    }];

#endif
}
- (void)changeToPregnancySuccess {
#if __has_include(<IMYRecord/IMYRecord.h>)
    IMYVKUserMode exMode = [IMYPublicAppHelper shareAppHelper].userMode;
    //改为同步后，这操作多余了 照旧逻辑走 Levy
    [IMYDayRecordModel deleteCurrentPregnancy];
    [IMYDayRecordModel setPregnancyDueDate:self.dueDate];
    [self changeToMode:IMYVKUserModePregnancy];

    // 设备push开关未打开时，发送一条消息，引导用户打开
    if (![[IMYPublicAppHelper shareAppHelper] enabledNotification]) {
        [self sendOpenPushMessage];
        [self messageBI:exMode];
    }
#endif
}
// 取280天之前
- (NSDate *)calculateMinDate {
    return [[NSDate imy_today] dateByAddingDays:-280];
}

- (NSDate *)miniPregnacyPickerDate {
    return [[NSDate imy_today] dateByAddingDays:-13];
}

#pragma mark - Picker

- (void)showEndPregnacyPicker:(IMYVKUserMode)toMode {
    [self showEndPregnacyPicker:toMode toBlock:NO changeToModeBlock:nil];
}


- (void)showEndPregnacyPicker:(IMYVKUserMode)toMode toBlock:(BOOL)toBlock changeToModeBlock:(void(^)(IMYVKUserMode toMode))changeToModeBlock{
#if __has_include(<IMYRecord/IMYRecord.h>)
    if (self.pickerView) {
        [self.pickerView freePicker];
    }
    @weakify(self)
    NSDate *pregnacySart = [IMYDayRecordModel getCurrentPregnancyStartDate];
    NSArray *data = nil;
    if (toMode == IMYVKUserModeLama) {
        data = pregnacySart ? @[[pregnacySart dateByAddingDays:1], [NSDate imy_today]] : @[IMYPickerViewDefaultDate, [NSDate imy_today]];
    } else {
        data = @[[pregnacySart dateByAddingDays:15], [NSDate imy_today]];
    }
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
                                                                      dataArray:data
                                                                  pickerViewTpe:IMYPickerViewTypeDate
                                                                   confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
        @strongify(self);
        IMYVKUserMode lastMode = [IMYPublicAppHelper shareAppHelper].userMode;
        if (toMode == IMYVKUserModeLama) {
            [IMYDayRecordModel birthCurrentPregnancy:result.resultDate];
        } else {
            
        }
        
        [IMYRecordPregnancyBabyManager suspendGestationWithDueDate:result.resultDate
                                                        loadingBtn:self.loadingBtn
                                                     completeBlock:^(NSError * _Nullable error) {
            @strongify(self);
            if (!error) {
                [IMYDayRecordModel endCurrentPregnancy:result.resultDate];
                NSDate *manualOvlution = [IMYMensesDao findManualOvulatinDateFromSomeday:result.resultDate];
                if (manualOvlution) {
                    [IMYDayRecordDao setManualOvulation:NO date:manualOvlution];
                }
                if (toBlock) {
                    if (changeToModeBlock) {
                        changeToModeBlock(toMode);
                    }
                } else{
                    [self changeToMode:toMode];
                }
                //怀孕切换其他身份_妊娠终止
                [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"hyqhqtsf_rszz", @"action": @(2), @"public_type": @(toMode)} headers:nil completed:nil];
                [IMYGAEventHelper postWithPath:@"bi_mode"
                                        params:@{ @"mode": @(toMode),
                                                  @"exmode": @(lastMode),
                                                  @"position": @(self.position)
                                               }
                                       headers:nil
                                     completed:nil];

            }
        }];
    }
                                                                    cancelBlock:^{
        
    }];
    [picker setSelectWithDate:[NSDate imy_today]];
    picker.title = toMode == IMYVKUserModeLama ? IMYString(@"选择宝宝出生日") : IMYString(@"请选择妊娠终止日");
    [picker show];
    self.pickerView = picker;
#endif
}

- (void)showPregancyStartPicker {
    if (self.pickerView) {
        [self.pickerView freePicker];
    }
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[[NSDate imy_today], IMYPickerViewDefaultDate]
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            BOOL valid = [self checkPregnancyValid:result.resultDate
                                     completeBlock:^(BOOL valid, NSDate *resulteDate){

                                     }];
            if (valid) {
                [self changeToPregnancy];
                [self.pickerView hide];
            }
        }
        cancelBlock:^{
            @strongify(self);
            [self.pickerView hide];
        }];
    picker.outDismissControl = YES;
    picker.title = IMYString(@"选择孕期结束日");
    [picker show];
    self.pickerView = picker;
}

#pragma mark - 身份选择
- (IMYVKUserMode)modeForRow:(NSInteger)row {
    if (row == 0) {
        return IMYVKUserModeNormal;
    }
    if (row == 1) {
        return IMYVKUserModeForPregnant;
    }
    if (row == 2) {
        return IMYVKUserModePregnancy;
    }
    if (row == 3) {
        return IMYVKUserModeLama;
    }
    return IMYVKUserModeNormal;
}

#pragma mark - 消息

- (void)sendOpenPushMessage {
    //单个设备近5天内最多提醒1次，即2次提醒间隔至少5天
    NSDate *today = [NSDate imy_today];
    NSString *lastAlertKey = @"kOpenPushMessageKey";
    NSDate *lastAlertDate = [[IMYUserDefaults standardUserDefaults] objectForKey:lastAlertKey];
    if (!lastAlertDate || [today daysAfterDate:lastAlertDate] >= 5) {
        [[IMYUserDefaults standardUserDefaults] setObject:today forKey:lastAlertKey];
        [[IMYUserDefaults standardUserDefaults] synchronize];
    } else {
        return;
    }

    NSDate *date_now = [NSDate date];
    NSTimeZone *tz = [NSTimeZone defaultTimeZone];
    NSInteger seconds = -[tz secondsFromGMTForDate:date_now];
    NSDate *date = [NSDate dateWithTimeInterval:seconds sinceDate:date_now];
    
    NSString *dateString = [[IMYDateFormatter imy_getDateTimeFormater] stringFromDate:date];
    NSDictionary *publisher = @{@"avatar": @"http://sc.seeyouyima.com/xxy_400.png"};
    NSDictionary *message = @{ @"uri_type": @(48),
                               @"title": IMYString(@"通知功能被关闭了哦~"),
                               @"content": IMYString(@"亲爱的准妈妈，开启通知后孕期各阶段注意事项一个不落~"),
                               @"image": @"http://estatic.seeyouyima.com/myyq/95B8.png",
                               @"updated_date": dateString,
                               @"publisher": publisher,
                               @"uri": @"push/system?from=message",
                               @"url_title": IMYString(@"立即开启") };
    NSDictionary *msg = @{ @"type": @(3),
                           @"push_type": @(48),
                           @"message": message,
                           @"sn": dateString };

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:msg options:0 error:&error];
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

    [[NSNotificationCenter defaultCenter] postNotificationName:@"kPushMessageReceived" object:@[jsonString]];
}

- (void)messageBI:(IMYVKUserMode)exmode {
    NSDictionary *params = @{ @"push_id": @(1000),
                              @"project_type": IMYString(@"身份切换"),
                              @"exmode": @(exmode),
                              @"mode": @(IMYVKUserModePregnancy),
                              @"action": @(0) };
    [IMYGAEventHelper postWithPath:@"bi_message_push" params:params headers:nil completed:NULL];
}

// 7.6.8 BI_宝宝切换埋点
// https://www.tapd.cn/21039721/prong/stories/view/1121039721001048436
- (void)postBabyChangeBabyId:(NSInteger)babyId mode:(NSInteger)mode exBabyId:(NSInteger)exBabyId exMode:(NSInteger)exMode {
    id (^babyIdVar)(NSInteger originBabyId) = ^(NSInteger originBabyId) {
        return (originBabyId == 0) ? @"null" : [NSString stringWithFormat:@"%ld",originBabyId];
    };

    [IMYGAEventHelper postWithPath:@"bi_baby_change"
                            params:@{ @"baby_id": babyIdVar(babyId),
                                      @"mode": @(mode),
                                      @"exbaby_id": babyIdVar(exBabyId),
                                      @"exmode": @(exMode),
                                      @"position": @(3)}
      headers:nil
    completed:nil];
}

-(NSInteger)position{
    if (_position == 0) {
        return 4;
    }
    return _position;
}

- (NSDate *)babyBirthday{
    if(!_babyBirthday){
        _babyBirthday = [NSDate imy_today];
    }
    return _babyBirthday;
}

@end
