//
//  SYUserModeViewModel.h
//  Seeyou
//
//  Created by 林云峰 on 16/8/9.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <IMYBaseKit/IMYPublic.h>
#import "IMYCKLoadingTextButton.h"

typedef void (^CheckValidResultBlock)(BOOL valid, NSDate *resulteDate);
typedef void (^ChangeModeCompleteBlock)(IMYVKUserMode mode);
typedef void(^SYUserModeSaveLamaBlk)(BOOL result);


@interface SYUserModeViewModel : NSObject
@property (nonatomic, copy) ChangeModeCompleteBlock changeCompleteBlock;
@property (nonatomic, copy) ChangeModeCompleteBlock extraChangeCompleteBlock;

/// 857 添加宝宝，回调出去到VC，
@property (nonatomic, copy) ChangeModeCompleteBlock extraChangeCompleteBlockForSync;

@property (nonatomic, weak) UIViewController *controller;
@property (nonatomic, weak) IMYCKLoadingTextButton *loadingBtn;//转圈圈的按钮

//https://www.tapd.cn/21039721/prong/stories/view/1121039721001080515
@property (nonatomic, assign) NSInteger position; //埋点用
@property (nonatomic, assign) NSInteger public_info; //埋点用
// 切换身份过程中当前的usermode可能会变 
@property (nonatomic, assign) IMYVKUserMode lastModeForBi;
@property (nonatomic, assign) BOOL useLastModeForBi;

@property (nonatomic, strong) NSDate *dueDate;      //用户手动选的预产期
@property (nonatomic, strong) NSDate *babyBirthday;////< 宝宝的出生日
@property (nonatomic, assign) NSInteger babySex;////< 宝宝的性别
/**
 *  验证预产期是否可用
 *
 *  @param pregnancyDate 预产期
 *  @param block         验证回调
 *
 *  @return 验证结果
 */
- (BOOL)checkPregnancyValid:(NSDate *)pregnancyDate completeBlock:(CheckValidResultBlock)block;
/**
 *  保存孕产期
 *
 *  @param date 孕产期
 */
- (void)savePregancyWithDate:(NSDate *)date;
/**
 *  最小末次经期开始日
 *
 *  @return date
 */
- (NSDate *)calculateMinDate;
/**
 *  孕产期picker可供选择的最小孕产期
 *
 *  @return date
 */
- (NSDate *)miniPregnacyPickerDate;
/**
 *  确认切换身份
 *
 *  @param mode 要切换的身份
 *  @param consultable 是否确定切换弹框
 */
- (void)confirmChangeToMode:(IMYVKUserMode)mode consultable:(BOOL)consultable;

- (void)sync_confirmChangeToMode:(IMYVKUserMode)mode
                     consultable:(BOOL)consultable
               andDeleteBabyList:(NSArray *)deleteList
                      completion:(SYUserModeSaveLamaBlk)completionBlk;

- (void)confirmChangeToMode:(IMYVKUserMode)mode;
- (void)continueChangeToMode:(IMYVKUserMode)mode;
- (void)sync_continueChangeToMode:(IMYVKUserMode)mode completion:(SYUserModeSaveLamaBlk)completionBlk;

//--欢迎页面点击 下一步 弹出的alter 逻辑
- (void)welcomeStyleCheckAlterConfirmChangeToMode:(IMYVKUserMode)mode changeToModeBlock:(void(^)(IMYVKUserMode toMode))changeToModeBlock deletePregnancyAndChangeMode:(void(^)(IMYVKUserMode toMode))deletePregnancyAndChangeMode;
//切换至怀孕
- (void)changeToPregnancy;
//切换至怀孕有回调
- (void)changeToPregnacyWithDueDate:(NSDate *)dueDate scene:(NSString *)scene loadingBtn:(IMYCKLoadingTextButton *)loadingBtn completeBlock:(SYUserModeSaveLamaBlk)block ;
;
/**
 *  切换身份
 *
 *  @param mode 要切换的身份
 */
- (void)changeToMode:(IMYVKUserMode)mode;

/// 857新方法，同步接口
/// - Parameters:
///   - mode: mode description
///   - completionBlk: completionBlk description
- (void)sync_changeToMode:(IMYVKUserMode)mode completion:(SYUserModeSaveLamaBlk)completionBlk;
//各身份名称
- (NSString *)stringForMode:(IMYVKUserMode)mode;
- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate sheetCompleteBlock:(CheckValidResultBlock)block;
- (BOOL)makeSureShortPregnacy:(NSDate *)dueDate;
/**
 *  保存宝宝出生日
 *
 *  @param babyBirthday 宝宝出生日
 */
- (void)saveLamaWithBabyBirthday:(NSDate *)babyBirthday sex:(NSInteger)sex isNew:(BOOL)isNew;

- (void)sync_saveLamaWithBabyBirthday:(NSDate *)babyBirthday
                                  sex:(NSInteger)sex
                                isNew:(BOOL)isNew
                    andDeleteBabyList:(NSArray *)deleteBabyList
             isFromForcedHandoverLama:(BOOL)isFromForcedHandoverLama
                           completion:(SYUserModeSaveLamaBlk)completionBlk;
- (void)sync_saveLamaWithBabyBirthday:(NSDate *)babyBirthday
                                  sex:(NSInteger)sex
                             nickName:(NSString *)nickName
                                isNew:(BOOL)isNew
                    andDeleteBabyList:(NSArray *)deleteBabyList
             isFromForcedHandoverLama:(BOOL)isFromForcedHandoverLama
                           completion:(SYUserModeSaveLamaBlk)completionBlk;
//同步宝宝其他信息
- (void)syncBabyInfoWithBirthType:(NSInteger)birthType weight:(NSString *)weight bbj_baby_id:(NSInteger)bbj_baby_id completion:(void (^)(BOOL success,NSString *errorMessage))completion;
//身份选择时返回的行数
- (IMYVKUserMode)modeForRow:(NSInteger)row;

//验证孕产期是否跟之前孕期交叉
- (BOOL)isCrossAnotherPregnacy:(NSDate *)pregnancyDate;

/// 跟设置的预产期冲突的孕期数据(当前设置预产期（即当前孕期）对应孕期开始日与历史孕期结束日间隔≤14天)
- (NSArray *)conflictPregnacyDatesWithNewDueDate:(NSDate *)dueDate;

//计算获取孕期开始日及预产期
+ (NSArray *)dueDatedOfChildbirth;

@end
