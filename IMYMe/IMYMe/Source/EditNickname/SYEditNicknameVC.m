//
//  SYEditNicknameVC.m
//  Seeyou
//
//  Created by <PERSON> on 13-9-12.
//  Copyright (c) 2013年 linggan. All rights reserved.
//

#import "SYEditNicknameVC.h"
#import "IMYMe.h"
#import <IMYAccount/IMYAccountServerURL.h>

@interface SYNickNameUpdateTimes : NSObject

@property (nonatomic, assign) NSInteger remain_times;
@property (nonatomic, assign) NSInteger times;

@end

@implementation SYNickNameUpdateTimes

@end

@interface SYEditNicknameVC ()

@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) SYNickNameUpdateTimes *updateTimes;

@end

@implementation SYEditNicknameVC

- (void)dealloc {
    if (self.editFinishBlock) {
        self.editFinishBlock();
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initTextXib];
    self.navigationItem.title = IMYString(@"昵称");
    
    _textField.text = [SYUserHelper sharedHelper].screen_name;
    _textField.tintColor = [UIColor imy_colorForKey:kCK_Red_B];
    [_textField imy_setTextColor:kCK_Black_A];
    [_textField imy_setClearButtonStyle:1];
    [_textField imy_setPlaceholderColorForKey:kCK_Black_B];
    
    [_bg_text imy_setBackgroundColorForKey:kCK_White_AN];
    
    [self loadData];
    
    self.navigationController.view.userInteractionEnabled = NO;
    imy_asyncMainBlock(1, ^{
        self.navigationController.view.userInteractionEnabled = YES;
    });
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [_textField becomeFirstResponder];
}

#pragma mark - 昵称修改次数

- (void)loadData {
    self.captionView.state = IMYCaptionViewStateLoading;
    @weakify(self);
    [[IMYServerRequest getPath:@"v3/profile/update_times" host:users_seeyouyima_com params:@{@"screen_name_origin": @(1)} headers:nil] subscribeNext:^(IMYHTTPResponse *response) {
        @strongify(self);
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            self.captionView.state = IMYCaptionViewStateHidden;
            
            NSDictionary *dataDic = response.responseObject[@"screen_name"];
            self.updateTimes = [dataDic toModel:[SYNickNameUpdateTimes class]];
            
            [self refreshWithData];
        });
        
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        imy_asyncMainExecuteBlock(^{
            @strongify(self);
            self.captionView.state = IMYCaptionViewStateHidden;
            if ([IMYNetState networkEnable]) {
                [self.captionView setTitle:MT_Request_Retry andState:IMYCaptionViewStateRetry];
            } else {
                [self.captionView setTitle:MT_Request_NoNet andState:IMYCaptionViewStateRetry];
            }
        });
    }];
}

- (void)refreshWithData {
    NSString *timesString = [NSString stringWithFormat:@"%ld", (long)self.updateTimes.times];
    NSString *remainTimes = [NSString stringWithFormat:@"%ld", (long)self.updateTimes.remain_times];
    
    NSString *str1 = [NSString stringWithFormat:@"昵称每月只能修改%@次，", timesString];
    NSMutableAttributedString *mutAttStr1 = [[NSMutableAttributedString alloc] initWithString:str1];
    [mutAttStr1 addAttribute:NSForegroundColorAttributeName value:[UIColor imy_colorForKey:kCK_Red_A] range:[str1 rangeOfString:timesString]];
    [mutAttStr1 addAttribute:NSKernAttributeName value:@(0) range:[str1 rangeOfString:timesString]];
    
    NSString *str2 = [NSString stringWithFormat:@"本月剩余%@次", remainTimes];
    NSMutableAttributedString *mutAttStr2 = [[NSMutableAttributedString alloc] initWithString:str2];
    [mutAttStr2 addAttribute:NSForegroundColorAttributeName value:[UIColor imy_colorForKey:kCK_Red_A] range:[str2 rangeOfString:remainTimes]];
    [mutAttStr2 addAttribute:NSKernAttributeName value:@(0) range:[str2 rangeOfString:timesString]];
    
    [mutAttStr1 appendAttributedString:mutAttStr2.copy];
    
    [self.nickCountLabel imy_addThemeChangedBlock:^(UILabel *weakObject) {
        [weakObject imy_setTextColor:kCK_Black_B];
        [weakObject setAttributedText:mutAttStr1];
    }];
}

- (IBAction)handleRuleButtonPressed:(id)sender {
    NSString *ruleString = @"https://nodejs-user.seeyouyima.com/users/name-change-rules.html";
    IMYVKWebViewController *web = [IMYVKWebViewController webWithURLString:ruleString];
    [self imy_push:web];
}

#pragma mark -

- (IBAction)save:(id)sender {
    NSString *nickname = _textField.text.imy_trimString;
    if (nickname.length) {
        if ([_textField.text containsString:@" "]) {
            [UIAlertController imy_showAlertViewWithTitle:nil message:IMYString(@"昵称不能含有空格哦~") cancelButtonTitle:IMYString(@"知道了") otherButtonTitles:nil handler:NULL];
            return;
        }
        if (_textField.text.imy_charCount < 2) {
            [UIAlertController imy_showAlertViewWithTitle:nil message:IMYString(@"昵称太短了，最少两个字哦~") cancelButtonTitle:IMYString(@"知道了") otherButtonTitles:nil handler:NULL];
            return;
        }
        if (_textField.text.imy_charCount > 20) {
            [UIAlertController imy_showAlertViewWithTitle:nil message:IMYString(@"昵称太长了，简化后再试试哦~") cancelButtonTitle:IMYString(@"知道了") otherButtonTitles:nil handler:NULL];
            return;
        }
        //不允许昵称数字超过6个
        NSUInteger numberCount = [self numberCountOfString:_textField.text];
        if (numberCount > 6) {
            [UIAlertController imy_showAlertViewWithTitle:@"" message:IMYString(@"请减少数字再设置看看哦～") cancelButtonTitle:IMYString(@"知道了") otherButtonTitles:nil handler:nil];
            return;
        }
        [self.view endEditing:YES];
        [self syncScreenName:nickname];
    } else {
        [UIAlertController imy_showAlertViewWithTitle:@"" message:IMYString(@"没有输入昵称不能保存哦～") cancelButtonTitle:IMYString(@"知道了") otherButtonTitles:nil handler:nil];
    }
}

- (void)syncScreenName:(NSString *)name {
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:kStatusText_networkDisconnectCache];
        return;
    }
    [self imy_showTextHUDWithDelay:MAXFLOAT text:IMYString(@"正在保存昵称...")];

    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    [self uploadNewName:name];
}

- (void)uploadNewName:(NSString *)name {
    
    NSDictionary *params = @{@"screen_name": name};

    @weakify(self);

    NSData *bodyData = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];

    NSString *signStr = [SYUserHelper getSign:bodyData];
    NSString *headStr = [NSString stringWithFormat:@"XDS %@", [SYPublicFun getUserTicket]];

    NSMutableDictionary *header = [NSMutableDictionary dictionary];
    header[@"Authorization"] = headStr;
    header[@"Content-Signature"] = signStr;
    header[@"Content-Length"] = [NSString stringWithFormat:@"%ld", (long)bodyData.length];
    
    [[[IMYServerRequest put:@"me" host:users_seeyouyima_com params:bodyData headers:nil].RequestSerializerType(IMYHTTPSerializerTypeBody).signal deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *response) {
        @strongify(self);
        [self imy_hideHUD];
        [self imy_showTextHUD:IMYString(@"设置成功")];
        [SYUserHelper sharedHelper].screen_name = self.textField.text;
        [SYUserHelper sharedHelper].nickname = self.textField.text;
        [[SYUserHelper sharedHelper] saveToDB];
        // 更新联合登录
        [IMYAccountUnionLoginService updateAccountNickName:self.textField.text];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SYUserInfoUploadNicknameSuccess" object:nil];
        imy_asyncMainBlock(0.5, ^{
            [self imy_pop:YES];
        });
    } error:^(NSError *error) {
        @strongify(self);
        [self imy_hideHUD];
        IMYWebMessageModel *failedModel = [error.af_responseData toModel:[IMYWebMessageModel class]];
        if (failedModel.code == 403) {
            SYNickNameOccupiedModel *occupiedModel = [error.af_responseData toModel:[SYNickNameOccupiedModel class] forKey:@"message"];
            //李瑞说极小概率3次都还是被占用的处理
            if (imy_isEmptyString(occupiedModel.screen_name)) {
                [UIWindow imy_showTextHUD:occupiedModel.title ?: IMYString(@"昵称被使用了，再想一个吧～")];
                return;
            }
            [UIAlertController imy_showAlertViewWithTitle:occupiedModel.title message:[NSString stringWithFormat:@"%@%@\r\n%@", occupiedModel.tip, occupiedModel.screen_name, occupiedModel.tip_dislike] cancelButtonTitle:nil otherButtonTitles:@[IMYString(@"保存"), IMYString(@"取消")] handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                if (buttonIndex == 0) {
                    self.textField.text = occupiedModel.screen_name;
                    [self syncScreenName:occupiedModel.screen_name];
                }
            }];
        } else if (failedModel.code == kPhoneStolenErrorCode || failedModel.code == kPhoneDubiousErrorCode) {
            int type = (failedModel.code == kPhoneStolenErrorCode) ? 1 : 2;
            [[IMYURIManager shareURIManager] runActionWithPath:@"account/phone/verify" params:@{ @"type": @(type) } info:nil];
        } else if (failedModel.code == 400) {
            // 用户当月修改次数=3，toast：修改昵称失败，本月可修改次数为0
            IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
            NSString *message = model.message ?: IMYString(@"修改昵称失败，本月可修改次数为0");
            [UIWindow imy_showTextHUD:message];
        } else if (failedModel.code == ********) {
            // 【【跨迭代】用户修改昵称，命中职业敏感词库时，弹窗提示】
            // https://www.tapd.meiyou.com/********/prong/stories/view/11********001195404
            IMYActionMessageBox *box = [IMYActionMessageBox showBoxWithTitle:nil message:failedModel.message action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
                if (sender != nil) {
                    // 点击空白区 不关闭
                    [messageBox dismiss];
                }
                if (sender == messageBox.rightButton) {
                    // 跳转七鱼客服
                    self.view.userInteractionEnabled = NO;
                    imy_asyncMainBlock(0.3, ^{
                        self.view.userInteractionEnabled = YES;
                        [[IMYURIManager shareURIManager] runActionWithPath:@"qiyu/chat" params:@{
                            @"groupId": @480815878
                        } info:nil];
                    });
                }
            }];
            UILabel *messageLabel = (id)box.contentView;
            if ([messageLabel isKindOfClass:UILabel.class]) {
                messageLabel.textColor = IMY_COLOR_KEY(kCK_Black_A);
            }
            [box.leftButton imy_setTitle:@"取消"];
            [box.rightButton imy_setTitle:@"联系客服"];
        } else {
            IMYWebMessageModel *model = [error.af_responseData toModel:[IMYWebMessageModel class]];
            NSString *message = model.message ?: IMYString(@"保存昵称失败，请重试");
            [UIWindow imy_showTextHUD:message];
            [IMYEventHelper event:@"ggtccx" attributes:@{@"来源": message}];
        }
        
        // 上报修改昵称失败
        [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                            pageName:IMYMeetyouHTTPHooks.currentPageName
                            category:IMYErrorTraceCategoryUser
                             message:@"edit_nickname_fails"
                              detail:@{
            @"screen_name" : name ?: @"",
            @"http_code" : @(error.code),
            @"http_domain" : error.domain ?: @"",
            @"response" : error.af_responseData.imy_jsonString ?: @"",
        }];
    }];
}

- (NSUInteger)numberCountOfString:(NSString *)string {
    NSString *pattern = @"[0-9]";
    NSRegularExpression *regex = [[NSRegularExpression alloc] initWithPattern:pattern options:0 error:nil];
    NSArray *results = [regex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    __block NSInteger numberCount = 0;
    [results enumerateObjectsUsingBlock:^(NSTextCheckingResult *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        numberCount += obj.range.length;
    }];
    return numberCount;
}

- (void)initTextXib {
    self.saveButton.layer.cornerRadius = self.saveButton.imy_height / 2.0;
    self.saveButton.layer.masksToBounds = YES;
    self.saveButton.titleLabel.font = [UIFont systemFontOfSize:18];
    [self.saveButton imy_setTitle:IMYString(@"保存")];
    [self.saveButton imy_setTitleColor:IMY_COLOR_KEY(kCK_White_A)];
    [self.saveButton imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Red_A)] state:UIControlStateNormal stretch:YES];
    [self.saveButton imy_setBackgroundImage:[UIImage imageWithColor:IMY_COLOR_KEY(kCK_Black_K)] state:UIControlStateDisabled stretch:YES];

    self.textField.placeholder = IMYString(@"请输入昵称");
    [self.textField imy_setPlaceholderColorForKey:kCK_Black_B];
    [self.textField imy_setTextColor:kCK_Black_A];
    
    NSString *tipStr = IMYString(@"昵称将是您在美柚她她圈里的身份标识哦，请设置您独一无二的昵称：");
    NSMutableAttributedString *tipMutAttString = [[NSMutableAttributedString alloc] initWithString:tipStr];
    // 1、字间距0
    [tipMutAttString addAttribute:NSKernAttributeName value:@(0) range:NSMakeRange(0, tipStr.length)];
    [tipMutAttString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:13] range:NSMakeRange(0, tipStr.length)];
    // 2、行间距7
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:7];
    [tipMutAttString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, tipStr.length)];
    [self.tipsLabel setAttributedText:tipMutAttString];
    
    [self.tipsLabel imy_setTextColor:kCK_Black_B];
    
    [self.nickCountLabel imy_setTextColor:kCK_Black_B];
    
    [self.ruleButton imy_setTitleColor:kCK_Colour_A];
}

#pragma mark - get

- (IMYCaptionView *)captionView {
    if (_captionView == nil) {
        _captionView = [[IMYCaptionView alloc] initWithFrame:self.view.bounds];
        [self.view addSubview:_captionView];
        _captionView.state = IMYCaptionViewStateLoading;
        @weakify(self);
        [_captionView setRetryBlock:^{
            @strongify(self);
            [self loadData];
        }];
    }
    return _captionView;
}

@end

@implementation SYNickNameOccupiedModel

@end
