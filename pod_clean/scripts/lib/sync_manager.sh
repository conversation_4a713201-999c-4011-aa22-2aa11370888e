#!/bin/bash

# Pod库优化工具 - 同步管理模块 v3.0
# 功能: 基于新状态管理系统的CSV导出/导入和远程同步
# 特性:
#   - 支持pod_status_summary和optimization_details表的完整同步
#   - 增量同步机制，仅同步变更数据
#   - 智能冲突解决和数据合并
#   - 多用户协作支持
# 用法: source "$SCRIPT_DIR/lib/sync_manager.sh"
# 依赖: sqlite3, git, database.sh, status_manager.sh, common.sh
# 版本: v3.0

# CSV格式版本定义
CSV_FORMAT_VERSION="3.0"
CSV_STATUS_HEADER="format_version,pod_name,original_png_count,original_jpg_count,original_gif_count,original_total_count,original_total_size,current_png_count,current_jpg_count,current_gif_count,current_total_count,current_total_size,deleted_count,compressed_count,heic_converted_count,total_space_saved,last_optimization_time,last_scan_time,optimization_types,user_id,sync_timestamp"
CSV_DETAILS_HEADER="format_version,pod_name,optimization_id,operation_type,files_affected,space_saved,compression_ratio,operation_time,details,user_id,sync_timestamp"

# 获取用户标识
get_user_id() {
    # 优先使用 git config，其次环境变量，最后默认值
    local user_id
    user_id=$(git config user.name 2>/dev/null || echo "${USER:-unknown}")
    echo "$user_id"
}

# 获取最后同步时间戳
get_last_sync_timestamp() {
    local sync_type="$1"  # export 或 import
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" "SELECT MAX(sync_time) FROM sync_log WHERE status='$sync_type';" 2>/dev/null || echo ""
    fi
}

# 确保同步目录存在
ensure_sync_dir() {
    # 如果 SCRIPT_DIR 未设置，使用当前脚本所在目录的父目录
    if [ -z "$SCRIPT_DIR" ]; then
        export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    fi
    if [ -z "$DB_DIR" ]; then
        # 从lib目录向上两级到达项目根目录pod_clean
        export PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
        export DB_DIR="$PROJECT_ROOT/db"
    fi
    if [ -z "$DB_FILE" ]; then
        export DB_FILE="$DB_DIR/optimization.db"
    fi
    mkdir -p "$DB_DIR/sync"
    mkdir -p "$DB_DIR/sync/processed"
    mkdir -p "$DB_DIR/sync/export"
    mkdir -p "$DB_DIR/sync/import"
}

# 记录同步操作日志
record_sync_log() {
    local sync_file="$1"
    local sync_type="$2"  # export 或 import
    local user_id="$3"

    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT INTO sync_log (sync_file, user_id, status, sync_time)
VALUES ('$sync_file', '$user_id', '$sync_type', CURRENT_TIMESTAMP);
EOF
    fi
}

# 导出Pod状态摘要为CSV
export_pod_status_to_csv() {
    local output_file="$1"
    local incremental="$2"  # true/false
    local user_id=$(get_user_id)
    local sync_timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if [ ! -f "$DB_FILE" ]; then
        log_warning "本地数据库不存在，跳过导出"
        return 1
    fi

    # 写入CSV头部
    echo "$CSV_STATUS_HEADER" > "$output_file"

    # 构建WHERE条件（增量导出）
    local where_clause=""
    if [ "$incremental" = "true" ]; then
        local last_sync=$(get_last_sync_timestamp "export")
        if [ -n "$last_sync" ]; then
            where_clause="WHERE last_scan_time > '$last_sync' OR last_optimization_time > '$last_sync'"
        fi
    fi

    # 导出数据
    sqlite3 "$DB_FILE" << EOF
.mode csv
.headers off
SELECT
    '$CSV_FORMAT_VERSION',
    pod_name,
    COALESCE(original_png_count, 0),
    COALESCE(original_jpg_count, 0),
    COALESCE(original_gif_count, 0),
    COALESCE(original_total_count, 0),
    COALESCE(original_total_size, 0),
    COALESCE(current_png_count, 0),
    COALESCE(current_jpg_count, 0),
    COALESCE(current_gif_count, 0),
    COALESCE(current_total_count, 0),
    COALESCE(current_total_size, 0),
    COALESCE(deleted_count, 0),
    COALESCE(compressed_count, 0),
    COALESCE(heic_converted_count, 0),
    COALESCE(total_space_saved, 0),
    COALESCE(last_optimization_time, ''),
    COALESCE(last_scan_time, ''),
    COALESCE(optimization_types, ''),
    '$user_id',
    '$sync_timestamp'
FROM pod_status_summary
$where_clause
ORDER BY last_scan_time DESC;
EOF
}

# 导出优化详情为CSV
export_optimization_details_to_csv() {
    local output_file="$1"
    local incremental="$2"  # true/false
    local user_id=$(get_user_id)
    local sync_timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if [ ! -f "$DB_FILE" ]; then
        log_warning "本地数据库不存在，跳过导出"
        return 1
    fi

    # 写入CSV头部
    echo "$CSV_DETAILS_HEADER" > "$output_file"

    # 构建WHERE条件（增量导出）
    local where_clause=""
    if [ "$incremental" = "true" ]; then
        local last_sync=$(get_last_sync_timestamp "export")
        if [ -n "$last_sync" ]; then
            where_clause="WHERE operation_time > '$last_sync'"
        fi
    fi

    # 导出数据
    sqlite3 "$DB_FILE" << EOF
.mode csv
.headers off
SELECT
    '$CSV_FORMAT_VERSION',
    pod_name,
    COALESCE(optimization_id, ''),
    operation_type,
    COALESCE(files_affected, 0),
    COALESCE(space_saved, 0),
    COALESCE(compression_ratio, 0),
    operation_time,
    COALESCE(details, ''),
    '$user_id',
    '$sync_timestamp'
FROM optimization_details
$where_clause
ORDER BY operation_time DESC;
EOF
}

# 主要的CSV导出功能（功能9：提交优化↑）
export_optimization_data() {
    local incremental="${1:-true}"  # 默认增量导出
    local force_full="${2:-false}"  # 强制全量导出

    ensure_sync_dir
    local user_id=$(get_user_id)
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local export_dir="$DB_DIR/sync/export"

    # 确定导出类型
    local export_type="incremental"
    if [ "$force_full" = "true" ]; then
        export_type="full"
        incremental="false"
    fi

    local status_file="pod_status_${export_type}_${timestamp}_${user_id}.csv"
    local details_file="optimization_details_${export_type}_${timestamp}_${user_id}.csv"
    local status_path="$export_dir/$status_file"
    local details_path="$export_dir/$details_file"

    log_info "开始导出优化数据 (${export_type})..."

    # 导出Pod状态摘要
    if export_pod_status_to_csv "$status_path" "$incremental"; then
        local status_count=$(tail -n +2 "$status_path" | wc -l | tr -d ' ')
        log_success "Pod状态数据导出成功: $status_count 条记录"
    else
        log_error "Pod状态数据导出失败"
        return 1
    fi

    # 导出优化详情
    if export_optimization_details_to_csv "$details_path" "$incremental"; then
        local details_count=$(tail -n +2 "$details_path" | wc -l | tr -d ' ')
        log_success "优化详情数据导出成功: $details_count 条记录"
    else
        log_error "优化详情数据导出失败"
        return 1
    fi

    # 记录同步日志
    record_sync_log "$status_file" "export" "$user_id"
    record_sync_log "$details_file" "export" "$user_id"

    # 返回导出的文件路径
    echo "$status_path"
    echo "$details_path"

    log_success "数据导出完成，文件保存在: $export_dir"
}

# 查找并添加相关的备份文件
add_related_backup_files() {
    local sync_timestamp="$1"
    local user_id="$2"

    # 查找所有备份目录（移除时间限制）
    local backup_dirs=()
    if [ -d "backups" ]; then
        # 查找所有备份目录，不限制时间（包含多种命名模式）
        while IFS= read -r -d '' backup_dir; do
            backup_dirs+=("$backup_dir")
        done < <(find backups/ -maxdepth 1 -type d \( -name "*backup*" -o -name "*bak*" -o -name "*_[0-9]*" \) -print0 2>/dev/null)

        if [ ${#backup_dirs[@]} -gt 0 ]; then
            log_info "发现 ${#backup_dirs[@]} 个相关备份目录"

            for backup_dir in "${backup_dirs[@]}"; do
                # 添加所有备份目录，不限制大小
                local backup_size=$(du -sm "$backup_dir" 2>/dev/null | cut -f1)
                git add "$backup_dir"
                log_info "已添加备份目录: $(basename "$backup_dir") (${backup_size}MB)"
            done
        else
            log_info "没有发现相关的备份目录"
        fi
    fi

    # 检查当前目录下的备份文件
    local current_dir_backups=$(find . -maxdepth 1 -name "*.bak*" -o -name "*backup*" -o -name "*.backup" 2>/dev/null)
    if [ -n "$current_dir_backups" ]; then
        echo "$current_dir_backups" | while read -r backup_file; do
            if [ -f "$backup_file" ]; then
                git add "$backup_file"
                log_info "已添加当前目录备份文件: $(basename "$backup_file")"
            fi
        done
    fi

    # 添加日志文件
    if [ -d "logs" ]; then
        # 查找所有日志文件，不限制时间
        local recent_logs=$(find logs/ -name "*.txt" 2>/dev/null)
        if [ -n "$recent_logs" ]; then
            echo "$recent_logs" | while read -r log_file; do
                git add "$log_file"
                log_info "已添加日志文件: $(basename "$log_file")"
            done
        fi
    fi

    # 添加scripts/lib/logs目录下的日志文件
    if [ -d "scripts/lib/logs" ]; then
        local script_logs=$(find scripts/lib/logs/ -name "*.txt" 2>/dev/null)
        if [ -n "$script_logs" ]; then
            echo "$script_logs" | while read -r log_file; do
                git add "$log_file"
                log_info "已添加脚本日志文件: $(basename "$log_file")"
            done
        fi
    fi
}

# Git推送功能
push_to_remote() {
    local export_files=("$@")

    if [ ${#export_files[@]} -eq 0 ]; then
        log_error "没有文件需要推送"
        return 1
    fi

    log_info "开始推送优化数据到远程仓库..."

    # 检查Git状态
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是Git仓库"
        return 1
    fi

    # 添加CSV文件到Git（使用相对路径）
    for file in "${export_files[@]}"; do
        if [ -f "$file" ]; then
            # 获取相对于当前目录的路径
            local relative_path="${file#$(pwd)/}"
            git add "$relative_path"
            log_info "已添加CSV文件: $(basename "$file")"
        fi
    done

    # 添加相关的备份文件和日志文件
    local user_id=$(get_user_id)
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    add_related_backup_files "$timestamp" "$user_id"

    # 检查是否有变更
    if git diff --cached --quiet; then
        log_warning "没有新的变更需要提交"
        return 0
    fi

    # 提交变更
    local user_id=$(get_user_id)
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local commit_msg="Pod优化数据同步 (含备份) - $user_id - $timestamp"

    if git commit -m "$commit_msg"; then
        log_success "提交成功: $commit_msg"
    else
        log_error "提交失败"
        return 1
    fi

    # 推送到远程
    local current_branch=$(git branch --show-current)
    if git push origin "$current_branch"; then
        log_success "推送成功到远程分支: $current_branch"
        return 0
    else
        log_error "推送失败，可能需要先拉取远程变更"
        return 1
    fi
}

# 完整的提交优化功能（功能9）
submit_optimization() {
    local incremental="${1:-true}"
    local force_full="${2:-false}"

    log_info "=== 功能9：提交优化↑ (导出CSV、备份文件并推送到远端) ==="
    echo ""

    # 导出数据
    local export_result
    export_result=$(export_optimization_data "$incremental" "$force_full")

    if [ $? -ne 0 ]; then
        log_error "数据导出失败"
        return 1
    fi

    # 解析导出的文件路径
    local export_files=()
    while IFS= read -r line; do
        if [ -f "$line" ]; then
            export_files+=("$line")
        fi
    done <<< "$export_result"

    if [ ${#export_files[@]} -eq 0 ]; then
        log_error "没有找到导出的文件"
        return 1
    fi

    # 数据完整性验证
    log_info "验证导出数据完整性..."
    for file in "${export_files[@]}"; do
        if [ ! -s "$file" ]; then
            log_error "文件为空或不存在: $file"
            return 1
        fi

        # 验证CSV格式
        local line_count=$(wc -l < "$file")
        if [ "$line_count" -lt 1 ]; then
            log_error "CSV文件格式错误: $file"
            return 1
        fi

        log_success "文件验证通过: $(basename "$file") ($line_count 行)"
    done

    # 推送到远程
    if push_to_remote "${export_files[@]}"; then
        log_success "🎉 优化数据和备份文件提交完成！"
        echo ""
        echo "已导出并推送的CSV文件："
        for file in "${export_files[@]}"; do
            echo "  - $(basename "$file")"
        done
        echo ""
        echo "同时提交了相关的备份文件和日志文件"
        return 0
    else
        log_error "推送失败，但数据已导出到本地"
        return 1
    fi
}

# 导入Pod状态CSV数据
import_pod_status_csv() {
    local csv_file="$1"
    local user_id=$(get_user_id)

    if [ ! -f "$csv_file" ]; then
        log_error "CSV文件不存在: $csv_file"
        return 1
    fi

    log_info "导入Pod状态数据: $(basename "$csv_file")"

    # 验证CSV格式
    local header=$(head -n 1 "$csv_file")
    if [[ "$header" != "$CSV_STATUS_HEADER" ]]; then
        log_warning "CSV格式可能不匹配，尝试兼容导入"
    fi

    # 逐行处理CSV数据（跳过头部）
    local imported_count=0
    local skipped_count=0

    tail -n +2 "$csv_file" | while IFS=',' read -r format_version pod_name orig_png orig_jpg orig_gif orig_total orig_size \
                                                    curr_png curr_jpg curr_gif curr_total curr_size \
                                                    deleted compressed heic space_saved last_opt_time last_scan_time \
                                                    opt_types csv_user_id sync_timestamp; do

        # 清理引号
        pod_name=$(echo "$pod_name" | sed 's/^"//;s/"$//')
        csv_user_id=$(echo "$csv_user_id" | sed 's/^"//;s/"$//')
        opt_types=$(echo "$opt_types" | sed 's/^"//;s/"$//')

        # 检查是否已存在该Pod的记录
        local exists=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM pod_status_summary WHERE pod_name='$pod_name';" 2>/dev/null || echo "0")

        if [ "$exists" -gt 0 ]; then
            # 更新现有记录（智能合并）
            sqlite3 "$DB_FILE" << EOF
UPDATE pod_status_summary SET
    original_png_count = CASE WHEN original_png_count = 0 THEN $orig_png ELSE original_png_count END,
    original_jpg_count = CASE WHEN original_jpg_count = 0 THEN $orig_jpg ELSE original_jpg_count END,
    original_gif_count = CASE WHEN original_gif_count = 0 THEN $orig_gif ELSE original_gif_count END,
    original_total_count = CASE WHEN original_total_count = 0 THEN $orig_total ELSE original_total_count END,
    original_total_size = CASE WHEN original_total_size = 0 THEN $orig_size ELSE original_total_size END,
    current_png_count = $curr_png,
    current_jpg_count = $curr_jpg,
    current_gif_count = $curr_gif,
    current_total_count = $curr_total,
    current_total_size = $curr_size,
    deleted_count = CASE WHEN deleted_count > $deleted THEN deleted_count ELSE $deleted END,
    compressed_count = CASE WHEN compressed_count > $compressed THEN compressed_count ELSE $compressed END,
    heic_converted_count = CASE WHEN heic_converted_count > $heic THEN heic_converted_count ELSE $heic END,
    total_space_saved = CASE WHEN total_space_saved > $space_saved THEN total_space_saved ELSE $space_saved END,
    last_optimization_time = CASE
        WHEN '$last_opt_time' > COALESCE(last_optimization_time, '') THEN '$last_opt_time'
        ELSE last_optimization_time
    END,
    last_scan_time = CASE
        WHEN '$last_scan_time' > COALESCE(last_scan_time, '') THEN '$last_scan_time'
        ELSE last_scan_time
    END,
    optimization_types = CASE
        WHEN '$opt_types' != '' AND optimization_types NOT LIKE '%$opt_types%'
        THEN optimization_types || ',' || '$opt_types'
        ELSE optimization_types
    END
WHERE pod_name = '$pod_name';
EOF
            skipped_count=$((skipped_count + 1))
        else
            # 插入新记录
            sqlite3 "$DB_FILE" << EOF
INSERT INTO pod_status_summary (
    pod_name, original_png_count, original_jpg_count, original_gif_count, original_total_count, original_total_size,
    current_png_count, current_jpg_count, current_gif_count, current_total_count, current_total_size,
    deleted_count, compressed_count, heic_converted_count, total_space_saved,
    last_optimization_time, last_scan_time, optimization_types
) VALUES (
    '$pod_name', $orig_png, $orig_jpg, $orig_gif, $orig_total, $orig_size,
    $curr_png, $curr_jpg, $curr_gif, $curr_total, $curr_size,
    $deleted, $compressed, $heic, $space_saved,
    '$last_opt_time', '$last_scan_time', '$opt_types'
);
EOF
            imported_count=$((imported_count + 1))
        fi
    done

    log_success "Pod状态数据导入完成: 新增 $imported_count 条，更新 $skipped_count 条"
}

# 导入优化详情CSV数据
import_optimization_details_csv() {
    local csv_file="$1"
    local user_id=$(get_user_id)

    if [ ! -f "$csv_file" ]; then
        log_error "CSV文件不存在: $csv_file"
        return 1
    fi

    log_info "导入优化详情数据: $(basename "$csv_file")"

    # 验证CSV格式
    local header=$(head -n 1 "$csv_file")
    if [[ "$header" != "$CSV_DETAILS_HEADER" ]]; then
        log_warning "CSV格式可能不匹配，尝试兼容导入"
    fi

    # 逐行处理CSV数据（跳过头部）
    local imported_count=0
    local skipped_count=0

    tail -n +2 "$csv_file" | while IFS=',' read -r format_version pod_name opt_id operation_type files_affected \
                                                    space_saved compression_ratio operation_time details \
                                                    csv_user_id sync_timestamp; do

        # 清理引号
        pod_name=$(echo "$pod_name" | sed 's/^"//;s/"$//')
        operation_type=$(echo "$operation_type" | sed 's/^"//;s/"$//')
        details=$(echo "$details" | sed 's/^"//;s/"$//')
        csv_user_id=$(echo "$csv_user_id" | sed 's/^"//;s/"$//')

        # 检查是否已存在相同的记录（基于pod_name, operation_type, operation_time）
        local exists=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimization_details WHERE pod_name='$pod_name' AND operation_type='$operation_type' AND operation_time='$operation_time';" 2>/dev/null || echo "0")

        if [ "$exists" -eq 0 ]; then
            # 插入新记录
            sqlite3 "$DB_FILE" << EOF
INSERT INTO optimization_details (
    pod_name, optimization_id, operation_type, files_affected, space_saved,
    compression_ratio, operation_time, details
) VALUES (
    '$pod_name', $opt_id, '$operation_type', $files_affected, $space_saved,
    $compression_ratio, '$operation_time', '$details'
);
EOF
            imported_count=$((imported_count + 1))
        else
            skipped_count=$((skipped_count + 1))
        fi
    done

    log_success "优化详情数据导入完成: 新增 $imported_count 条，跳过重复 $skipped_count 条"
}

# Git拉取功能
pull_from_remote() {
    log_info "从远程仓库拉取最新数据..."

    # 检查Git状态
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是Git仓库"
        return 1
    fi

    # 获取当前分支
    local current_branch=$(git branch --show-current)

    # 拉取远程变更
    if git pull origin "$current_branch"; then
        log_success "远程数据拉取成功"
        return 0
    else
        log_error "拉取失败，可能存在冲突"
        return 1
    fi
}

# 查找并处理CSV文件
find_and_process_csv_files() {
    local import_dir="$DB_DIR/sync"
    local processed_count=0

    # 查找所有CSV文件
    local csv_files=()
    while IFS= read -r -d '' file; do
        csv_files+=("$file")
    done < <(find "$import_dir" -name "*.csv" -type f -print0 2>/dev/null)

    if [ ${#csv_files[@]} -eq 0 ]; then
        log_warning "没有找到CSV文件进行导入"
        return 0
    fi

    log_info "找到 ${#csv_files[@]} 个CSV文件"

    # 分类处理CSV文件
    for csv_file in "${csv_files[@]}"; do
        local filename=$(basename "$csv_file")

        # 检查是否已处理过
        local sync_log_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM sync_log WHERE sync_file='$filename' AND status='import';" 2>/dev/null || echo "0")
        if [ "$sync_log_count" -gt 0 ]; then
            log_info "跳过已处理的文件: $filename"
            continue
        fi

        # 根据文件名判断类型
        if [[ "$filename" == *"pod_status"* ]]; then
            if import_pod_status_csv "$csv_file"; then
                record_sync_log "$filename" "import" "$(get_user_id)"
                processed_count=$((processed_count + 1))
            fi
        elif [[ "$filename" == *"optimization_details"* ]]; then
            if import_optimization_details_csv "$csv_file"; then
                record_sync_log "$filename" "import" "$(get_user_id)"
                processed_count=$((processed_count + 1))
            fi
        else
            log_warning "未知的CSV文件格式: $filename"
        fi
    done

    log_success "处理完成，共导入 $processed_count 个文件"
}

# 完整的同步优化功能（功能10）
sync_optimization() {
    log_info "=== 功能10：同步优化↓ (拉取远端CSV并合并到本地) ==="
    echo ""

    ensure_sync_dir

    # 拉取远程数据
    if ! pull_from_remote; then
        log_error "远程数据拉取失败"
        return 1
    fi

    # 处理CSV文件
    if ! find_and_process_csv_files; then
        log_error "CSV文件处理失败"
        return 1
    fi

    # 数据一致性检查
    log_info "执行数据一致性检查..."

    # 检查pod_status_summary表的数据完整性
    local status_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM pod_status_summary;" 2>/dev/null || echo "0")
    local details_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimization_details;" 2>/dev/null || echo "0")

    log_success "数据一致性检查完成"
    log_info "Pod状态记录: $status_count 条"
    log_info "优化详情记录: $details_count 条"

    # 清理重复数据
    log_info "清理重复数据..."
    sqlite3 "$DB_FILE" << 'EOF'
-- 清理重复的优化详情记录
DELETE FROM optimization_details
WHERE id NOT IN (
    SELECT MIN(id)
    FROM optimization_details
    GROUP BY pod_name, operation_type, operation_time
);
EOF

    log_success "🎉 优化数据同步完成！"
    echo ""
    echo "同步统计："
    echo "  - Pod状态记录: $status_count 条"
    echo "  - 优化详情记录: $details_count 条"

    return 0
}

# 兼容性函数：支持旧版本的导出格式
export_db_to_csv() {
    log_warning "使用旧版导出格式，建议使用 export_optimization_data"
    export_optimization_data "true" "false"
}

# 兼容性函数：支持旧版本的导入格式
import_csv_to_db() {
    log_warning "使用旧版导入格式，建议使用 sync_optimization"
    sync_optimization
}

# 清理旧的同步文件（保留最近30天）
cleanup_old_sync_files() {
    local sync_dir="$DB_DIR/sync"
    local days_to_keep=30

    if [ -d "$sync_dir" ]; then
        # 删除30天前的导出文件
        find "$sync_dir/export" -name "*.csv" -type f -mtime +$days_to_keep -delete 2>/dev/null || true
        find "$sync_dir/import" -name "*.csv" -type f -mtime +$days_to_keep -delete 2>/dev/null || true

        # 清理对应的日志记录
        local cutoff_date=$(date -d "$days_to_keep days ago" +%Y-%m-%d 2>/dev/null || date -v-${days_to_keep}d +%Y-%m-%d)
        sqlite3 "$DB_FILE" "DELETE FROM sync_log WHERE sync_time < '$cutoff_date';" 2>/dev/null || true

        log_info "已清理30天前的同步文件"
    fi
}

# 兼容性函数：旧版本的提交功能
submit_optimization_updates() {
    log_warning "使用旧版提交功能，建议使用 submit_optimization"
    submit_optimization "true" "false"
}

# 兼容性函数：旧版本的同步功能
sync_optimization_updates() {
    log_warning "使用旧版同步功能，建议使用 sync_optimization"
    sync_optimization
}

