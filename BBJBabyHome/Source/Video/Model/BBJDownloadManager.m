//
//  BBJDownloadManager.m
//  IMYBabyJournal
//
//  Created by angBiu on 2019/6/27.
//  Copyright © 2019 MeetYou. All rights reserved.
//

#import "BBJDownloadManager.h"

@interface BBJDownloadManager () <NSURLSessionDataDelegate>

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, assign) long long totalSize;
@property (nonatomic, assign) long long tmpSize;
@property (nonatomic, strong) NSOutputStream *outputStream;
@property (nonatomic, copy) NSString *tempPath;
@property (nonatomic, copy) NSString *targetPath;
@property (nonatomic, strong) NSURLSessionDataTask *dataTask;

@property (nonatomic, copy) BBJDownloadProgressBlock downloadProgressBlock;
@property (nonatomic, copy) BBJDownloadCompletionHandler completionHandler;

@end

@implementation BBJDownloadManager

+ (instancetype)manager {
    return [[self alloc] init];
}

- (void)downloadTaskWithUrlString:(nonnull NSString *)UrlString
                       targetPath:(nonnull NSString *)targetPath
                         progress:(nonnull BBJDownloadProgressBlock)downloadProgressBlock
                completionHandler:(nonnull BBJDownloadCompletionHandler)completionHandler {
    self.targetPath = targetPath;
    self.downloadProgressBlock = downloadProgressBlock;
    self.completionHandler = completionHandler;
    [self downLoader:[NSURL URLWithString:UrlString]];
}

- (void)cancelCurrentTask {
    [self.dataTask cancel];
    [self.session invalidateAndCancel];
    self.session = nil;
    self.downloadProgressBlock = nil;
    self.completionHandler = nil;
    [self cleanTempFile];
}

#pragma mark - NSURLSessionDataDelegate

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSHTTPURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    //本地缓存
    _totalSize = [response.allHeaderFields[@"Content-Length"]longLongValue];
    NSString *contentRangeStr = response.allHeaderFields[@"content-range"];
    if (contentRangeStr.length != 0) {
        _totalSize  =   [[contentRangeStr componentsSeparatedByString:@"/"].lastObject longLongValue];
    }
    if (_tmpSize == _totalSize) {
        [self moveFile:self.tempPath toPath:self.targetPath];
        completionHandler(NSURLSessionResponseCancel);
        return;
    }
    if (_tmpSize > _totalSize) {
        [self cleanTempFile];
        completionHandler(NSURLSessionResponseCancel);
        [self downLoader:response.URL];
        return;
    }
    //本地大小 < 总大小  ==>>  从本地大小开始下载.
    self.outputStream = [NSOutputStream outputStreamToFileAtPath:self.tempPath append:YES];
    [self.outputStream open];
    completionHandler(NSURLSessionResponseAllow);
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
    // 当前以及下载的大小
    _tmpSize += data.length;
    CGFloat progress = 1.0 * _tmpSize / _totalSize;
    if (self.downloadProgressBlock) {
        self.downloadProgressBlock(progress);
    }
    
    [self.outputStream write:data.bytes maxLength:data.length];
}

#import "IMYNetworkingTaskMetrics.h"
#import "NSError+IMYNetworking.h"
#import <objc/runtime.h>

static const void *IMYTaskMetricsKey = &IMYTaskMetricsKey;

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics API_AVAILABLE(ios(10.0)) {
    IMYNetworkingTaskMetrics *taskMetrics = [[IMYNetworkingTaskMetrics alloc] initWithTaskMetrics:metrics];
    objc_setAssociatedObject(task, IMYTaskMetricsKey, taskMetrics, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    if (error) {
        id<IMYNetworkingTaskMetricsProtocol> taskMetrics = objc_getAssociatedObject(task, IMYTaskMetricsKey);
        if (taskMetrics) {
            [error setImy_taskMetrics:taskMetrics];
        }
    }
    if (error == nil) {
        [self moveFile:self.tempPath toPath:self.targetPath];
    }
    if (self.completionHandler) {
        self.completionHandler(task.response, self.targetPath, error);
    }
    [self.outputStream close];
}

#pragma mark - Private

- (void)downLoader:(NSURL*)url {
    [self.dataTask resume];
    NSString *fileName = url.lastPathComponent;
    self.tempPath = [NSTemporaryDirectory() stringByAppendingPathComponent:fileName];
    //检测，临时文件是否存在
    if (![self fileExists:self.tempPath]) {
        // 从0字节开始请求资源
        [self downLoadWithURL:url offset:0];
        return;
    }
    // 获取本地大小
    _tmpSize = [self fileSize:self.tempPath];
    [self downLoadWithURL:url offset:_tmpSize];
}
/**
 根据开始字节去请求资源
 
 @param url url
 @param offset 开始字节
 */
- (void)downLoadWithURL:(NSURL *)url offset:(long long)offset {
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:0];
    [request setValue:[NSString stringWithFormat:@"bytes=%lld-",offset] forHTTPHeaderField:@"Range"];
    // session 分配的task,默认挂起状态
    self.dataTask = [self.session dataTaskWithRequest:request];
    [self.dataTask resume];
}

- (void)cleanTempFile {
    [self removeFile:self.tempPath];
}

#pragma mark - FileManager

- (BOOL)fileExists:(NSString *)filePath {
    if (filePath.length == 0) {
        return NO;
    }
    return [[NSFileManager defaultManager]fileExistsAtPath:filePath];
}

- (long long)fileSize:(NSString *)filePath {
    if (![self fileExists:filePath]) {
        return 0;
    }
    NSDictionary *fileInfo = [[NSFileManager defaultManager]attributesOfItemAtPath:filePath error:nil];
    return [fileInfo[NSFileSize]longLongValue];
}

- (void)moveFile:(NSString *)fromPath toPath:(NSString *)toPath {
    if (![self fileSize:fromPath]) {
        return;
    }
    [[NSFileManager defaultManager]moveItemAtPath:fromPath toPath:toPath error:nil];
}

- (void)removeFile:(NSString *)filePath {
    [[NSFileManager defaultManager]removeItemAtPath:filePath error:nil];
}

-(NSURLSession *)session {
    if (!_session) {
        NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
        _session = [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:[NSOperationQueue mainQueue]];
    }
    return _session;
}

@end
