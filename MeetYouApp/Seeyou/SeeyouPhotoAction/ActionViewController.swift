//
//  ActionViewController.swift
//  SeeyouPhotoAction
//
//  Created by meetyou on 2025/9/2.
//  Copyright © 2025 linggan. All rights reserved.
//

import UIKit
import MobileCoreServices
import UniformTypeIdentifiers

class ActionViewController: UIViewController {
    
    var shared: Bool = false
    
    var jumpFinish: Bool = false

    lazy var share: IMYSharedExtension = {
       return IMYSharedExtension(extensionContext: self.extensionContext)
    }()
    
    @IBOutlet weak var imageView: UIImageView!
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if (!shared) {
            shared = true
            share.onlyhHandleShareAndJump { [weak self] in
                self?.jumpFinish = true
                self?.jumpToMainApp()
            }
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        
        
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if jumpFinish {
            self.extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
        }
    }

    @IBAction func done() {
        // Return any edited content to the host app.
        // This template doesn't do anything, so we just echo the passed in items.
        self.extensionContext!.completeRequest(returningItems: self.extensionContext!.inputItems, completionHandler: nil)
    }
    
    private func jumpToMainApp() {
        DispatchQueue.main.async {
            guard let url = URL(string: IMYSharedExtension.kRoute) else { return }
            var responder: UIResponder? = self as UIResponder
            let selector = #selector(UIApplication.open(_:options:completionHandler:))
            
            while responder != nil {
                if responder?.responds(to: selector) == true {
                    responder?.perform(selector, with: url, with: nil)
                    break
                }
                responder = responder?.next
            }
            self.extensionContext?.completeRequest(returningItems: [], completionHandler: nil)
        }
    }

}
