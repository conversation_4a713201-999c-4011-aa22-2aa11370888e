//
//  IMYSharedExtension.swift
//  SharedExtension
//
//  Created by meetyou on 2025/6/11.
//

import Foundation
import UIKit
import Intents
import UniformTypeIdentifiers

let appGroup = "group.com.meiyou"

enum IMYFileType {
    case image
    case video
    
    var publicType: String {
        switch self {
        case .image:
            return "public.image"
        case .video:
            return "public.movie"
        }
    }
}

@available(iOSApplicationExtension 13.0, *)
public class IMYSharedExtension {
    public static let kRoute = "meetyouopen:///shared/extension/baby"
    
    public var extensionContext: NSExtensionContext?
    
    public init(extensionContext: NSExtensionContext? = nil) {
        self.extensionContext = extensionContext
    }
    
    public func handleShareAndJump(completeBlock: (()->())? = nil) {
        // 清理文件夹
        clearSharedContainerContents()
        
        let block = {
            if let intent = self.extensionContext?.intent as? INSendMessageIntent {
                UserDefaults.intentId = intent.conversationIdentifier
            } else {
                UserDefaults.intentId = nil
            }
        }
        
        guard let inputItems = extensionContext?.inputItems as? [NSExtensionItem] else {
            extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
            block()
            completeBlock?()
            return
        }
        
        let group = dispatch_group_t()
        
        for item in inputItems {
            for provider in item.attachments ?? [] {
                if provider.hasItemConformingToTypeIdentifier(IMYFileType.image.publicType) {
                    group.enter()
                    provider.loadItem(forTypeIdentifier: IMYFileType.image.publicType) { [weak self] (data, error) in
                        guard let self = self else {
                            group.leave()
                            return
                        }
                        
                        if let url = data as? URL {
                            self.saveToSharedContainer(url: url, type: .image)
                        } else if let image = data as? UIImage {
                            self.saveImageToSharedContainer(image: image)
                        }
                        group.leave()
                    }
                } else if provider.hasItemConformingToTypeIdentifier(IMYFileType.video.publicType) {
                    group.enter()
                    provider.loadItem(forTypeIdentifier: IMYFileType.video.publicType) { [weak self] (data, error) in
                        if let url = data as? URL {
                            self?.saveToSharedContainer(url: url, type: .video)
                        }
                        group.leave()
                    }
                }
            }
        }
        
        group.notify(queue: .main) {
            block()
            completeBlock?()
        }
    }
    
    public func onlyhHandleShareAndJump(completeBlock: (()->())? = nil) {
        // 清理文件夹
        clearSharedContainerContents()
        
        guard let inputItems = extensionContext?.inputItems as? [NSExtensionItem] else {
            extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
            completeBlock?()
            return
        }
        
        let group = dispatch_group_t()
        
        for item in inputItems {
            for provider in item.attachments ?? [] {
                if provider.hasItemConformingToTypeIdentifier(IMYFileType.image.publicType) {
                    group.enter()
                    provider.loadItem(forTypeIdentifier: IMYFileType.image.publicType) { [weak self] (data, error) in
                        guard let self = self else {
                            group.leave()
                            return
                        }
                        
                        if let url = data as? URL {
                            self.saveToSharedContainer(url: url, type: .image)
                        } else if let image = data as? UIImage {
                            self.saveImageToSharedContainer(image: image)
                        }
                        group.leave()
                    }
                } else if provider.hasItemConformingToTypeIdentifier(IMYFileType.video.publicType) {
                    group.enter()
                    provider.loadItem(forTypeIdentifier: IMYFileType.video.publicType) { [weak self] (data, error) in
                        if let url = data as? URL {
                            self?.saveToSharedContainer(url: url, type: .video)
                        }
                        group.leave()
                    }
                }
            }
        }
        
        group.notify(queue: .main) {
            completeBlock?()
        }
    }
    
    func clearSharedContainerContents() {
        guard let sharedURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroup) else { return }
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: sharedURL,
                includingPropertiesForKeys: nil,
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                try FileManager.default.removeItem(at: fileURL)
            }
        } catch {
            
        }
    }
    
    @discardableResult
    private func saveToSharedContainer(url: URL, type: IMYFileType) -> Bool {
        let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroup)
        let fileName = url.lastPathComponent
        guard let destURL = sharedContainer?.appendingPathComponent("\(fileName)") else {
            return false
        }
        
        do {
            try FileManager.default.copyItem(at: url, to: destURL)
            let dic = try FileManager.default.attributesOfItem(atPath: url.absoluteString)
            try FileManager.default.setAttributes(dic, ofItemAtPath: destURL.absoluteString)
            
            return true
        } catch {
            return false
        }
    }
    
    @discardableResult
    private func saveImageToSharedContainer(image: UIImage) -> Bool {
        guard let data = image.jpegData(compressionQuality: 0.98) else { return false }
        let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroup)
        let fileName = "\(UUID().uuidString).jpg"
        let destURL = sharedContainer?.appendingPathComponent(fileName)
        do {
            if let destURL {
                try data.write(to: destURL)
            }
            return true
        } catch {
            return false
        }
    }
    
}

extension UserDefaults {
    
    // 联系人id
    @IMYUserDefaultsWrapper(key: "group.com.meiyou.sharedMedias.intent.id", stroage: UserDefaults(suiteName: appGroup) ?? .standard)
    static var intentId: String?
}

@propertyWrapper
public struct IMYUserDefaultsWrapper<Value> {
    let key: String
    let defaultValue: Value?
    var stroage: UserDefaults = .standard
    
    public var wrappedValue: Value? {
        get {
            let value = stroage.value(forKey: key) as? Value
            return value ?? defaultValue
        }
        
        set {
            
            if newValue == nil {
                stroage.removeObject(forKey: key)
            } else {
                stroage.setValue(newValue, forKey: key)
            }
            stroage.synchronize()
        }
    }
    
    init(key: String, defaultValue: Value? = nil, stroage: UserDefaults = .standard) {
        self.key = key
        self.defaultValue = defaultValue
        self.stroage = stroage
    }
}



