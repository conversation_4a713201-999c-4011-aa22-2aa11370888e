//
//  IMYBaByFeed02WidgetAppItents.swift
//  SeeyouWidgetExtension
//
//  Created by meetyou on 2025/2/19.
//  Copyright © 2025 linggan. All rights reserved.
//

import WidgetKit
import SwiftUI
import SeeyouWidget
import AppIntents
import SeeyouWidgetService

@available(iOSApplicationExtension 17.0, *)
public struct IMYBaByFeed02WidgetAppItents: AppIntent {
    
    var entry: IMYBabyFeed02WidgetSimpleEntry?  //这里就获取到了传递过来的数据
    public static var isDiscoverable: Bool = false

    public static var title: LocalizedStringResource = "IMYBaByFeed02WidgetAppItents Refresh Widget"
    
    public init() {
    }
    
    public init(entry: IMYBabyFeed02WidgetSimpleEntry? = nil) {
        self.entry = entry
    }
        
    public func perform() async throws -> some IntentResult {
        
        print("🐶 perform")
        
        // 触发 Widget 刷新
//        WidgetCenter.shared.reloadAllTimelines()
        
        return .result()
    }
}

@available(iOSApplicationExtension 17.0, *)
struct IMYBaByFeed02WidgetSleepAppItents: AppIntent {
    // 标题
    static var title: LocalizedStringResource = "喂养记录"
    // 描述
    static var description: IntentDescription = IntentDescription("便捷查看宝宝的喂养记录")
    static var isDiscoverable: Bool = false

    // 定义一个变量value
    @Parameter(title: "common_baby_id")
    var common_baby_id: Int
    
    init() { }
    init(common_baby_id: Int) {
       self.common_baby_id = common_baby_id
    }
    func perform() async throws -> some IntentResult {
        let floatModel = WidgetBabyFeedHelper.getFloationWindowModel(eventId: 2, common_baby_id: common_baby_id, recordType: 4)
        var isPauseLeft = true;
        if floatModel?.countingType == .BBJFloatingWindowModelStatePause { //暂停 To 开始
            isPauseLeft = false;
        }
        WidgetBabyFeedHelper.saveFloationWindowModelStatus(eventId: 2, common_baby_id: common_baby_id, recordType: 4, isPauseLeft: isPauseLeft, isPauseRight: true, isClickRight: false,reloadWidget: true)
        print("lgw 02 睡觉点击")
        return .result()
    }
}

@available(iOSApplicationExtension 17.0, *)
struct IMYBaByFeed02WidgetLeftAppItents: AppIntent {
    // 标题
    static var title: LocalizedStringResource = "喂养记录"
    // 描述
    static var description: IntentDescription = IntentDescription("便捷查看宝宝的喂养记录")
    static var isDiscoverable: Bool = false

    // 定义一个变量value
    @Parameter(title: "common_baby_id")
    var common_baby_id: Int
    
    init() { }
    init(common_baby_id: Int) {
       self.common_baby_id = common_baby_id
    }
    func perform() async throws -> some IntentResult {
        let floatModel = WidgetBabyFeedHelper.getFloationWindowModel(eventId: 1, common_baby_id: common_baby_id, recordType: 1)
        var isPauseLeft = true;
        var isPauseRight = true;
        if floatModel?.countingType != .BBJFloatingWindowModelStatePlay { //暂停 To 开始
            isPauseLeft = false;
            isPauseRight = true;
        } else { //进行中
            let countingPos = floatModel?.countingPos; // 哺乳，左1右2
            if countingPos == 1 { //如果之前 就是在左边计时器 ing
                isPauseLeft = true;
                isPauseRight = true;
            }else{ ////如果之前 就是在右边计时器
                isPauseRight = true;
                isPauseLeft = false;
            }
        }
        WidgetBabyFeedHelper.saveFloationWindowModelStatus(eventId: 1, common_baby_id: common_baby_id, recordType: 1, isPauseLeft: isPauseLeft, isPauseRight: isPauseRight, isClickRight: false, reloadWidget: true)
        IMYWidgetBabyFeedHelper.clickBiReportForBabyFeed(common_baby_id: common_baby_id, pageName: "IMYBabyFeed02Widget", kind: "weiyang_vip_01", index: 1)
        print("lgw 02 母乳 左边点击")
//        WidgetCenter.shared.reloadTimelines(ofKind: "weiyang_free_01")// 触发 Widget 刷新
//        WidgetCenter.shared.reloadTimelines(ofKind: "weiyang_vip_01")// 触发 Widget 刷新
        return .result()
    }
}


@available(iOSApplicationExtension 17.0, *)
struct IMYBaByFeed02WidgetRightAppItents: AppIntent {
    // 标题
    static var title: LocalizedStringResource = "喂养记录"
    // 描述
    static var description: IntentDescription = IntentDescription("便捷查看宝宝的喂养记录")
    static var isDiscoverable: Bool = false

    // 定义一个变量value
    @Parameter(title: "common_baby_id")
    var common_baby_id: Int
    
    init() { }
    init(common_baby_id: Int) {
       self.common_baby_id = common_baby_id
    }
    func perform() async throws -> some IntentResult {
        let floatModel = WidgetBabyFeedHelper.getFloationWindowModel(eventId: 1, common_baby_id: common_baby_id, recordType: 1)
        var isPauseLeft = true;
        var isPauseRight = true;
        if floatModel?.countingType != .BBJFloatingWindowModelStatePlay { //暂停 To 开始
            isPauseLeft = true;
            isPauseRight = false;
        } else { //进行中
            let countingPos = floatModel?.countingPos; // 哺乳，左1右2
            if countingPos == 1 { //如果之前 就是在左边计时器
                isPauseLeft = true;
                isPauseRight = false;
            }else{ ////如果之前 就是在右边计时器 ing
                isPauseRight = true;
                isPauseLeft = true;
            }
        }
        WidgetBabyFeedHelper.saveFloationWindowModelStatus(eventId: 1, common_baby_id: common_baby_id, recordType: 1, isPauseLeft: isPauseLeft, isPauseRight: isPauseRight, isClickRight: true, reloadWidget: true)
        IMYWidgetBabyFeedHelper.clickBiReportForBabyFeed(common_baby_id: common_baby_id, pageName: "IMYBabyFeed02Widget", kind: "weiyang_vip_01", index: 2)
        print("lgw 02 母乳 右边点击")
//        WidgetCenter.shared.reloadTimelines(ofKind: "weiyang_free_01")// 触发 Widget 刷新
//        WidgetCenter.shared.reloadTimelines(ofKind: "weiyang_vip_01")// 触发 Widget 刷新
        return .result()
    }
}


@available(iOSApplicationExtension 17.0, *)
struct IMYBaByFeed02WidgetSaveAppItents: AppIntent {
    // 标题
    static var title: LocalizedStringResource = "喂养记录"
    // 描述
    static var description: IntentDescription = IntentDescription("便捷查看宝宝的喂养记录")
    static var isDiscoverable: Bool = false

    // 定义一个变量value
    @Parameter(title: "common_baby_id")
    var common_baby_id: Int
    
    @Parameter(title: "recordType")
    var recordType: Int
    
    init() { }
    init(common_baby_id: Int = 0, recordType:Int = 0) {
       self.common_baby_id = common_baby_id
    }
    func perform() async throws -> some IntentResult {
        WidgetBabyFeedHelper.saveFloationWindowModelStatus(eventId: 2, common_baby_id: common_baby_id, recordType: recordType, isPauseLeft: true, isPauseRight: true, isClickRight: false,reloadWidget: false)
//        WidgetCenter.shared.reloadTimelines(ofKind: "weiyang_free_01")// 触发 Widget 刷新
        print("lgw 02  保存点击")
        return .result()
    }
}
