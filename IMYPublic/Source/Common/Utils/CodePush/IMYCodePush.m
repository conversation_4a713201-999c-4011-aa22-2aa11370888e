//
//  IMYCodePush.m
//  IMYPublic
//
//  Created by ljh on 2019/10/17.
//

#import "IMYCodePush.h"
#import "IMYCodePushAppInfo.h"
#import "IMYConfigsCenterProjectGroup+Private.h"
#import "IMYPublic.h"
#import <CommonCrypto/CommonCrypto.h>

@interface IMYCodePush()
@property (nonatomic, strong) NSMutableDictionary<NSString *, IMYCodePushAppInfo *> *appInfoMap;
@property (nonatomic, strong) NSMutableDictionary<NSString *, RACSignal *> *updatingInfoMap;
// 主线程中已更新的 AppInfo，可直接返回，提高在主线程加载速度
@property (nonatomic, strong) NSMutableDictionary<NSString *, IMYCodePushAppInfo *> *mainThreadAppInfoMap;
@property (nonatomic, strong) dispatch_queue_t ioQueue;
@property (nonatomic, strong) RACQueueScheduler *racScheduler;
// 从独立接口转到配置中心，需要指定已支持的 groupKeys
@property (nonatomic, strong) NSArray<NSString *> *groupKeys;
@end
 
@implementation IMYCodePush

IMY_KYLIN_FUNC_IDLE_ASYNC {
    // 预加载包
    [[IMYCodePush sharedInstance] prefetchCodeBundles];
}

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

+ (void)loadCodeWithAppKey:(NSString *)appKey
            completedBlock:(void (^)(NSString * _Nullable, NSError * _Nullable))completedBlock {
    [[self sharedInstance] _loadCodeWithAppKey:appKey
                                     localFast:NO
                                completedBlock:completedBlock];
}

+ (void)loadLocalFastCodeWithAppKey:(NSString *)appKey
                     completedBlock:(void (^)(NSString * _Nullable, NSError * _Nullable))completedBlock {
    [[self sharedInstance] _loadCodeWithAppKey:appKey
                                     localFast:YES
                                completedBlock:completedBlock];
}

+ (BOOL)hasLocalBundleWithAppKey:(NSString *)appKey {
    return [IMYCodePushAppInfo bundleInfoExistsAtAppKey:appKey];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.appInfoMap = [NSMutableDictionary dictionary];
        self.updatingInfoMap = [NSMutableDictionary dictionary];
        self.mainThreadAppInfoMap = [NSMutableDictionary dictionary];
        self.ioQueue = dispatch_queue_create("imy.codepush.queue", NULL);
        self.racScheduler = [[RACTargetQueueScheduler alloc] initWithName:@"imy.codepush.queue.rac" targetQueue:self.ioQueue];
        self.groupKeys = @[ @"dynamic_ui" ];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(30 * NSEC_PER_SEC)), self.ioQueue, ^{
            // 30s后，清理打开时间超过 x天 以上的包
            [self cleanExpiredCodeBundles];
        });
    }
    return self;
}

- (void)prefetchCodeBundles {
    IMYCConfigsProject *project = [[IMYConfigsCenter sharedInstance] projectForKey:@"web_template"];
    for (NSString *gkey in self.groupKeys) {
        [project fetchGroupForKey:gkey completedBlock:^(IMYCConfigsGroup * _Nullable group) {
            // 每次增加group都需要服务端发布代码，所以改用数组，客户端内部做多重遍历
            NSArray<NSDictionary *> *lists = [group arrayForKey:@"lists"];
            for (NSDictionary *config in lists) {
                NSString * const app_key = config[@"app_key"];
                NSString * const zip_url = config[@"zip_url"];
                if (!app_key.length || !zip_url.length) {
                    continue;
                }
                // prefetch 1：不预加载， 2：空闲阶段预加载
                const NSInteger prefetch = [config[@"prefetch"] integerValue];
                if (prefetch == 2) {
                    imy_asyncMainBlockInDefaultMode(0, ^{
                        [IMYCodePush loadCodeWithAppKey:app_key completedBlock:^(NSString * _Nullable directoryPath, NSError * _Nullable error) {
                            // 预加载完成
                        }];
                    });
                }
            }
        }];
    }
}

- (void)loadRemoteConfigWithAppKey:(NSString * const)appKey completedBlock:(void (^)(NSDictionary *))completedBlock {
    if (!appKey.length || !completedBlock) {
        NSAssert(NO, @"params invalid!");
        return;
    }
    BOOL isCallbacked = NO;
    IMYCConfigsProject *project = [[IMYConfigsCenter sharedInstance] projectForKey:@"web_template"];
    for (NSString *gkey in self.groupKeys) {
        IMYCConfigsGroup *group = [project groupForKey:gkey];
        NSArray<NSDictionary *> *lists = [group arrayForKey:@"lists"];
        for (NSDictionary *config in lists) {
            NSString * const app_key = config[@"app_key"];
            NSString * const zip_url = config[@"zip_url"];
            if (!app_key.length || !zip_url.length) {
                continue;
            }
            if ([app_key isEqualToString:appKey] && !isCallbacked) {
                isCallbacked = YES;
                completedBlock(config);
                break;
            }
        }
        if (isCallbacked) {
            break;
        }
    }
    if (!isCallbacked) {
        completedBlock(nil);
    }
}

/// 清理打开时间超过 x天 以上的包
- (void)cleanExpiredCodeBundles {
    [IMYCodePushAppInfo cleanWithExpiredBundles];
}

- (IMYCodePushAppInfo *)appInfoForAppKey:(NSString *)appKey {
    IMYCodePushAppInfo *appInfo = self.appInfoMap[appKey];
    if (!appInfo) {
        appInfo = [[IMYCodePushAppInfo alloc] initWithAppKey:appKey];
        self.appInfoMap[appKey] = appInfo;
    }
    return appInfo;
}

- (void)_loadCodeWithAppKey:(NSString *)appKey
                  localFast:(BOOL)localFast
             completedBlock:(void (^)(NSString * _Nullable, NSError * _Nullable))completedBlock {
    if (!appKey || !completedBlock) {
        NSAssert(NO, @"code push params invalid!");
        return;
    }
    // 如果是在主线程，并且相关数据已经是最新，则直接返回
    __block BOOL hasCallbacked = NO;
    if (NSThread.isMainThread) {
        IMYCodePushAppInfo *appInfo = self.mainThreadAppInfoMap[appKey];
        if (appInfo.isNewest && appInfo.outPath) {
            completedBlock(appInfo.outPath, nil);
            hasCallbacked = YES;
            return;
        }
        if (localFast && !appInfo) {
            // 本地优先, 并且无AppInfo数据时，判断是否存在 Bundle 资源
            appInfo = [self appInfoForAppKey:appKey];
            if (appInfo.outPath) {
                completedBlock(appInfo.outPath, nil);
                hasCallbacked = YES;
            }
        }
    }
    // 所有操作都在队列里面，保证数据不会错乱
    dispatch_async(self.ioQueue, ^{
        IMYCodePushAppInfo *appInfo = [self appInfoForAppKey:appKey];
        // 优先直接返回本地已有资源, 然后进行数据刷新
        if (!hasCallbacked && localFast && appInfo.outPath) {
            completedBlock(appInfo.outPath, nil);
            hasCallbacked = YES;
        }
        // 一个生命周期内只请求一次
        if (appInfo.isNewest && appInfo.outPath) {
            if (!hasCallbacked) {
                completedBlock(appInfo.outPath, nil);
            }
        } else {
            // 防止重复请求
            RACSignal *signal = self.updatingInfoMap[appKey];
            if (!signal) {
                signal = [self updateAppInfo:appInfo];
                self.updatingInfoMap[appKey] = signal;
            }
            [signal subscribeNext:^(id x) {
                if (!hasCallbacked) {
                    completedBlock(appInfo.outPath, nil);
                }
                [self.updatingInfoMap removeObjectForKey:appKey];
            } error:^(NSError *error) {
                if (!hasCallbacked) {
                    completedBlock(appInfo.outPath, error);
                }
                if (error.code != -1) {
                    [IMYGAEventHelper postWithPath:@"code_push_fails"
                                            params:@{
                        @"code" : @(error.code),
                        @"domain" : error.domain ?: @"",
                        @"reason" : error.localizedFailureReason ?: @""
                    } headers:nil completed:nil];
                }
                [self.updatingInfoMap removeObjectForKey:appKey];
            }];
        }
    });
}

- (RACSignal *)updateAppInfo:(IMYCodePushAppInfo *)appInfo {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        // 读取配置中心的值
        [self loadRemoteConfigWithAppKey:appInfo.appKey completedBlock:^(NSDictionary * const config) {
            // 是否已经回调?
            BOOL isCallbacked = NO;
            // 无配置
            NSString * const md5 = config[@"md5"];
            if (!md5.length) {
                [subscriber sendError:[NSError errorWithDomain:@"response md5 invalid!" code:-1 userInfo:nil]];
                isCallbacked = YES;
                return;
            }
            // 服务端没更新
            if ([md5 isEqualToString:appInfo.md5]) {
                [appInfo toNewestOne];
                [subscriber sendNext:appInfo];
                [subscriber sendCompleted];
                isCallbacked = YES;
                // 进行主线程赋值
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.mainThreadAppInfoMap[appInfo.appKey] = appInfo;
                });
                return;
            }
            
            // upgrade 1：静默更新，2：强制更新， 3：静默增量更新，4: 强制增量更新
            NSInteger const upgrade = [config[@"upgrade"] integerValue];
            
            // 静默更新，先返回旧的文件夹地址
            if ((upgrade == 1 || upgrade == 3) && appInfo.outPath) {
                [appInfo toNewestOne];
                [subscriber sendNext:appInfo];
                [subscriber sendCompleted];
                isCallbacked = YES;
            }
            
            // 自动清理的天数
            NSInteger const cleanDay = [config[@"clean_day"] integerValue];
            
            // 只是用来中转，用来执行后续的 then 操作
            RACSignal *signal = [RACSignal return:@YES];
            // 判断待下载的文件，是否已存在，已存在的直接返回成功
            NSString * const newOutPutPath = [appInfo.rootDir stringByAppendingPathComponent:md5];
            const BOOL hasExistsNewFile = [NSFileManager.defaultManager contentsOfDirectoryAtPath:newOutPutPath error:nil].count > 0;
            if (hasExistsNewFile == NO) {
                // 全量下载
                NSString * const full_url = config[@"zip_url"];
                signal = [signal then:^RACSignal *{
                    return [self downloadFullZipURL:full_url md5:md5 upgrade:upgrade toAppInfo:appInfo];
                }];
            }
            // 不管中间的过程，只处理结果
            [signal subscribeNext:^(id x) {
                // 修改后续使用的版本md5
                [appInfo toNewestOne];
                [appInfo updateCurrentMD5:md5 cleanDay:cleanDay];
                // 回调给外部
                if (!isCallbacked) {
                    [subscriber sendNext:appInfo];
                    [subscriber sendCompleted];
                }
                // 进行主线程赋值
                dispatch_async(dispatch_get_main_queue(), ^{
                    self.mainThreadAppInfoMap[appInfo.appKey] = appInfo;
                });
            } error:^(NSError *error) {
                if (!isCallbacked) {
                    [subscriber sendError:error];
                }
            }];
        }];
        // 不可取消
        return nil;
    }];
}

- (RACSignal *)downloadFullZipURL:(NSString * const)fullURL
                              md5:(NSString * const)md5
                          upgrade:(NSInteger const)upgrade
                        toAppInfo:(IMYCodePushAppInfo * const)appInfo {
    return [[RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        NSURL *downloadURL = [NSURL URLWithString:fullURL];
        if (!downloadURL) {
            // 创建下载 URL 失败
            [subscriber sendError:[NSError errorWithDomain:@"fullURL invalid!" code:-3 userInfo:nil]];
        } else {
            [IMYPublicServerRequest downloadTaskWithURL:downloadURL completionHandler:^(NSURL * _Nullable location, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                if (error != nil) {
                    [subscriber sendError:[NSError errorWithDomain:@"download full zip fails!" code:-2 userInfo:nil]];
                    return;
                }
                NSString *newOutPutPath = [appInfo.rootDir stringByAppendingPathComponent:md5];
                NSString *newZipPath = [newOutPutPath stringByAppendingPathExtension:@"zip"];
                
                NSFileManager *fileManager = NSFileManager.defaultManager;
                
                // 先删除对应路径文件，防止解压失败
                [fileManager removeItemAtPath:newZipPath error:nil];
                [fileManager removeItemAtPath:newOutPutPath error:nil];
                
                // copy zip包
                const BOOL copyZipOK = [fileManager moveItemAtPath:location.relativePath toPath:newZipPath error:nil];
                if (!copyZipOK) {
                    [subscriber sendError:[NSError errorWithDomain:@"copy full zip fails!" code:-3 userInfo:nil]];
                    return;
                }
                
                NSString *newZipMD5 = [NSString imy_md5ByFilePath:newZipPath];
                if (![newZipMD5 isEqualToString:md5]) {
                    // md5 不一致，删除已下载的 zip 包文件
                    [fileManager removeItemAtPath:newZipPath error:nil];
                    [subscriber sendError:[NSError errorWithDomain:@"full zip md5 invalid!" code:-4 userInfo:nil]];
                    return;
                }
                
                // 增量更新，需要copy本地bundle文件
                if (upgrade == 3 || upgrade == 4) {
                    NSString *localBundlePath = [[NSBundle mainBundle] pathForResource:appInfo.appKey ofType:@"bundle"];
                    BOOL copyBundleOK = NO;
                    if (localBundlePath.length > 0) {
                        copyBundleOK = [fileManager copyItemAtPath:localBundlePath toPath:newOutPutPath error:nil];
                    }
                    if (!copyBundleOK) {
                        [fileManager removeItemAtPath:newOutPutPath error:nil];
                        [subscriber sendError:[NSError errorWithDomain:@"copy local bundle fails!" code:-3 userInfo:nil]];
                        return;
                    }
                }
                
                // 直接尝试解压，并合并文件
                ZipArchive *zipArchive = [[ZipArchive alloc] initWithFileManager:fileManager];
                [zipArchive UnzipOpenFile:newZipPath];
                const BOOL isOK = [zipArchive UnzipFileTo:newOutPutPath overWrite:YES];
                [zipArchive UnzipCloseFile];
                const BOOL hasExistsNewFile = [fileManager contentsOfDirectoryAtPath:newOutPutPath error:nil].count > 0;
                if (isOK && hasExistsNewFile) {
                    // 删除zip包
                    [fileManager removeItemAtPath:newZipPath error:nil];
                    // 解压成功
                    [subscriber sendNext:@1];
                    [subscriber sendCompleted];
                } else {
                    // 解压失败：删除zip包和解压后的文件
                    [fileManager removeItemAtPath:newZipPath error:nil];
                    [fileManager removeItemAtPath:newOutPutPath error:nil];
                    [subscriber sendError:[NSError errorWithDomain:@"unzip full zip fails!" code:-5 userInfo:nil]];
                }
            }];
        }
        return nil;
    }] deliverOn:self.racScheduler];
}

@end

