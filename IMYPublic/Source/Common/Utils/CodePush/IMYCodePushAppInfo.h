//
//  IMYCodePushAppInfo.h
//  IMYPublic
//
//  Created by ljh on 2019/10/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYCodePushAppInfo : NSObject

- (instancetype)initWithAppKey:(NSString *)appKey;

// 模块key
@property (nonatomic, copy, readonly) NSString *appKey;

// 根目录地址
@property (nonatomic, copy, readonly) NSString *rootDir;

// zip包解压后地址
@property (nonatomic, copy, nullable, readonly) NSString *outPath;

// 当前版本
@property (nonatomic, copy, nullable, readonly) NSString *md5;

// 是否最新的数据, 表示已请求过
@property (nonatomic, assign, readonly) BOOL isNewest;

// 标识已是最新数据
- (void)toNewestOne;

// 修改当前版本值
- (void)updateCurrentMD5:(NSString *)md5 cleanDay:(NSUInteger)cleanDay;

// 本地是否已经存在对应包
+ (BOOL)bundleInfoExistsAtAppKey:(NSString *)appKey;

// 清理打开时间超过 x天 以上的包
+ (void)cleanWithExpiredBundles;

@end

NS_ASSUME_NONNULL_END
