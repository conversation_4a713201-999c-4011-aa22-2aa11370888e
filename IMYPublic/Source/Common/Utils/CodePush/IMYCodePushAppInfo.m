//
//  IMYCodePushAppInfo.m
//  IMYPublic
//
//  Created by ljh on 2019/10/21.
//

#import "IMYCodePushAppInfo.h"
#import "IMYPublic.h"

#define kInfoFileName @"info.dex"

@interface IMYCodePushAppInfo() {
    // 是否最新数据
    BOOL _isNewest;
    // 自动清理的天数
    NSUInteger _cleanDay;
    // 最近打开的时间
    NSUInteger _lastOpenTime;
}
@end

@implementation IMYCodePushAppInfo

- (instancetype)initWithAppKey:(NSString *)appKey {
    self = [super init];
    if (self) {
        _appKey = appKey;
        _isNewest = NO;
        _cleanDay = 0;
        [self setup];
        [self unserialize];
        // 无远程配置，尝试读取本地bundle
        if (!_outPath.length) {
            _outPath = [[NSBundle mainBundle] pathForResource:appKey ofType:@"bundle"];
        }
    }
    return self;
}

- (void)setup {
    // 创建根目录
    NSString *dirPath = [IMYCodePushAppInfo bundlePathWithAppKey:self.appKey];
    [NSString imy_createDirectoryWithPath:dirPath];
    _rootDir = [dirPath copy];
}

- (BOOL)isNewest {
    if (!_isNewest) {
        return NO;
    }
    const NSUInteger now = IMYDateTimeIntervalSince1970();
    const NSUInteger diff = now - _lastOpenTime;
    // 更新时间大于 8小时，强制改为待更新
    if (diff > 28800) {
        _isNewest = NO;
    }
    return _isNewest;
}

- (void)toNewestOne {
    _isNewest = YES;
    _lastOpenTime = IMYDateTimeIntervalSince1970();
    // 更新 Bundle 最后一次打开时间
    [self serialize];
}

- (void)updateCurrentMD5:(NSString *)md5 cleanDay:(NSUInteger)cleanDay {
    // 判断文件夹路径是否存在
    NSString *const outPath = [self.rootDir stringByAppendingPathComponent:md5];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:outPath]) {
        NSAssert(NO, @"无对应解压文件!");
        return;
    }
    _md5 = [md5 copy];
    _outPath = outPath;
    _cleanDay = cleanDay;
    
    [self serialize];
}

- (void)serialize {
    const NSUInteger now = IMYDateTimeIntervalSince1970();
    NSString *content = [NSString stringWithFormat:@"%@::%@::%@", self.md5, @(now), @(_cleanDay)];
    NSString *infoPath = [self.rootDir stringByAppendingPathComponent:kInfoFileName];
    [content writeToFile:infoPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
}

- (void)unserialize {
    NSString *const infoPath = [self.rootDir stringByAppendingPathComponent:kInfoFileName];
    NSString *const infoContent = [NSString stringWithContentsOfFile:infoPath encoding:NSUTF8StringEncoding error:nil];
    // 数据格式不对
    NSArray *components = [infoContent componentsSeparatedByString:@"::"];
    if (components.count < 3) {
        return;
    }
    NSString *const md5 = components.firstObject;
    // 获取解压后的文件夹
    NSString *const outPath = [self.rootDir stringByAppendingPathComponent:md5];
    // 获取完整zip地址
    NSString *const zipPath = [outPath stringByAppendingPathExtension:@"zip"];
    
    // 判断解压后的文件夹内容数量
    NSFileManager *fileManager = [NSFileManager defaultManager];
    const BOOL hasExists = [fileManager contentsOfDirectoryAtPath:outPath error:nil].count > 0;
    if(!hasExists) {
        // 无解压后的内容， 删除全部文件
        [fileManager removeItemAtPath:outPath error:nil];
        [fileManager removeItemAtPath:zipPath error:nil];
        [fileManager removeItemAtPath:infoPath error:nil];
    }
    
    // 文件校验通过，才进行赋值
    if(hasExists) {
        _md5 = md5;
        _outPath = outPath;
        _lastOpenTime = [components[1] unsignedIntegerValue];
        _cleanDay = [components[2] unsignedIntegerValue];
        // 删除其他版本的 zip包和解压文件夹
        NSArray *contents = [fileManager contentsOfDirectoryAtPath:self.rootDir error:nil];
        for (NSString *name in contents) {
            if ([name hasPrefix:md5] || [name isEqualToString:kInfoFileName]) {
                continue;
            }
            NSString *path = [self.rootDir stringByAppendingPathComponent:name];
            [fileManager removeItemAtPath:path error:nil];
        }
    }
}

+ (NSString *)getRootDirectoryPath {
    static NSString *kRootDir;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        kRootDir = [[NSString imy_documentsDirectory] stringByAppendingPathComponent:@"codepush"];
        [NSString imy_createDirectoryWithPath:kRootDir];
    });
    return kRootDir;
}

+ (BOOL)bundleInfoExistsAtAppKey:(NSString *)appKey {
    NSString *bundlePath = [self bundlePathWithAppKey:appKey];
    NSString *infoPath = [bundlePath stringByAppendingPathComponent:kInfoFileName];
    BOOL isExists = [[NSFileManager defaultManager] fileExistsAtPath:infoPath];
    if (!isExists) {
        NSString *localPath = [[NSBundle mainBundle] pathForResource:appKey ofType:@"bundle"];
        isExists = localPath.length > 0;
    }
    return isExists;
}

+ (NSString *)bundlePathWithAppKey:(NSString *)appKey {
    return [[IMYCodePushAppInfo getRootDirectoryPath] stringByAppendingPathComponent:appKey];
}

// 清理打开时间超过 x天 以上的包
+ (void)cleanWithExpiredBundles {
    // 获取当前时间
    NSFileManager *fileManager = [NSFileManager defaultManager];
    const NSUInteger nowTime = IMYDateTimeIntervalSince1970();
    // 获取已下载的 Bundle Info Path
    NSString *rootDirPath = [self getRootDirectoryPath];
    NSArray *rootFiles = [fileManager contentsOfDirectoryAtPath:rootDirPath error:nil];
    for (NSString *dirName in rootFiles) {
        NSString *infoName = [NSString stringWithFormat:@"%@/%@", dirName, kInfoFileName];
        NSString *infoPath = [rootDirPath stringByAppendingPathComponent:infoName];
        NSString *infoContent = [NSString stringWithContentsOfFile:infoPath encoding:NSUTF8StringEncoding error:nil];
        NSArray *components = [infoContent componentsSeparatedByString:@"::"];
        if (components.count < 3) {
            // 数据格式不对
            continue;
        }
        const NSUInteger lastOpenTime = [components[1] unsignedLongValue];
        const NSUInteger cleanDay = [components[2] unsignedLongValue];
        const NSUInteger diffTime = labs(nowTime - lastOpenTime);
        // 最后一次打开时间超过 设定的值
        if (cleanDay > 0 && (diffTime / 86400) > cleanDay) {
            NSString *bundlePath = infoPath.stringByDeletingLastPathComponent;
            [fileManager removeItemAtPath:bundlePath error:nil];
        }
    }
}

@end
