//
//  IMYUserGroupModel.h
//  ZZGlobalMain
//
//  Created by ss on 2025/2/19.
//

#import <Foundation/Foundation.h>
#import "IMYAppDefines.h"

NS_ASSUME_NONNULL_BEGIN
//亲友身份判断
#define IMYAppQinyouValue($normal, $qinyou) ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou ? $qinyou : $normal)
//亲友身份-经期
#define IMYAppQinyouModeNormal ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou && [IMYPublicAppHelper shareAppHelper].userRelationModel.mode == IMYVKUserModeNormal)
//亲友身份-备孕
#define IMYAppQinyouModeForPregnant ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou && [IMYPublicAppHelper shareAppHelper].userRelationModel.mode == IMYVKUserModeForPregnant)
//亲友身份-怀孕
#define IMYAppQinyouModePregnancy ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou && [IMYPublicAppHelper shareAppHelper].userRelationModel.mode == IMYVKUserModePregnancy)
//亲友身份-辣妈
#define IMYAppQinyouModeLama ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeQinYou && [IMYPublicAppHelper shareAppHelper].userRelationModel.mode == IMYVKUserModeLama)

///伴侣用户数据
@interface IMYRelationPersonInfoModel : NSObject

// 用户 ID
@property (nonatomic, copy) NSString *user_id;
// 用户头像链接
@property (nonatomic, copy) NSString *avatar;
// 用户生日
@property (nonatomic, copy) NSString *birthday;
// 用户身高
@property (nonatomic, assign) NSInteger height;
// 经期持续天数
@property (nonatomic, assign) NSInteger duration_of_menstruation;
// 月经周期天数
@property (nonatomic, assign) NSInteger menstrual_cycle;
// 模式（可选）
@property (nonatomic, assign) IMYVKUserMode mode;

@end
/**
 * 群成员关系模型，用于表示群中成员的相关信息。
 */
@interface IMYGroupRelationModel : NSObject

/**
 * 关系id ID，标识该成员所属的群。
 */
@property (nonatomic, assign) NSInteger relation_id;
/**
 * 群 ID，标识该成员所属的群。
 */
@property (nonatomic, assign) NSInteger group_id;
/**
 * 成员的用户 ID，唯一标识群中的成员。
 */
@property (nonatomic, copy) NSString *user_id;
/**
 * 群身份类型，1 表示群主，2 表示管理员，3 表示普通成员。
 */
@property (nonatomic, assign) NSInteger role_type;
/**
 * 成员进群的时间戳，单位为秒（Unix 时间戳）。
 */
@property (nonatomic, assign) NSInteger created_at;
/**
 * 用户身份，具体含义根据业务需求而定。
 */
@property (nonatomic, assign) IMYVKUserMode mode;
/**
 * 绑定天数。
 */
@property (nonatomic, assign) NSInteger bind_days;

@property (nonatomic, strong) IMYRelationPersonInfoModel *personInfo;

@end

/**
 * 群数据模型，用于表示群的整体信息，包含群基本信息和群成员关系信息。
 */
@interface IMYUserGroupModel : NSObject
/**
 * 群 ID，唯一标识一个群。
 */
@property (nonatomic, assign) NSInteger group_id;
/**
 * 群类型，1 表示伴侣群。
 */
@property (nonatomic, assign) NSInteger group_type;
/**
 * 群主的用户 ID，标识创建并管理该群的用户。
 */
@property (nonatomic, copy) NSString *user_id;
/**
 * 群的创建时间戳，单位为秒（Unix 时间戳）。
 */
@property (nonatomic, assign) NSInteger created_at;
/**
 * 群成员关系数组，包含群中每个成员的详细关系信息。
 */
@property (nonatomic, strong) NSArray<IMYGroupRelationModel *> *group_relations;
/// 获取所有群关系
+ (NSArray *)getAllUserGroupModel;
/// 清除该用户的数据
+ (void)clearHistoryList;
@end



NS_ASSUME_NONNULL_END
