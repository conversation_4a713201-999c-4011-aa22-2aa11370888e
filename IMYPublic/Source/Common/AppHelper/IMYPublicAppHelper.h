//
//  IMYPublicAppHelper.h
//  IMY_ViewKit
//
//  Created by ljh on 15/5/21.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYAppDefines.h"
#import "IMYAppProtocol.h"
#import "IMYUserInfoModel.h"
#import "IMYBBJUserInfoModel.h"
#import "IMYUserGroupModel.h"

@interface IMYPublicAppHelper : NSObject <IMYAppProtocol, IMYAppStoreProtocol, IMYAppUserProtocol, IMYAppFeedbackProtocol>
///应用内的变量
+ (instancetype)shareAppHelper;
@end

@interface IMYPublicAppHelper (Created)
/// 类别重载该方法，返回具体使用的 Class
+ (Class)usingAppHelperClass;
@end

///数据属性
@interface IMYPublicAppHelper ()

/// 用户id
@property (nonatomic, copy) NSString *userid;
/// 动态-用户身份（在新宝宝记中会根据页面而改变)
@property (nonatomic, assign) IMYVKUserMode userMode;
@property (nonatomic, assign) NSInteger userNewState; //1:新注册，2:新切换，3:登录老用户，0:其它
/// 固定-用户身份，主身份
@property (nonatomic, assign) IMYVKUserMode mainUserMode;
/// 亲友、伴侣身份，邀请人信息模型
@property (nonatomic, strong) IMYGroupRelationModel *userRelationModel;
/*
 用户第二身份, 只要有宝宝ID, enable就为YES, 与AB无关
 具体判断在 SYIMYAppHelper 的 resetUserSecondModel 方法
 */
@property (nonatomic, strong) IMYUserInfoModel *userSecondModel;
/*
 用户第二身份, 当用户在多宝的情况下 单独查看某个类型的宝宝
 */
@property (nonatomic, strong) IMYBBJUserInfoModel *bbjUserSecondModel;

///用户昵称
@property (nonatomic, copy) NSString *nickName;
///用户头像
@property (nonatomic, copy) NSString *avatar;
///用户柚币数量
@property (nonatomic, assign) NSUInteger coin;
///用户等级
@property (nonatomic, assign) NSUInteger level;
///最后一次签到时间
@property (nonatomic, copy) NSDate *lastSignDate;
///今天是否已签到
@property (nonatomic, assign) BOOL todaySigned;
///签到显示样式 1-未签到是旋转柚币样式;2-未签到是小红点样式;
@property (nonatomic, assign) NSInteger signDisplayType;
/// 签到显示文案
@property (nonatomic, copy) NSString *signText;


///0 普通用户， 1美柚号， 2品牌号
@property (nonatomic, assign) NSUInteger user_type;
///是否是认证后的美柚号
@property (nonatomic, assign) BOOL is_mp_vip;

/// 生日 yyyy-MM-dd
@property (nonatomic, copy) NSString *birthday;

/// 用户的周岁 ：1990-05-23 到 2020-05-22 返回 29 岁，1990-05-22 到 2020-05-22 返回 30 岁，
@property (nonatomic, assign) NSInteger userAge;

///出生年份：2000-05-23 就返回：2000
@property (nonatomic, assign) NSInteger birth_year;
///是否有密友圈
@property (nonatomic, assign) BOOL hasMiYouQuan;
///上传图片是否带水印
@property (nonatomic, assign) BOOL hasWatermark;

///消息数量
@property (nonatomic, assign) NSInteger msgCount;
///是否被封号
@property (nonatomic, assign) BOOL cannotAccess;

///渠道id
@property (nonatomic, copy) NSString *channelID;

///当前的语言
@property (nonatomic, copy) NSString *language;

///皮肤id
@property (nonatomic, copy) NSString *themeID;
///皮肤名称
@property (nonatomic, copy) NSString *themeName;
///是否夜间模式
@property (nonatomic, assign) BOOL isNight;
///是否默认皮肤
@property (nonatomic, assign) BOOL isDefaultTheme;
///是否伴侣皮肤
@property (nonatomic, assign) BOOL isMate;

///授权头
@property (nonatomic, copy) NSString *userToken;
///虚拟授权头
@property (nonatomic, copy) NSString *virtualToken;
///用户是否登录
@property (nonatomic, assign) BOOL hasLogin;

///新版请求api 都需要带
@property (nonatomic, readonly) NSString *myclient;

/// 最新app标识
@property (nonatomic, readonly) NSString *myappinfo;

///密码锁 是否展示
@property (nonatomic, assign) BOOL isSecretShowing;

/// 是否关闭插屏广告
@property (nonatomic, assign) BOOL isCloseChapingAds;

/// 是否关闭她她圈插屏广告
@property (nonatomic, assign) BOOL isCloseTTQChapingAds;

/// 是否关闭首页插屏广告
@property (nonatomic, assign) BOOL isCloseHomeChapingAds;

/// 是否关闭二楼动画提示
@property (nonatomic, assign) BOOL isCloseTwoFloorAdGif;

/// 是否不显示有版本更新
@property (nonatomic, assign) BOOL isNoAlterUpdate;

/// 第三方平台用户id
@property (nonatomic, copy) NSString *myid;

/// 是否打开震动反馈
@property (nonatomic, assign) BOOL isImpactFeedback;

/// 是否打开个推开关
@property (nonatomic, assign) BOOL isPersonalRecommand;

/// 是否打开健康开关
@property (nonatomic, assign) BOOL isHealthKitEnable;

/// 是否 同意 App的隐私权限申明，同意后才能注册第三方SDK
@property (nonatomic, assign) BOOL hasAgreedPrivacy;

/// 是否使用青少年模式
@property (nonatomic, readonly) BOOL useYoungMode;

/// 是否允许弹出体重秤秤重页面
@property (nonatomic, readonly) BOOL isWeightBalanceEnable;

/// 是否打开 个性化广告
@property (nonatomic, readonly) BOOL isPersonalAD;

/// 是否打开 个性化电商推荐
@property (nonatomic, readonly) BOOL isPersonalEBRecommend;

/// 第三方登录方式取消授权（目前指微信取消登录授权）
@property (nonatomic, assign) BOOL isThirdAutoCancel;

//体温提醒是否打开
- (BOOL)isTemperatureRemindOpen;
//排卵试纸是否打开
- (BOOL)isTestPaperRemindOpen;
/// 身份字符串
+ (NSString *)userModeNameForMode:(IMYVKUserMode)userMode;
/// 切换吐司提示语
- (NSString *)userModeChangeToastString;
+ (NSString *)userModeChangeToastStringForMode:(IMYVKUserMode)userMode;

/**
 *  记经期 or 备孕
 */
@property (nonatomic, assign) NSInteger parsMensesDay;               //行经时间  比如 5天
@property (nonatomic, assign) NSInteger parsInterval;                //周期（由用户设置）      比如 28天
@property (nonatomic, readonly) NSInteger avgInterval;                 //平均周期（由服务端计算得出）   比如 28天
@property (nonatomic, assign) double height;                         //身高 单位:cm
@property (nonatomic, assign) double tagetWeight;                    //目标体重
@property (nonatomic, strong) NSString *lastTimeMenses;              //最后一次经期:yyyy-MM-dd
@property (nonatomic, readonly) NSString *lastTimeMensesInRecord;    //日历里最后一次经期:yyyy-MM-dd
@property (nonatomic, readonly) NSString *lastTimeMensesEndInRecord; //日历里最后一次经期结束:yyyy-MM-dd
@property (nonatomic, readonly) NSInteger pregnancyOdds;             //当天怀孕几率 (0~100)
@property (nonatomic, readonly) NSInteger today_diff_ovulatory_day;  //距下次排卵日还多少天


@property (nonatomic, assign) NSInteger phase_day;                   //备孕周期天数  比如 5天
@property (nonatomic, assign) NSInteger phase;                       //备孕周期       比如 28天

/**
 *  怀孕设定
 */
@property (nonatomic, assign) NSUInteger gravidity_value;      //好运值
@property (nonatomic, strong) NSString *pregnancy;             //预产期:yyyy-MM-dd
@property (nonatomic, assign) NSInteger pregnancyStartDayDiff; //现在是 妊娠第几天

/**
 *  辣妈设定
 */
@property (nonatomic, copy) NSString *babyBirthday; //宝宝出生日:yyyy-M-d (对这个格式很头疼...改不动)
@property (nonatomic, copy) NSString *baby_nick;    //宝宝昵称
@property (nonatomic, copy) NSString *baby_weight;  //宝宝体重
@property (nonatomic, assign) NSInteger baby_sex;   //宝宝性别 1男2女
@property (nonatomic, assign) NSInteger is_eutocia; //是否顺产 1是2否
@property (nonatomic, copy) NSString *babyBirthday_v1; //宝宝出生日:yyyy-M-d（二胎版本之前的宝宝数据)
@property (nonatomic, assign) NSInteger baby_sex_v1;   //宝宝性别 1男2女（二胎版本之前的宝宝数据）
@property (nonatomic, readonly) NSInteger currentBabyID;    //当前宝宝ID
@property (nonatomic, readonly) NSUInteger babyCount;       //宝宝个数

//保存用户数据
- (void)saveUserData:(NSDictionary *)data;

// 重置第二身份
- (void)resetUserSecondModel;

/// 命中夜间免打扰条件（夜间免打扰开关打开 && 当前处于夜间时间段）
- (BOOL)hitNightUndisturbRule;

@end

@interface IMYPublicAppHelper (IMYRecordBaby)

/// 返回宝宝出生日
/// @param babyId 宝宝ID
- (NSString *)babyBirthdayWithID:(NSInteger)babyId;
@end


@interface IMYPublicAppHelper (IMYLaunchUtil)
///当Launch结束时 执行该 block
- (void)runActionBlockWithLaunchFinished:(void (^)(void))actionBlock forKey:(NSString *)key;
@end


@interface IMYPublicAppHelper (FlowModel)

/**
 用户是否开启省流量模式
 */
+ (BOOL)userLowFlowModel;

@end

@interface IMYPublicAppHelper (Pregnancy)
/// 返回所有孕期数据，只简单返回开始日（begin）和结束日（end），不处理结束状态。
- (NSArray *)pregnancyAllData;

@end

typedef NS_ENUM(NSUInteger, IMYPrivacyUpdateAgreeType) {
    IMYPrivacyUpdateAgreeType_SDK_Default = 0,
    IMYPrivacyUpdateAgreeType_SDK_LCDFeeds = 1  /// 大社区字节流（头条）
};

typedef void (^IMYPrivacyUpdateBlock)(BOOL hasUpdate, NSString *content);

@interface IMYPublicAppHelper (PrivacyUpdate)

/// 是否有隐私更新
- (void)hasPrivacyUpdate:(IMYPrivacyUpdateBlock)block;

/// 隐私更新同意
- (void)onPrivacyUpdateAgree;

/// 对应隐私更新，是否已经同意
- (BOOL)privacyUpdateAgreeWithType:(IMYPrivacyUpdateAgreeType)type;

/// 隐私更新同意信号
- (RACSignal *)privacyUpdateAgreeSignal;

@end

///已弃用方法
@interface IMYPublicAppHelper (Deprecated)

@end
