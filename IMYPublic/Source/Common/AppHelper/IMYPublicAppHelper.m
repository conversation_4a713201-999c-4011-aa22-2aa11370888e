//
//  IMYPublicAppHelper.m
//  IMY_ViewKit
//
//  Created by ljh on 15/5/21.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPublicAppHelper.h"
#import "IMYPublic.h"

///美柚系统里面的id
NSString *kIMY_MeetyouAppId = nil;
///美柚系统里面的应用名称
NSString *kIMY_MeetyouAppName = nil;
///苹果应用商店的id
NSString *kIMY_AppleStoreId = nil;
///应用外部打开scheme
NSString *kIMY_MeetyouAppScheme = nil;

///应用闪退通知
NSString *const IMYApplicationOnCrashNotification = @"IMYApplicationOnCrashNotification";

@implementation IMYPublicAppHelper
#pragma mark - IMYAppProtocol
- (NSString *)app_id {
    if (!kIMY_MeetyouAppId) {
        @throw [NSException exceptionWithName:@"IMYAppProtocol"
                                       reason:NSInternalInconsistencyException
                                     userInfo:@{NSLocalizedDescriptionKey: @"子类必须重载此方法"}];
    }
    return kIMY_MeetyouAppId;
}

- (NSString *)appName {
    if (!kIMY_MeetyouAppName) {
        @throw [NSException exceptionWithName:@"IMYAppProtocol"
                                       reason:NSInternalInconsistencyException
                                     userInfo:@{NSLocalizedDescriptionKey: @"子类必须重载此方法"}];
    }
    return kIMY_MeetyouAppName;
}

- (NSString *)appleStoreID {
    if (!kIMY_AppleStoreId) {
        @throw [NSException exceptionWithName:@"IMYAppProtocol"
                                       reason:NSInternalInconsistencyException
                                     userInfo:@{NSLocalizedDescriptionKey: @"子类必须重载此方法"}];
    }
    return kIMY_AppleStoreId;
}

- (NSString *)appScheme {
    if (!kIMY_MeetyouAppScheme) {
        @throw [NSException exceptionWithName:@"IMYAppProtocol"
                                       reason:NSInternalInconsistencyException
                                     userInfo:@{NSLocalizedDescriptionKey: @"子类必须重载此方法"}];
    }
    return kIMY_MeetyouAppScheme;
}

- (NSString *)appSecretKey {
    return @"";
}

#pragma mark -

+ (NSString *)getDefaultChannelID {
    return @"AppStore";
}

+ (void)initialize {
#ifdef DEBUG
    if (self == [IMYPublicAppHelper class]) {
        NSLog(@"home dir: %@", NSHomeDirectory());
    }
#endif
    [self imy_yyjsonRemoveProperty:@"debugDescription"];
    [self imy_yyjsonRemoveProperty:@"description"];
    [self imy_yyjsonRemoveProperty:@"superclass"];
    [self imy_yyjsonRemoveProperty:@"userModeChangedSignal"];
    [self imy_yyjsonRemoveProperty:@"useridChangedSignal"];
    [self imy_yyjsonRemoveProperty:@"userRelativeIdChangedSignal"];
    [self imy_yyjsonRemoveProperty:@"userBirthdayChangedSignal"];
    [self imy_yyjsonRemoveProperty:@"openPersonnalAdRecommondChangedSignal"];
}

+ (RACReplaySubject *)getUseridChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}
+ (RACReplaySubject *)getUserRelativeIdChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}

+ (RACSubject *)getUserRelativeModeChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}

+ (RACReplaySubject *)getUserModeChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}

+ (RACReplaySubject *)getUserBirthdayChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}

///全局 个性化广告推荐 切换通知
+ (RACSubject *)getOpenPersonnalAdRecommondChangedSubject {
    static RACReplaySubject *subject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        subject = [RACReplaySubject replaySubjectWithCapacity:1];
    });
    return subject;
}

+ (instancetype)shareAppHelper {
    static IMYPublicAppHelper *appHelper = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class appHelperClass = [IMYPublicAppHelper class];
        if ([appHelperClass respondsToSelector:@selector(usingAppHelperClass)]) {
            appHelperClass = [IMYPublicAppHelper usingAppHelperClass];
        }
        appHelper = [[appHelperClass alloc] init];
    });
    return appHelper;
}

- (void)setUserid:(NSString *)userid {
    _userid = userid;
    _userAge = 0;
    [[IMYPublicAppHelper getUseridChangedSubject] sendNext:userid];
}

- (void)setUserMode:(IMYVKUserMode)userMode {
    _userMode = userMode;
    [[IMYPublicAppHelper getUserModeChangedSubject] sendNext:@(userMode)];
}

- (IMYVKUserMode)mainUserMode {
    return [self userMode];
}

- (void)setBirthday:(NSString *)birthday {
    _birthday = birthday;
    _userAge = 0;
    [[IMYPublicAppHelper getUserBirthdayChangedSubject] sendNext:birthday];
}

- (NSInteger)userAge {
    if (self.birthday && _userAge == 0) {
        _userAge = [[self.birthday imy_getOnlyDate] imy_getDiffComponents:NSDate.imy_today unitFlags:NSCalendarUnitYear].year;
    }
    return _userAge;
}

- (RACSignal *)useridChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getUseridChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

- (RACSignal *)userModeChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getUserModeChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

- (RACSignal *)userRelativeIdChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getUserRelativeIdChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

- (RACSignal *)userRelativeModeChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getUserRelativeModeChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

- (RACSignal *)userBirthdayChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getUserBirthdayChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

- (RACSignal *)openPersonnalAdRecommondChangedSignal {
    static RACSignal *signal;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        signal = [[[[IMYPublicAppHelper getOpenPersonnalAdRecommondChangedSubject] throttle:0.1] distinctUntilChanged] deliverOnMainThread];
    });
    return signal;
}

// 以自然天作为间隔
- (BOOL)todaySigned {
    if (!self.lastSignDate) {
        return NO;
    }
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSCalendarUnit unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth |  NSCalendarUnitDay;
    NSDateComponents *comp1 = [calendar components:unitFlags fromDate:self.lastSignDate];
    NSDateComponents *comp2 = [calendar components:unitFlags fromDate:[NSDate date]];
    
    return [comp1 day] == [comp2 day] &&
    [comp1 month] == [comp2 month] &&
    [comp1 year]  == [comp2 year];
}

- (NSString *)myclient {
    static NSString *myclient = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSMutableString *buildString = [[NSMutableString alloc] init];
        //前两位 表示appid
        [buildString appendFormat:@"%.2ld", (long)(self.app_id.integerValue) % 100];
        //第三位 表示平台 ios = 2
        [buildString appendString:@"2"];
        //版本号
        {
            NSMutableString *versionString = [NSMutableString string];
            NSArray *varray = [APPVersion componentsSeparatedByString:@"."];
            for (int i = 0; i < varray.count; i++) {
                NSString *str = [varray objectAtIndex:i];
                if (i == 0 && str.length <= 1) {
                    [versionString appendString:@"0"];
                }
                [versionString appendString:str];
                if (versionString.length > 4) {
                    break;
                }
            }
            while (versionString.length < 4) {
                [versionString appendString:@"0"];
            }
            NSString *vstring = [versionString substringToIndex:4];
            [buildString appendString:vstring];
        }
        //渠道号
        [buildString appendFormat:@"%.4ld", (long)[IMYPublicAppHelper getDefaultChannelID].integerValue % 10000];
        //5位保留位
        [buildString appendString:@"00000"];
        // result my client string
        myclient = [buildString copy];
    });
    return myclient;
}

- (NSString *)myappinfo {
    static NSString *myappinfo = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSMutableString *buildString = [[NSMutableString alloc] init];
        //前两位 表示appid
        [buildString appendFormat:@"%.2ld", (long)(self.app_id.integerValue) % 100];
        //第三位 表示平台 ios = 2
        [buildString appendString:@"-2-"];
        //版本号
        [buildString appendString:APPVersion];
        //渠道号
        [buildString appendString:@"-0000"];
        //保留位
        [buildString appendString:@"-0"];
        // 全局赋值
        myappinfo = [buildString copy];
    });
    return myappinfo;
}

- (NSString *)channelID {
    return @"AppStore";
}

- (BOOL)isDefaultTheme {
    NSInteger themeID = [self.themeID integerValue];
    BOOL isDefault = (themeID == 20 || themeID == 0);
    return isDefault;
}

- (NSString *)userModeName {
    return [IMYPublicAppHelper userModeNameForMode:self.userMode];
}

+ (NSString *)userModeNameForMode:(IMYVKUserMode)userMode {
    switch (userMode) {
        case IMYVKUserModeLama:
            return IMYString(@"育儿");
        case IMYVKUserModePregnancy:
            return IMYString(@"怀孕");
        case IMYVKUserModeForPregnant:
            return IMYString(@"备孕");
        case IMYVKUserModeQinYou:
            return IMYString(@"亲友");
        default:
            return IMYString(@"经期");
    }
}

- (NSString *)userModeString {
    switch (self.userMode) {
        case IMYVKUserModeLama:
            return @"我是辣妈";
        case IMYVKUserModePregnancy:
            return @"我怀孕了";
        case IMYVKUserModeForPregnant:
            return @"我在备孕";
        case IMYVKUserModeQinYou:
            return @"亲友";
        default:
            return @"我要记经期";
    }
}

- (NSString *)modeKeyString {
    switch (self.userMode) {
        case IMYVKUserModeNormal:
            return @"mode_jq";
        case IMYVKUserModeForPregnant:
            return @"mode_by";
        case IMYVKUserModePregnancy:
            return @"";
        case IMYVKUserModeLama:
            return @"mode_lm";
        case IMYVKUserModeQinYou:
            return @"mode_qy";
    }
    return @"";
}

- (NSString *)userModeChangeToastString {
    return [IMYPublicAppHelper userModeChangeToastStringForMode:self.userMode];
}

+ (NSString *)userModeChangeToastStringForMode:(IMYVKUserMode)userMode {
    switch (userMode) {
        case IMYVKUserModeLama:
            return IMYString(@"你已切换至育儿模式");
        case IMYVKUserModePregnancy:
            return IMYString(@"你已切换至孕期模式");
        case IMYVKUserModeForPregnant:
            return IMYString(@"你已切换至备孕模式");
        case IMYVKUserModeQinYou:
            return IMYString(@"已开启伴侣模式");
        default:
            return IMYString(@"你已切换至经期模式");
    }
    return @"";
}

- (NSString *)getModeDate {
    return @"";
}

- (BOOL)enabledNotification {
    UIUserNotificationSettings *setting = [[UIApplication sharedApplication] currentUserNotificationSettings];
    if (setting.types == UIUserNotificationTypeNone) {
        return NO;
    } else {
        return YES;
    }
}

- (void)goRating {
    NSString *openURL = [NSString stringWithFormat:@"itms-apps://ax.itunes.apple.com/WebObjects/MZStore.woa/wa/viewContentsUserReviews?type=Purple+Software&id=%@", self.appleStoreID];
#if IS_TEST
    if (isSimulator) {
        openURL = @"http://apple.com";
    }
#endif
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:openURL]];
}

- (void)runActionBlockWithLaunchFinished:(void (^)(void))actionBlock forKey:(NSString *)key {
    [NSObject imy_asyncBlock:actionBlock onLevel:IMYQueueLevelMain afterSecond:3 forKey:key];
}

- (NSInteger)parsMensesDay {
    return 5;
}

- (NSInteger)parsInterval {
    return 28;
}

- (NSInteger)avgInterval {
    return 28;
}

- (NSInteger)phase_day {
    return 5;
}

- (NSInteger)phase {
    return 28;
}

- (BOOL)isImpactFeedback {
    return YES;
}

- (BOOL)isPersonalRecommand {
    return YES;
}

- (BOOL)isHealthKitEnable {
    return YES;
}

- (BOOL)hasAgreedPrivacy {
    return YES;
}

- (BOOL)useYoungMode {
    return NO;
}

- (BOOL)isWeightBalanceEnable {
    return YES;
}

- (BOOL)isPersonalAD {
    return YES;
}

- (BOOL)isPersonalEBRecommend{
    return YES;
}

- (BOOL)isTemperatureRemindOpen {
    return NO;
}

- (BOOL)isTestPaperRemindOpen {
    return NO;
}

- (NSString *)babyBirthdayWithID:(NSInteger)babyId {
    return nil;
}

- (void)saveUserData:(NSDictionary *)data {
}

+ (BOOL)userLowFlowModel {
    IMYUserDefaults *ud = [IMYUserDefaults standardUserDefaults];
    id value = [ud objectForKey:@"Pic2G3GMode"];
    if (!value) {
        return NO;
    }
    return [value intValue] != 0;
}

- (void)resetUserSecondModel {
    
}

/// 命中夜间免打扰条件（夜间免打扰开关打开 && 当前处于夜间时间段）
- (BOOL)hitNightUndisturbRule {
    return NO;
}

- (NSString *)privacyRemoteURLString {
    // 隐私政策，所有App共用一个url，使用 app_id 参数区分
    NSString *urlString = [NSString stringWithFormat:@"%@/users/privacy.html?platform=ios&app_id=%ld&v=%@",view_seeyouima_com, (long)[IMYPublicAppHelper AppID], APPVersion];
    return urlString;
}

- (NSString *)userAgreementRemoteURLString {
    NSString *urlString = nil;
    if (IMYPublicAppHelper.isYouzijie) {
        urlString = @"https://yzj-images.youzibuy.com/youzibuy/protocol/index.html?mywtb_name=youzibuy";
    } else if (IMYPublicAppHelper.isYunqi || IMYPublicAppHelper.isYunqiPro) {
        urlString = @"https://view.seeyouyima.com/help/user_agreement_bbj.html";
    } else {
        urlString = [NSString stringWithFormat:@"%@/help/user_agreement_protocol.html?platform=ios&app_id=%ld&v=%@",view_seeyouima_com, (long)[IMYPublicAppHelper AppID], APPVersion];
    }
    return urlString;
}

- (NSString *)childrenPrivacyRemoteURLString{
    // 儿童政策，所有App共用一个url，使用 app_id 参数区分
    NSString *urlString = [NSString stringWithFormat:@"%@/users/children-privacy-rules.html?platform=ios&app_id=%ld&v=%@", nodejs_user_seeyouyima_com, (long)[IMYPublicAppHelper AppID], APPVersion];
    return urlString;
}

- (NSArray *)pregnancyAllData {
    return nil;
}

- (NSString *)feedbackURIWithType:(IMYAppFeedbackType)type {
    switch (type) {
        case IMYAppFeedbackTypeCancellation: {
            if (IMYURLEnvironmentManager.currentType == IMYURLEnviromentTypeTest) {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6NTc5LCJkZWZhdWx0U2V0SWQiOjY2MCwiTkVFRF9ISURFX0RJQUxPRyI6dHJ1ZSwiZmVlZGJhY2tfbmFtZSI6IuazqOmUgOW4kOWPtyIsImhpZGRlbkhlbHBCdXR0b24iOjF9";
            } else {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6Mjk0LCJkZWZhdWx0U2V0SWQiOjU5MywiTkVFRF9ISURFX0RJQUxPRyI6dHJ1ZSwiZmVlZGJhY2tfbmFtZSI6IuazqOmUgOW4kOWPtyIsImhpZGRlbkhlbHBCdXR0b24iOiAxfQ==";
            }
        } break;
        case IMYAppFeedbackTypeSuggestions: {
            if (IMYURLEnvironmentManager.currentType == IMYURLEnviromentTypeTest) {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6IDY1NiwgImRlZmF1bHRTZXRJZCI6IDY1NSwgIk5FRURfSElERV9ESUFMT0ciOiBmYWxzZSwgImZlZWRiYWNrX25hbWUiOiAi5oqV6K+J5LiO5bu66K6uIiwgImhpZGRlbkhlbHBCdXR0b24iOiAwfQ==";
            } else {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6IDg1NiwgImRlZmF1bHRTZXRJZCI6IDg1NSwgIk5FRURfSElERV9ESUFMT0ciOiBmYWxzZSwgImZlZWRiYWNrX25hbWUiOiAi5oqV6K+J5LiO5bu66K6uIiwgImhpZGRlbkhlbHBCdXR0b24iOiAwfQ==";
            }
        } break;
        case IMYAppFeedbackTypeYouth: {
            if (IMYURLEnvironmentManager.currentType == IMYURLEnviromentTypeTest) {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6IDY2MiwgImRlZmF1bHRTZXRJZCI6IDY2MSwgIk5FRURfSElERV9ESUFMT0ciOiB0cnVlLCAiZmVlZGJhY2tfbmFtZSI6ICLmnKrmiJDlubTkurrmipXor4nkuL7miqUiLCAiaGlkZGVuSGVscEJ1dHRvbiI6IDF9";
            } else {
                return @"meiyou:///feedback?params=eyJTRUxFQ1RfVFlQRSI6IDg1NywgImRlZmF1bHRTZXRJZCI6IDg1OCwgIk5FRURfSElERV9ESUFMT0ciOiB0cnVlLCAiZmVlZGJhY2tfbmFtZSI6ICLmnKrmiJDlubTkurrmipXor4nkuL7miqUiLCAiaGlkZGVuSGVscEJ1dHRvbiI6IDF9";
            }
        } break;
    }
    NSAssert(NO, @"未知的意见反馈类型。");
    return nil;
}

#pragma mark - PrivacyUpdate

/// 是否有隐私更新
- (void)hasPrivacyUpdate:(IMYPrivacyUpdateBlock)block {
    if (block) {
        block(NO, nil);
    }
}

/// 隐私更新同意
- (void)onPrivacyUpdateAgree {
    
}

/// 对应隐私更新，是否已经同意
- (BOOL)privacyUpdateAgreeWithType:(IMYPrivacyUpdateAgreeType)type {
    return YES;
}

/// 隐私更新同意信号
- (RACSignal *)privacyUpdateAgreeSignal {
    return nil;
}

@end
