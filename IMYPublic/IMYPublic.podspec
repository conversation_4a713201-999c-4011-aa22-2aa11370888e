# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
	s.name = 'IMYPublic'
	s.version = '8.60.006'
	s.description = 'IMYPublic Description'
	s.license = 'MIT'
	s.summary = 'IMYPublic'
	s.homepage = 'https://github.com/meiyoudev/IMYPublic'
	s.authors = { 'xyz' => '<EMAIL>' }
	s.source = { :git => '*********************:iOS/IMYPublic.git', :branch => 'release-8.60.0' }
	s.requires_arc = true
	s.ios.deployment_target = '10.0'

	s.source_files = 'Source/**/*.{h,m,c,S}'
	s.resources = 'Source/**/*.{json,png,jpg,gif,xib,bundle}'
	#s.vendored_frameworks = 'Source/**/*.framework'
	s.vendored_libraries = 'Source/**/*.a'

	s.libraries = 'resolv'

	s.dependency 'CommonCrypto','1.1'
	s.dependency 'FLAnimatedImage','~> 1.0.16'
	s.dependency 'YYImage','~> 1.0.4'
	s.dependency 'UICKeyChainStore','2.1.2'
	s.dependency 'THProgressView','1.0'
	s.dependency 'GCDObjC','0.3.0'
	s.dependency 'IMYVendor'
	s.dependency 'IMYDynamicFrameworks'
end
