#!/bin/bash
# 脚本：check_vipicon_size_diff.sh
# 针对特定文件检查大小差异

files=(
    "Rights/Bundles/Images.xcassets/885-vip/vipicon_img_bg_vip.imageset/<EMAIL>"
    "Rights/Bundles/Images.xcassets/885-vip/vipicon_img_bg_vip.imageset/<EMAIL>"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        # 获取当前文件大小
        current_size=$(ls -l "$file" | awk '{print $5}')
        # 获取最近一次提交（HEAD）中文件的大小
        last_commit_size=$(git show HEAD:"$file" | wc -c | awk '{print $1}')
        # 计算大小差异
        diff_size=$((current_size - last_commit_size))
        echo "File: $file"
        echo "Current size: $current_size bytes"
        echo "Last commit size: $last_commit_size bytes"
        echo "Difference: $diff_size bytes"
        echo "-------------------"
    else
        echo "File: $file"
        echo "Status: File does not exist in working directory"
        echo "-------------------"
    fi
done
