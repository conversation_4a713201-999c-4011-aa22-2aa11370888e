//
//  IMYMRHomeCardImageBoxAdapter.m
//  ZZIMYMain
//
//  Created by ljh on 2024/11/8.
//

#import "IMYMRHomeCardImageBoxAdapter.h"

@interface IMYMRHomeCardImageBoxCell : UITableViewCell
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, copy) NSString *bi_key;
- (void)setupWithRawData:(NSDictionary *)rawData;
+ (CGFloat)heightWithRawData:(NSDictionary *)rawData;
@end

#pragma mark - adapter

@interface IMYMRHomeCardImageBoxAdapter()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, copy) NSString *cardJSONString;
@property (nonatomic, copy) NSDictionary *imageBoxData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardImageBoxAdapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardImageBoxAdapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const NSInteger style = [cardData[@"style"] integerValue];
    return style == 19;
}

- (NSString *)card_key {
    NSString *key = self.cardData[@"title"];
    if (!key.length) {
        key = self.cardData[@"key"];
    }
    return key;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardImageBoxCell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    NSString *jsonStr = data[@"json_str"];
    if (![self.cardJSONString isEqualToString:jsonStr]) {
        self.imageBoxData = [jsonStr imy_jsonObject];
        self.cardJSONString = jsonStr;
    }
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    if (self.imageBoxData.count > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    const CGFloat cellHeight = [IMYMRHomeCardImageBoxCell heightWithRawData:self.imageBoxData];
    return cellHeight;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardImageBoxCell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardImageBoxCell.class];
    // 埋点需要用到的数据
    cell.bi_floor = module.sortedIndex;
    cell.bi_key = self.card_key;
    // 刷新UI
    [cell setupWithRawData:self.imageBoxData];
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //...
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardImageBoxCell ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, strong) NSDictionary *rawData;

@end

@implementation IMYMRHomeCardImageBoxCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 200);
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.edgeBoxView = [UIView new];
    self.edgeBoxView.frame = CGRectMake(12, 0, SCREEN_WIDTH - 24, 200);
    [self.edgeBoxView imy_setBackgroundColor:kCK_White_AN];
    [self.edgeBoxView imy_drawAllCornerRadius:12];
    [self.contentView addSubview:self.edgeBoxView];
    
    self.topImageView = [YYAnimatedImageView new];
    self.topImageView.frame = self.edgeBoxView.bounds;
    self.topImageView.userInteractionEnabled = YES;
    [self.edgeBoxView addSubview:self.topImageView];
    
    self.scrollView = [UIScrollView new];
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.frame = self.edgeBoxView.bounds;
    [self.edgeBoxView addSubview:self.scrollView];
}

- (void)setupWithRawData:(NSDictionary *)rawData {
    if (self.rawData == rawData) {
        return;
    }
    self.rawData = rawData;
    
    // 移除旧图片集
    [self.scrollView imy_removeAllSubviews];
    
    // 头部图片
    {
        NSString *topURL = rawData[@"title_img"];
        CGSize topSize = [topURL imy_lastComponentOriginalImageSize];
        CGFloat topHeight = 0;
        if (topSize.width > 0 && topSize.height > 0) {
            topHeight = ceil(self.topImageView.imy_width / (topSize.width / topSize.height));
        }
        self.topImageView.imy_height = topHeight;
//        if ([topURL hasSuffix:@".jpg"] || [topURL hasSuffix:@".jpeg"]) {
//            [self.topImageView imy_setOriginalImageURL:topURL];
//        } else {
//            [self.topImageView imy_setImageURL:topURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
//        }
        [self.topImageView imy_setOriginalImageURL:topURL];
        
        // 曝光埋点
        NSString * const biInfoTag = rawData[@"key"];
        self.topImageView.imyut_eventInfo.showRadius = 1;
        self.topImageView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, 0, self.bi_floor];
        @weakify(self);
        self.topImageView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.bi_floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @0,
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
        
        [self.topImageView bk_whenTapped:^{
            @strongify(self);
            NSString * const uri = rawData[@"title_uri"];
            if (uri.length > 0) {
                [[IMYURIManager sharedInstance] runActionWithString:uri];
            }
            NSInteger action = uri.length > 0 ? 2 : 3;
            NSDictionary *gaParams = @{
                @"action" : @(action),
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.bi_floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(0),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        }];
    }
    
    // 滚动图片
    {
        __block CGFloat contentHeight = -1;
        __block CGFloat lastLeftX = 0;
        NSArray<NSDictionary *> *images = rawData[@"images"];
        [images enumerateObjectsUsingBlock:^(NSDictionary *map, NSUInteger idx, BOOL *stop) {
            NSString * const imageURL = map[@"img"];
            CGSize size = [imageURL imy_lastComponentOriginalImageSize];
            if (contentHeight < 0) {
                contentHeight = IMYIntegerBy375Design(size.height/3.0);
            }
            
            CGFloat contentWidth = IMYIntegerBy375Design(size.width/3.0);
            
            YYAnimatedImageView *imageView = [YYAnimatedImageView new];
            imageView.frame = CGRectMake(lastLeftX, 0, contentWidth, contentHeight);
//            if ([imageURL hasSuffix:@".jpg"] || [imageURL hasSuffix:@".jpeg"]) {
//                [imageView imy_setOriginalImageURL:imageURL];
//            } else {
//                [imageView imy_setImageURL:imageURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
//            }
            [imageView imy_setOriginalImageURL:imageURL];
            
            [self.scrollView addSubview:imageView];
            
            NSString *biInfoTag = map[@"key"];
            
            // 曝光埋点
            imageView.imyut_eventInfo.showRadius = 1;
            imageView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, idx + 1, self.bi_floor];
            @weakify(self);
            imageView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSDictionary *gaParams = @{
                    @"action" : @1,
                    @"position" : @142,
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"floor" : @(self.bi_floor),
                    @"info_tag" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                };
                [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
            };
            
            imageView.userInteractionEnabled = YES;
            [imageView bk_whenTapped:^{
                @strongify(self);
                NSString * const uri = map[@"uri"];
                if (uri.length > 0) {
                    [[IMYURIManager sharedInstance] runActionWithString:uri];
                }
                NSInteger action = uri.length > 0 ? 2 : 3;
                NSDictionary *gaParams = @{
                    @"action" : @(action),
                    @"position" : @142,
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"floor" : @(self.bi_floor),
                    @"info_tag" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                };
                [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
            }];
            
            lastLeftX = imageView.imy_right;
        }];
        // 滚动区域设置
        self.scrollView.contentSize = CGSizeMake(lastLeftX, 0);
        self.scrollView.scrollEnabled = (lastLeftX - 5 > self.scrollView.imy_width);
        // 组件高度设置
        self.scrollView.imy_height = contentHeight;
        self.scrollView.imy_top = self.topImageView.imy_bottom;
    }
    
    self.edgeBoxView.imy_height = self.scrollView.imy_bottom;
}

+ (CGFloat)heightWithRawData:(NSDictionary *)rawData {
    CGFloat topHeight = 0;
    CGFloat contentHeight = 0;
    {
        NSString *titleImageURL = rawData[@"title_img"];
        CGSize topSize = [titleImageURL imy_lastComponentOriginalImageSize];
        CGFloat topWidth = SCREEN_WIDTH - 24;
        if (topSize.width > 0 && topSize.height > 0) {
            topHeight = ceil(topWidth / (topSize.width / topSize.height));
        }
    }
    
    {
        NSString *contentImageURL = [rawData[@"images"] firstObject][@"img"];
        CGSize contentSize = [contentImageURL imy_lastComponentOriginalImageSize];
        contentHeight = IMYIntegerBy375Design(contentSize.height/3.0);
    }
    
    CGFloat cellHeight = topHeight + contentHeight + 8;
    return cellHeight;
}

@end

