//
//  IMYMRHomeCardBannerV1Adapter.m
//  ZZIMYMain
//
//  Created by ljh on 2024/11/8.
//

#import "IMYMRHomeCardBannerV1Adapter.h"

@interface IMYMRHomeCardBannerV1Cell : UITableViewCell
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, copy) NSString *bi_key;
- (void)setupWithRawData:(NSDictionary *)rawData;
+ (CGFloat)heightWithRawData:(NSDictionary *)rawData;
@end

#pragma mark - adapter

@interface IMYMRHomeCardBannerV1Adapter ()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, copy) NSString *cardJSONString;
@property (nonatomic, copy) NSDictionary *bannerBoxData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardBannerV1Adapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardBannerV1Adapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const NSInteger style = [cardData[@"style"] integerValue];
    return style == 18;
}

- (NSString *)card_key {
    NSString *key = self.cardData[@"title"];
    if (!key.length) {
        key = self.cardData[@"key"];
    }
    return key;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardBannerV1Cell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    NSString *jsonStr = data[@"json_str"];
    if (![self.cardJSONString isEqualToString:jsonStr]) {
        self.bannerBoxData = [jsonStr imy_jsonObject];
        self.cardJSONString = jsonStr;
    }
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    if (self.bannerBoxData.count > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    const CGFloat cellHeight = [IMYMRHomeCardBannerV1Cell heightWithRawData:self.bannerBoxData];
    return cellHeight;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardBannerV1Cell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardBannerV1Cell.class];
    // 埋点需要用到的数据
    cell.bi_floor = module.sortedIndex;
    cell.bi_key = self.card_key;
    // 刷新UI
    [cell setupWithRawData:self.bannerBoxData];
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //...
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardBannerV1Cell ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) IMYBannerView *bannerView;
@property (nonatomic, strong) UIScrollView *tagBoxView;

@property (nonatomic, strong) NSDictionary *rawData;
@property (nonatomic, strong) NSArray<NSDictionary *> *bannerDatas;

@end

@implementation IMYMRHomeCardBannerV1Cell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 200);
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.edgeBoxView = [UIView new];
    self.edgeBoxView.frame = CGRectMake(12, 0, SCREEN_WIDTH - 24, 200);
    [self.edgeBoxView imy_setBackgroundColor:kCK_White_AN];
    [self.edgeBoxView imy_drawAllCornerRadius:12];
    [self.contentView addSubview:self.edgeBoxView];
    
    self.topImageView = [YYAnimatedImageView new];
    self.topImageView.frame = self.edgeBoxView.bounds;
    self.topImageView.userInteractionEnabled = YES;
    [self.edgeBoxView addSubview:self.topImageView];
    
    self.bannerView = [[IMYBannerView alloc] initWithFrame:self.edgeBoxView.bounds];
    self.bannerView.transitionAnimStyle = 1;
    self.bannerView.enableSwipeRecognizer = YES;
    self.bannerView.isHiddenProgress = YES;
    self.bannerView.isHiddenPageControl = YES;
    [self.edgeBoxView addSubview:self.bannerView];
    
    self.tagBoxView = [UIScrollView new];
    self.tagBoxView.showsHorizontalScrollIndicator = NO;
    self.tagBoxView.frame = self.bannerView.frame;
    [self.edgeBoxView addSubview:self.tagBoxView];
    
    @weakify(self);
    self.bannerView.onDidClickEvent = ^(NSInteger index) {
        @strongify(self);
        NSDictionary *data = self.bannerDatas[index];
        NSString * const uri = data[@"uri"];
        NSString * const biInfoTag = data[@"key"];
        if (uri.length > 0) {
            // 点击事件
            [[IMYURIManager sharedInstance] runActionWithString:uri];
        }
        NSInteger action = uri.length > 0 ? 2 : 3;
        NSDictionary *gaParams = @{
            @"action" : @(action),
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @(self.bi_floor),
            @"info_tag" : biInfoTag ?: @"",
            @"index" : @(index + 1),
            @"sub_tab" : self.bi_key ?: @"",
        };
        [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
    };
    
    self.bannerView.onItemViewCreating = ^UIView *(UIView *reusableView, NSInteger index, NSString *imageURL) {
        @strongify(self);
        IMYPAGView *pagView = (id)reusableView;
        if (![pagView isKindOfClass:IMYPAGView.class]) {
            pagView = [[IMYPAGView alloc] initWithBigPAG:NO];
            pagView.imy_size = self.bannerView.imy_size;
        }
        // 判断是否 pag格式
        if ([imageURL containsString:@".pag"]) {
            pagView.notPAGFile = NO;
        } else {
            pagView.notPAGFile = YES;
        }
        // 加载PAG动图
        NSString *imageURLString = [NSString qiniuURL:imageURL type:IMY_QiNiu_AutoWebP];
        [pagView loadWithURL:[NSURL imy_URLWithString:imageURLString] placeholder:nil completed:nil];
        return pagView;
    };
    
    self.bannerView.onDidScrollToIndex = ^(NSInteger index, BOOL isDragged) {
        @strongify(self);
        [self refreshTagViews];
    };
    
    self.bannerView.onDidItemViewCreated = ^(UIView *itemView, NSInteger index) {
        @strongify(self);
        // 曝光事件
        NSDictionary *data = self.bannerDatas[index];
        NSString * const biInfoTag = data[@"key"];
        itemView.imyut_eventInfo.showRadius = 1;
        itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, index + 1, self.bi_floor];
        itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.bi_floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(index + 1),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
    };
}

- (void)refreshTagViews {
    NSInteger const currentIndex = self.bannerView.currentIndex;
    CGFloat midX = -1;
    for (UIImageView *imageView in self.tagBoxView.subviews) {
        if (![imageView isKindOfClass:UIImageView.class]) {
            continue;
        }
        NSInteger const index = imageView.tag - 100;
        NSDictionary *map = self.bannerDatas[index];
        if (index == currentIndex) {
            [imageView imy_setImageURL:map[@"tag_img_sel"] resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
            midX = imageView.imy_centerX;
        } else {
            [imageView imy_setImageURL:map[@"tag_img"] resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        }
    }
    
    // 不可滚动
    if (!self.tagBoxView.scrollEnabled || midX < 0) {
        return;
    }
    
    CGFloat offsetX = midX - self.tagBoxView.imy_width / 2.0;
    if (offsetX < 0) {
        offsetX = 0;
    }
    if (offsetX > self.tagBoxView.contentSize.width - self.tagBoxView.imy_width) {
        offsetX = self.tagBoxView.contentSize.width - self.tagBoxView.imy_width;
    }
    [self.tagBoxView setContentOffset:CGPointMake(offsetX, 0) animated:YES];
}

- (void)setupWithRawData:(NSDictionary *)rawData {
    if (self.rawData == rawData) {
        return;
    }
    self.rawData = rawData;
    self.bannerDatas = rawData[@"banners"];
    
    // 移除旧标识
    [self.tagBoxView imy_removeAllSubviews];
    
    
    // 头部图片
    {
        NSString *topURL = rawData[@"title_img"];
        CGSize topSize = [topURL imy_lastComponentOriginalImageSize];
        CGFloat topHeight = 0;
        if (topSize.width > 0 && topSize.height > 0) {
            topHeight = ceil(self.topImageView.imy_width / (topSize.width / topSize.height));
        }
        self.topImageView.imy_height = topHeight;
        [self.topImageView imy_setImageURL:topURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        
        // 曝光埋点
        NSString * const biInfoTag = rawData[@"key"];
        self.topImageView.imyut_eventInfo.showRadius = 1;
        self.topImageView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, 0, self.bi_floor];
        @weakify(self);
        self.topImageView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.bi_floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @0,
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
        
        [self.topImageView bk_whenTapped:^{
            @strongify(self);
            NSString * const uri = rawData[@"title_uri"];
            if (uri.length > 0) {
                [[IMYURIManager sharedInstance] runActionWithString:uri];
            }
            NSInteger action = uri.length > 0 ? 2 : 3;
            NSDictionary *gaParams = @{
                @"action" : @(action),
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.bi_floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(0),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        }];
    }
     
    // banner区域
    {
        NSDictionary *anyData = self.bannerDatas.firstObject;
        NSString *contentImageURL = anyData[@"banner_img"];
        CGSize contentSize = [contentImageURL imy_lastComponentOriginalImageSize];
        CGFloat contentWidth = SCREEN_WIDTH - 24;
        CGFloat contentHeight = 0;
        if (contentSize.width > 0 && contentSize.height > 0) {
            contentHeight = ceil(contentWidth / (contentSize.width / contentSize.height));
        }
        
        self.bannerView.imy_height = contentHeight;
        self.bannerView.imy_top = self.topImageView.imy_bottom;
        
        NSString *tagImageURL = anyData[@"tag_img"];
        CGSize tagSize = [tagImageURL imy_lastComponentOriginalImageSize];
        CGFloat tagHeight = IMYIntegerBy375Design(tagSize.height/3.0);
        
        self.tagBoxView.imy_height = tagHeight;
        self.tagBoxView.imy_bottom = self.bannerView.imy_bottom;
    }
    
    // 配置数据源
    {
        __block CGFloat lastLeftX = 0;
        NSMutableArray *bannerImageURLs = [NSMutableArray array];
        
        @weakify(self);
        [self.bannerDatas enumerateObjectsUsingBlock:^(NSDictionary *map, NSUInteger idx, BOOL * _Nonnull stop) {
            NSString *img = [NSString qiniuURL:map[@"banner_img"] resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
            [bannerImageURLs addObject:img.length > 0 ? img : @""];
            
            NSString *tagImageURL = map[@"tag_img"];
            CGSize tagSize = [tagImageURL imy_lastComponentOriginalImageSize];
            CGFloat tagWidth = IMYIntegerBy375Design(tagSize.width/3.0);
            
            YYAnimatedImageView *tagView = [YYAnimatedImageView new];
            tagView.frame = CGRectMake(lastLeftX, 0, tagWidth, self.tagBoxView.imy_height);
            tagView.tag = 100 + idx;
            [self.tagBoxView addSubview:tagView];
            
            // 曝光埋点
            NSString * const biInfoTag = map[@"key"];
            tagView.imyut_eventInfo.showRadius = 1;
            tagView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, idx, self.bi_floor];
            @weakify(self);
            tagView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSDictionary *gaParams = @{
                    @"action" : @(1),
                    @"event" : @"dy_hytab_gjgndh",
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"public_type" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                    @"floor" : @(self.bi_floor),
                };
                [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            };
            
            tagView.userInteractionEnabled = YES;
            [tagView bk_whenTapped:^{
                @strongify(self);
                // 切换 banner 显示位置
                self.bannerView.currentIndex = idx;
                // 点击埋点
                NSDictionary *gaParams = @{
                    @"action" : @(2),
                    @"event" : @"dy_hytab_gjgndh",
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"public_type" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                    @"floor" : @(self.bi_floor),
                };
                [IMYGAEventHelper postWithPath:@"/event" params:gaParams headers:nil completed:nil];
            }];
            
            lastLeftX = tagView.imy_right;
        }];
        
        // 设置banner
        self.bannerView.images = bannerImageURLs;
        
        // 滚动区域设置
        self.tagBoxView.contentSize = CGSizeMake(lastLeftX, 0);
        self.tagBoxView.scrollEnabled = (lastLeftX - 5 > self.tagBoxView.imy_width);
    }
    
    // 加载标签图
    [self refreshTagViews];
    
    self.edgeBoxView.imy_height = self.bannerView.imy_bottom;
}

+ (CGFloat)heightWithRawData:(NSDictionary *)rawData {
    CGFloat topHeight = 0;
    CGFloat contentHeight = 0;
    
    {
        NSString *titleImageURL = rawData[@"title_img"];
        CGSize topSize = [titleImageURL imy_lastComponentOriginalImageSize];
        CGFloat topWidth = SCREEN_WIDTH - 24;
        if (topSize.width > 0 && topSize.height > 0) {
            topHeight = ceil(topWidth / (topSize.width / topSize.height));
        }
    }
    
    {
        NSString *contentImageURL = [rawData[@"banners"] firstObject][@"banner_img"];
        CGSize contentSize = [contentImageURL imy_lastComponentOriginalImageSize];
        CGFloat contentWidth = SCREEN_WIDTH - 24;
        if (contentSize.width > 0 && contentSize.height > 0) {
            contentHeight = ceil(contentWidth / (contentSize.width / contentSize.height));
        }
    }
    
    CGFloat cellHeight = topHeight + contentHeight + 8;
    return cellHeight;
}

@end

