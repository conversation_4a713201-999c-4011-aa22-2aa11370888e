//
//  IMYMRHomeCardGiftItemBoxAdapter.m
//  ZZIMYMain
//
//  Created by ljh on 2025/9/8.
//

#import "IMYMRHomeCardGiftItemBoxAdapter.h"
#import <IMYBaseKit/IMYSGVipGiftItemViewV2.h>

@interface IMYMRHomeCardGiftItemBoxCell : UITableViewCell
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, copy) NSString *bi_key;
- (void)setupWithRawData:(NSDictionary *)rawData;
+ (CGFloat)heightWithRawData:(NSDictionary *)rawData;
@end

#pragma mark - adapter

@interface IMYMRHomeCardGiftItemBoxAdapter()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardGiftItemBoxAdapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardGiftItemBoxAdapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const BOOL is_gift_list = [cardData[@"is_gift_list"] boolValue];
    return is_gift_list;
}

- (NSString *)card_key {
    return @"gift_list";
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardGiftItemBoxCell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    NSArray * const gift_list = self.cardData[@"gift_list"];
    if (gift_list.count > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    const CGFloat cellHeight = [IMYMRHomeCardGiftItemBoxCell heightWithRawData:self.cardData];
    return cellHeight;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardGiftItemBoxCell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardGiftItemBoxCell.class];
    // 埋点需要用到的数据
    cell.bi_floor = module.sortedIndex;
    cell.bi_key = self.card_key;
    // 刷新UI
    [cell setupWithRawData:self.cardData];
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //...
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardGiftItemBoxCell ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UILabel *topTitleLabel;
@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, strong) NSDictionary *rawData;

@end

@implementation IMYMRHomeCardGiftItemBoxCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 200);
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.edgeBoxView = [UIView new];
    self.edgeBoxView.frame = CGRectMake(12, 0, SCREEN_WIDTH - 24, 44 + 44 + 12);
    [self.edgeBoxView imy_setBackgroundColor:kCK_White_AN];
    [self.edgeBoxView imy_drawAllCornerRadius:12];
    [self.contentView addSubview:self.edgeBoxView];
    
    self.topTitleLabel = [UILabel new];
    self.topTitleLabel.frame = CGRectMake(12, 0, self.edgeBoxView.imy_width - 24, 44);
    self.topTitleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [self.topTitleLabel imy_setTextColor:kCK_Black_A];
    [self.edgeBoxView addSubview:self.topTitleLabel];
    
    self.scrollView = [UIScrollView new];
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.frame = CGRectMake(0, 44, self.edgeBoxView.imy_width, 44);
    [self.edgeBoxView addSubview:self.scrollView];
}

- (void)setupWithRawData:(NSDictionary * const)rawData {
    if (self.rawData == rawData) {
        return;
    }
    self.rawData = rawData;
    
    // 移除旧图片集
    [self.scrollView imy_removeAllSubviews];
    
    // 标题
    {
        self.topTitleLabel.text = @"会员赠礼";
    }
    
    // 赠礼数组
    {
        NSArray * const gift_list = self.rawData[@"gift_list"];
        CGFloat const itemWidth = (gift_list.count > 1 ? IMYIntegerBy375Design(236) : IMYIntegerBy375Design(327));
        [gift_list enumerateObjectsUsingBlock:^(NSDictionary *map, NSUInteger idx, BOOL *stop) {
            
            IMYSGVipGiftItemViewV2 *itemView = [IMYSGVipGiftItemViewV2 new];
            itemView.cardStyle = 1;
            itemView.cardWidth = itemWidth;
            itemView.rawData = map;
            
            itemView.imy_left = 12 + idx * (itemWidth + 8);
            [self.scrollView addSubview:itemView];
            
            NSString * const biInfoTag = map[@"id"];
            
            // 曝光埋点
            itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, idx + 1, self.bi_floor];
            @weakify(self);
            itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                NSDictionary *gaParams = @{
                    @"action" : @1,
                    @"position" : @142,
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"floor" : @(self.bi_floor),
                    @"info_tag" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                };
                [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
            };
            
            itemView.userInteractionEnabled = YES;
            [itemView bk_whenTapped:^{
                @strongify(self);
                NSString * const uri = map[@"jump_url"];
                if (uri.length > 0) {
                    [[IMYURIManager sharedInstance] runActionWithString:uri];
                }
                NSInteger action = uri.length > 0 ? 2 : 3;
                NSDictionary *gaParams = @{
                    @"action" : @(action),
                    @"position" : @142,
                    @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                    @"floor" : @(self.bi_floor),
                    @"info_tag" : biInfoTag ?: @"",
                    @"index" : @(idx + 1),
                    @"sub_tab" : self.bi_key ?: @"",
                };
                [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
            }];
        }];
        
        // 滚动区域设置
        CGFloat const allWidth = (12 + gift_list.count * (itemWidth + 8) - 8 + 12);
        self.scrollView.contentSize = CGSizeMake(allWidth, 0);
        self.scrollView.scrollEnabled = (allWidth - 5 > self.scrollView.imy_width);
        self.scrollView.contentOffset = CGPointZero;
    }
}

+ (CGFloat)heightWithRawData:(NSDictionary *)rawData {
    NSArray * const gift_list = rawData[@"gift_list"];
    if (gift_list.count > 0) {
        // 大卡片底部间隙：8pt
        return 44 + 44 + 12 + 8;
    }
    return 0;
}

@end

