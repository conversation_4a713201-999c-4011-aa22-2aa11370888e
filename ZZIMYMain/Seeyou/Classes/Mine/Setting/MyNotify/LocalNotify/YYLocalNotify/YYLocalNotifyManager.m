//
// Created by i<PERSON> on 13-7-17.
//
//


#import "YYLocalNotifyManager.h"
#import "IMYPregnanceModel.h"
#import "NSArray+BlocksKit.h"
#import "NSDictionary+SY.h"
#import "NSUserDefaults+SYNotify.h"
#import "SYCNotificationModel.h"
#import "SYLocalNotifyInfo.h"
#import "SYMyNotifyTodayTipModel.h"
#import "SYNotifyDefine.h"
#import "SYNotifyInfoVC_2.h"
#import "SYPublicFun+Setting.h"
#import "SYPushNotiAppDelegate.h"
#import "YYLocalNotifyItem.h"
#import "YYLocalNotifyManager+AddNewItems.h"
#import "IMYRecordEventHelper.h"
#import "IMYRecordOvulateForecastManager.h"

@interface YYLocalNotifyManager ()
//发送的次数   延迟2秒去刷新本地推送 防止过度刷新
@property (assign, nonatomic) NSInteger sendCount;
@property (assign, nonatomic) BOOL needReset;
//控制提醒多次上报的问题
@property (nonatomic, copy) NSString *lastUserId;

@end

inline dispatch_queue_t YYNotifySharedIOQueue(void) {
    static dispatch_queue_t queue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        queue = dispatch_queue_create("YYLocalNotifyManager.queue", NULL);
    });
    return queue;
}

@implementation YYLocalNotifyManager

+ (void)initialize {
    if (self == YYLocalNotifyManager.class) {
        [YYLocalNotifyManager fixUserId];
        [YYLocalNotifyItem class];
    }
}

+ (void)fixUserId {
    if (HasLogin) {
        NSInteger notifyCount = [YYLocalNotify rowCountWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
        NSInteger itemCount = [YYLocalNotifyItem rowCountWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
        if (notifyCount < 4 && itemCount > 4) {
            //数据有出现异常的，即YYLocalNotify的userId为空的时候，重置提醒,4这里代表一个判定异常的值，目前发现有YYLocalNotify是有userId的，但大部分没有
            [YYLocalNotifyItem deleteWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
            [YYLocalNotify deleteWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
            [YYLocalNotify deleteWithWhere:@"userid = ''"];
        }
    }
}

#pragma mark - 保证线程安全

- (void)setItems:(NSArray *)array {
    IMYSafeArray *tmpItems = _items;
    if (array) {
        _items = [IMYSafeArray arrayWithArray:array];
        _items.isSingleLock = NO;
    } else {
        _items = nil;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), YYNotifySharedIOQueue(), ^{
        [tmpItems class];
    });
}

- (void)setNotifies:(NSArray *)array {
    IMYSafeArray *tmpNotifies = _notifies;
    if (array) {
        _notifies = [IMYSafeArray arrayWithArray:array];
        _notifies.isSingleLock = NO;
    } else {
        _notifies = nil;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), YYNotifySharedIOQueue(), ^{
        [tmpNotifies class];
    });
}

- (void)updateFromLastVersion:(IMYSafeArray *)array isItem:(BOOL)isItem {
    if (isItem) {
        NSArray *items = [NSKeyedUnarchiver unarchiveObjectWithFile:[YYLocalNotifyItem applicationSYPath]];
        if (items.count) {
            for (int i = 0; i < items.count; i++) {
                [NSObject imy_copyPropertiesFrom:items[i] to:array[i] ignoreNil:NO];
            }
        }
    } else {
        NSArray *notifies = [NSKeyedUnarchiver unarchiveObjectWithFile:[YYLocalNotify applicationSYPath]];
        if (notifies.count) {
            for (int i = 0; i < notifies.count; i++) {
                [NSObject imy_copyPropertiesFrom:notifies[i] to:array[i] ignoreNil:NO];
            }
        }
    }
}

+ (YYLocalNotifyManager *)sharedYYLocalNotifyManager {
    static YYLocalNotifyManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        dispatch_async(YYNotifySharedIOQueue(), ^{
            [self setupObservers];
        });
    }
    return self;
}

- (void)setupObservers {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeItem:) name:RemoveItemNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginSucced) name:LoginSucceedNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reshowCalendarView) name:kReshowCalendarView object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(modeChange) name:@"SYChangeUserModelNotifi" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(resetData) name:UserInfoSavedNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pregnacyDataChange) name:YYPregnacyDataChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(save) name:SYLanguageChangeNotify object:nil];
    
    [[IMYABTestManager sharedInstance].loadedSignal subscribeNext:^(id x) {
        [self resetData];
    }];
    
    [self pregnanChanged];
}

- (void)removeItem:(NSNotification *)notification {
    if (HasLogin) {
        self.items = [YYLocalNotifyItem searchWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID] orderBy:@"rowid desc" offset:0 count:1000];
    } else {
        self.items = [YYLocalNotifyItem searchWithWhere:@"userid = ''" orderBy:@"rowid desc" offset:0 count:1000];
    }
    self.notifies = [YYLocalNotify searchWithWhere:nil orderBy:nil offset:0 count:1000];
    IMY_POST_NOTIFY(ItemChangedNotification);
    [self save];
}

- (void)modeChange {
    imy_throttle_on_queue(0, @"YYLocalNotifyManager.modeChange", YYNotifySharedIOQueue(), ^{
        [self safe_modeChange];
    });
}

- (void)safe_modeChange {
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModePregnancy) {
        [NSUserDefaults standardUserDefaults].jingqi_notify = YES;
        [NSUserDefaults standardUserDefaults].jingqiEnd_notify = YES;
    }
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy) {
        [NSUserDefaults standardUserDefaults].todayAdvice_notify = YES;
        [self updateTodayAdviceFireTimeAfterChangeMode];
    }
    [self resetData];
}

- (void)pregnacyDataChange {
    imy_throttle_on_queue(0, @"YYLocalNotifyManager.pregnacyDataChange", YYNotifySharedIOQueue(), ^{
        [self safe_pregnacyDataChange];
    });
}

- (void)safe_pregnacyDataChange {
    [self updateTodayAdviceFireTimeAfterChangeMode];
    [self resetData];
}

// 没有主线程调用的情况，不用包起来
- (void)pregnanChanged {
    imy_throttle_on_queue(0, @"YYLocalNotifyManager.pregnanChanged", YYNotifySharedIOQueue(), ^{
        [self safe_pregnanChanged];
    });
}

- (void)safe_pregnanChanged {
    SYUserModelType userModelType = [SYUserHelper sharedHelper].s_user_ModelType;
    IMYSafeArray *items = self.items;
    switch (userModelType) {
        case SYUserModelTypePregnancy: {
            [items bk_each:^(YYLocalNotifyItem *sender) {
                if (sender.notifyType == YYJingQiKaishi) {
                    [NSUserDefaults standardUserDefaults].jingqi_notify = sender.active;
                    sender.active = NO;
                } else if (sender.notifyType == YYJingQiEnd) {
                    [NSUserDefaults standardUserDefaults].jingqiEnd_notify = sender.active;
                    sender.active = NO;
                } else if (sender.notifyType == YYBeiYun) {
                    [NSUserDefaults standardUserDefaults].beiyun_notify = sender.active;
                    sender.active = NO;
                } else if (sender.notifyType == YYWSJ) {
                    [NSUserDefaults standardUserDefaults].wsj_notify = sender.active;
                    sender.active = NO;
                } else if (sender.notifyType == YYNotifyTypeToday) {
                    [sender programResetActive:[NSUserDefaults standardUserDefaults].todayAdvice_notify];
                } else if (sender.notifyType == YYOvulate) {
                    [sender programResetActive:[NSUserDefaults standardUserDefaults].ovulate_notify];
                } else if (sender.notifyType == YYNotifyTypeTiWen) {
                    [sender programResetActive:[NSUserDefaults standardUserDefaults].tiwen_notify];
                }
            }];
        } break;
        case SYUserModelTypeForPregnant: {
            __block IMYTodayStatusType todayStatusType = [IMYCalendarUserHelper sharedHelper].today_type;
            [items bk_each:^(YYLocalNotifyItem *sender) {
                if (sender.notifyType == YYBeiYun) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].beiyun_notify];
                } else if (sender.notifyType == YYGai) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].gai_notify];
                } else if (sender.notifyType == YYJingQiKaishi) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].jingqi_notify];
                } else if (sender.notifyType == YYJingQiEnd) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].jingqiEnd_notify];
                } else if (sender.notifyType == YYWSJ) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].wsj_notify];
                } else if (sender.notifyType == YYOvulate) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].ovulate_notify];
                }
            }];
        } break;
        default: {
            __block IMYTodayStatusType todayStatusType = [IMYCalendarUserHelper sharedHelper].today_type;
            [items bk_each:^(YYLocalNotifyItem *sender) {
                if (sender.notifyType == YYJingQiKaishi) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].jingqi_notify];
                } else if (sender.notifyType == YYJingQiEnd) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].jingqiEnd_notify];
                } else if (sender.notifyType == YYWSJ) {
                    [sender programResetActive:todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].wsj_notify];
                } else if (sender.notifyType == YYBeiYun) {
                    sender.active = todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].beiyun_notify;
                } else if (sender.notifyType == YYOvulate) {
                    sender.active = todayStatusType == IMYTodayStatusUnknown ? NO : [NSUserDefaults standardUserDefaults].ovulate_notify;
                }
            }];
        } break;
    }
    [self save];
    if (![self.lastUserId isEqualToString:[IMYPublicAppHelper shareAppHelper].userid]) {
        self.lastUserId = [IMYPublicAppHelper shareAppHelper].userid;
        [self postNotifyStatus:items];
    }
}

- (IMYSafeArray *)notifies {
    if (_notifies.count == 0 || !_notifies) {
        NSString *where = [NSString stringWithFormat:@"userid = '%@'", HasLogin ? SY_USERID : @""];
        NSArray *notifies = [YYLocalNotify searchWithWhere:where];
        self.notifies = notifies;
    }
    return _notifies;
}

- (IMYSafeArray *)items {
    static BOOL isCalling = NO;
    if (isCalling) {
        return _items;
    }
    isCalling = YES;
    
    if (!_items) {
        NSInteger count = 0;
        if (HasLogin) {
            count = [YYLocalNotifyItem rowCountWithWhere:[NSString stringWithFormat:@"userid = '%@' and notifyType != 8", SY_USERID]];
        } else {
            count = [YYLocalNotifyItem rowCountWithWhere:@"userid = '' and notifyType != 8"];
        }

        //fix 1009308 各个身份下小提醒列表显示不全
        //数据异常的，删除数据库数据，重新往里面加数据
        NSMutableArray *filterArray = [YYLocalNotifyItem searchWithWhere:[NSString stringWithFormat:@"userid = '%@' and notifyType != 8", SY_USERID]];
        filterArray = [filterArray valueForKeyPath:@"@distinctUnionOfObjects.self.notifyType"];
        if (filterArray.count > 0 && filterArray.count != count) {
            [YYLocalNotifyItem deleteWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
            count = 0;
        }

        if (count == 0) {
            NSData *data1 = [NSData dataWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"YYLocalNotifyItem" ofType:@"json"]];
            self.items = [NSMutableArray arrayWithArray:[data1 toModels:[YYLocalNotifyItem class]]];
            [self updateFromLastVersion:_items isItem:YES];
            YYLocalNotifyItem *moveItem = _items[3];
            if (moveItem) {
                [_items removeObject:moveItem];
                [_items insertObject:moveItem atIndex:1];
            }
            for (NSInteger i = _items.count - 1; i >= 0; i--) {
                YYLocalNotifyItem *item = _items[i];
                if (HasLogin) {
                    item.userid = SY_USERID;
                }
                if (item.notifyType == YYBeiYun) {
                    item.title = IMYString(@"排卵日提醒");
                }
                [item saveToDB];
            }
            NSData *data2 = [NSData dataWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"YYLocalNotify" ofType:@"json"]];
            // 本地文件一定不为空，所以直接直接用 get 方法返回的对象
            self.notifies = [NSMutableArray arrayWithArray:[data2 toModels:[YYLocalNotify class]]];
            IMYSafeArray *notifies = self.notifies;
            [self updateFromLastVersion:notifies isItem:NO];
            [notifies bk_each:^(YYLocalNotify *sender) {
                if (HasLogin) {
                    sender.userid = SY_USERID;
                }
                [_items enumerateObjectsUsingBlock:^(YYLocalNotifyItem *item, NSUInteger idx, BOOL *stop) {
                    if (item.notifyType == sender.notifyType) {
                        sender.itemID = item.rowid;
                        sender.title = item.title;
                        *stop = YES;
                    }
                }];
                if (sender.notifyType == YYBeiYun) {
                    sender.itemID = 3;
                }
                [sender saveToDB];
            }];
        }
        if (HasLogin) {
            //登录状态的用户，还要判断是否是由旧版本升级
            self.items = [YYLocalNotifyItem searchWithWhere:[NSString stringWithFormat:@"userid='%@'", SY_USERID ?: @""] orderBy:@"rowid desc" offset:0 count:1000];
        } else {
            self.items = [YYLocalNotifyItem searchWithWhere:@"userid = ''" orderBy:@"rowid desc" offset:0 count:1000];
        }
    }
    
    [self checkNewItems];
    [self check5_5NewItem];
    [self checkTodayAdviceItem];
    [self checkOvulateItems];
    [self checkTiwenItem];
    [self checkWeightItem];
    [self checkSTDItem];
    [self checkYMBItem];
    [self itemsSort];
    
    IMYSafeArray *returnItems = [IMYSafeArray array];
    SYUserModelType userModelType = [SYUserHelper sharedHelper].s_user_ModelType;
    if (userModelType == SYUserModelTypePregnancy) {
        NSArray *array = [_items bk_select:^BOOL(YYLocalNotifyItem *evaluatedObject) {
            YYNotifyType notifyType = evaluatedObject.notifyType;
            //备孕用户计划怀孕时起至怀孕后的前三个月末后叶酸片提醒消失。
            if (notifyType == YYYeSuan) {
                IMYPregnanceModel *model = [IMYPregnanceModel getLastPregnancyModel];
                NSInteger daydiff = [model.startDate getDayDiff:[NSDate imy_today]];
                NSInteger month = daydiff / 28;
                if (month <= 2) {
                    return YES;
                }
                return NO;
            } else if (notifyType == YYGai) {
                //怀孕状态用户在4个月后增加钙片提醒
                IMYPregnanceModel *model = [IMYPregnanceModel getLastPregnancyModel];
                NSInteger daydiff = [model.startDate getDayDiff:[NSDate imy_today]];
                NSInteger month = daydiff / 28;
                if (month >= 4) {
                    return YES;
                }
                return NO;
            } else if (notifyType == YYJingQiKaishi || notifyType == YYBeiYun || notifyType == YYWSJ || notifyType == YYJingQiEnd || notifyType == YYNotifyTypeTiWen) {
                return NO;
            }
            return YES;
        }];
        [returnItems addObjectsFromArray:array];
    } else if (userModelType == SYUserModelTypeForPregnant) {
        NSArray *array = [_items bk_select:^BOOL(YYLocalNotifyItem *evaluatedObject) {
            YYNotifyType notifyType = evaluatedObject.notifyType;
            if (notifyType == YYGai) {
                return NO;
            }
            if (notifyType == YYNotifyTypeToday) {
                return NO;
            }
            if (notifyType == YYNotifyTypeWeight) {
                return NO;
            }
            if (notifyType == YYNotifyTypeYMB) {
                return NO;
            }
            if (notifyType == YYNotifyTypeSTD) {
                return NO;
            }
            return YES;
        }];
        [returnItems addObjectsFromArray:array];
    } else {
        NSArray *array = [_items bk_select:^BOOL(YYLocalNotifyItem *evaluatedObject) {
            YYNotifyType notifyType = evaluatedObject.notifyType;
            if (notifyType == YYYeSuan || notifyType == YYGai || notifyType == YYNotifyTypeToday || notifyType == YYNotifyTypeTiWen) {
                return NO;
            }
            if (notifyType == YYNotifyTypeWeight) {
                return NO;
            }
            if (notifyType == YYNotifyTypeYMB) {
                return NO;
            }
            if (notifyType == YYNotifyTypeSTD) {
                return NO;
            }
            return YES;
        }];
        [returnItems addObjectsFromArray:array];
    }
    
    isCalling = NO;
    
    return returnItems;
}

- (void)resetFireDates {
    [self checkChiYao];
    [self checkCustomNotify];
    IMYSafeArray *items = self.items;
    [items bk_each:^(YYLocalNotifyItem *item) {
        [self resetFireDatesOfItem:item];
    }];
    [self resetLocalNotifications];
}

- (void)resetFireDatesOfItem:(YYLocalNotifyItem *)item {
    NSDate *now = [NSDate date];
    IMYMensesModel *mensesModel = [IMYMensesDao getLastMenses];
    IMYCalendarUserHelper *userHelper = [IMYCalendarUserHelper sharedHelper];
    switch (item.notifyType) {
        case YYJingQiKaishi: {
            for (YYLocalNotify *notify in item.localNotifies) {
                //如果没有经期记录则不进行计算
                if (notify.active && [IMYMensesModel getMensesAll].count > 0) {
                    //月经开始通知，如果不是预测期则按照常规计算，fireDate 为 经期开始的第一天+周期-提前N天的时间
                    //如果是预测期，则说明今天是月经的第一天，把上面的经期开始第一天替换为今天
                    notify.repeatInterval = (NSCalendarUnit)0;
                    notify.alertBody = IMYString(@"从今天开始注意不要吃生冷辛辣食物，如果大姨妈已经来了，记得在日历中添加开始日期，我们会提供更精准的预测~");
                    if (userHelper.today_type == IMYTodayStatusForecast) {
                        notify.fireDate = [[NSDate imy_today] dateBy_SY_AddingTimeInterval:(-1 * notify.aheadOfDays) * 86400 + [self secondsFromString:notify.fireTimeString]];
                    } else {
                        notify.fireDate = [[NSDate imy_today] dateBy_SY_AddingTimeInterval:(userHelper.today_diff - notify.aheadOfDays) * 86400 + [self secondsFromString:notify.fireTimeString]];
                        if ([notify.fireDate isEarlierThanDate:now]) {
                            notify.fireDate = [notify.fireDate dateByAddingDays:userHelper.avgParsInterval];
                        }
                    }
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYJingQiEnd: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    IMYMensesModel *lastMense = [IMYMensesDao getLastMenses];
                    if (lastMense) {
                        notify.repeatInterval = 0;
                        notify.alertBody = IMYString(@"建议及时记录月经结束，可以点击分析查看本次经期得分和经期知识");
                        if (userHelper.today_type == IMYTodayStatusForecast) {
                            notify.fireDate = [[NSDate imy_today] dateBy_SY_AddingTimeInterval:(userHelper.parsMensesDay - 1) * 86400 + [self secondsFromString:notify.fireTimeString]];
                        } else {
                            notify.fireDate = [lastMense.menddate dateByAddingTimeInterval:[self secondsFromString:notify.fireTimeString]];
                            BOOL isEnd = (!lastMense.isForecast) && ([lastMense.menddate compare:[NSDate imy_today]] == NSOrderedSame);
                            if ([notify.fireDate isEarlierThanDate:now] || isEnd) {
                                notify.fireDate = [[[NSDate imy_today] dateByAddingDays:userHelper.today_diff + userHelper.parsMensesDay - 1] dateByAddingTimeInterval:[self secondsFromString:notify.fireTimeString]];
                            }
                        }
                        [self preScheduleLocalNotificationWithNotify:notify];
                    } else {
                        notify.repeatInterval = 0;
                        [self preScheduleLocalNotificationWithNotify:notify];
                    }
                }
            }
            break;
        }
        case YYWSJ: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = (NSCalendarUnit)0;
                    notify.totalDays = userHelper.parsMensesDay;
                    //如果在经期内，判断是否过时了，是的话添加一天
                    if (userHelper.today_type == IMYTodayStatusPhysiological) {
                        notify.fireDate = notify.fireDate.todayWithHourAndMinute;
                    } else {
                        //不在经期内，全部重置
                        notify.fireDate = [mensesModel.mstartdate dateBy_SY_AddingTimeInterval:(userHelper.avgParsInterval + 1) * 86400 + [self secondsFromString:notify.fireTimeString]];
                    }
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYBeiYun: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    //备孕通知 即排卵日，fireDate = 最近一次月经开始时间+周期-14天-提前N天
                    //注意事项和上面一样
                    notify.repeatInterval = (NSCalendarUnit)0;
                    if (userHelper.today_type == IMYTodayStatusForecast) {
                        NSInteger ovulationDay = 14;
                        if ([IMYRecordOvulateForecastManager ovulateForecastEnabled]) {
                            ovulationDay = [IMYCalendarUserHelper sharedHelper].cloudOvulationDay;
                        }
                        notify.fireDate = [[NSDate imy_today] dateBy_SY_AddingTimeInterval:(userHelper.avgParsInterval - ovulationDay - notify.aheadOfDays) * 86400 + [self secondsFromString:notify.fireTimeString]];
                    } else {
                        notify.fireDate = [[NSDate imy_today] dateByAddingTimeInterval:(userHelper.today_diff_ovulatory_day - notify.aheadOfDays) * 86400 + [self secondsFromString:notify.fireTimeString]];
                    }
                    if ([notify.fireDate isEarlierThanDate:now]) {
                        notify.fireDate = [notify.fireDate dateByAddingDays:userHelper.avgParsInterval];
                    }
                    notify.alertBody = IMYString(@"近期怀孕几率较高，避孕和备孕都要做好准备哦~");

                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYOvulate: {
            NSArray *notifies = item.localNotifies;
            for (YYLocalNotify *notify in notifies) {
                BOOL isPast = [notify.fireDate isEarlierThanDate:now];
                if (isPast) {
                    /// 前面可以确保 startDate 一定是至少从今天开始的
                    notify.fireDate = [notify.fireDate dateByAddingDays:1];
                }
                notify.alertBody = IMYString(@"易孕期不要忘记监测排卵情况，科学使用排卵试纸，祝您好孕~");
                [self preScheduleLocalNotificationWithNotify:notify];
            }
        } break;
        case YYBaBeiShui: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = NSCalendarUnitDay;
                    notify.fireDate = notify.fireDate.todayWithHourAndMinute;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYNotifyTypeYMB:
        case YYNotifyTypeWeight:
        case YYFuMianMo: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = NSCalendarUnitWeekOfYear;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYYeSuan: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = NSCalendarUnitWeekOfYear;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYGai: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = NSCalendarUnitWeekOfYear;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYNotifyTypeSTD:
        case YYChiYao: {
            __block NSInteger day = 0;
            YYLocalNotify *chiyaoNotify = item.localNotifies.lastObject;
            if (chiyaoNotify.item.activeDate.isToday && [chiyaoNotify.fireDate isEarlierThanDate:[NSDate date]] && chiyaoNotify.totalDays == 1) {
                day = 1;
            }
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    //吃药提醒
                    notify.fireDate = [notify.fireDate.todayWithHourAndMinute dateByAddingDays:day];
                    notify.repeatInterval = (NSCalendarUnit)0;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYCustom: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    if (notify.weekDays == kNone) {
                        notify.repeatInterval = (NSCalendarUnit)0;
                    }
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYNotifyTypePlan: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    if (notify.repeatType == YYRepeatTypeNone) {
                        notify.repeatInterval = (NSCalendarUnit)0;
                    }
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYNotifyTypeToday: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    IMYPregnanceModel *pModel = [IMYPregnanceModel getLastPregnancyModel];
                    NSInteger daydiff = [pModel.startDate getDayDiff:notify.fireDate];
                    daydiff = MAX(1, daydiff);
                    daydiff = MIN(294, daydiff);
                    IMYSwitchModel *door = [[IMYDoorManager sharedManager] switchForType:@"today_notice_jump"];
                    if ([door.dataDictionary[@"type"] integerValue] == 2) {
                        NSInteger week = daydiff / 7;
                        NSInteger day = daydiff % 7;
                        if (day > 0) {
                            notify.alertBody = [NSString stringWithFormat:IMYString(@"今天处于孕%ld周%ld天，当前宝宝重点发育哪些部位，准妈妈应该补充哪些营养？快来看看吧~"), week, day];
                        } else {
                            notify.alertBody = [NSString stringWithFormat:IMYString(@"今天处于孕%ld周，当前宝宝重点发育哪些部位，准妈妈应该补充哪些营养？快来看看吧~"), week];
                        }
                        notify.otherInfo = @{@"todayJump": @2};
                    } else {
                        SYMyNotifyTodayTipModel *today = [SYMyNotifyTodayTipModel searchSingleWithWhere:[NSString stringWithFormat:@"userMode = 1 and periodDay = %ld", daydiff] orderBy:nil];
                        if (today) {
                            notify.alertBody = today.nofityWords;
                            notify.otherInfo = @{@"topicId": @(today.topicId),
                                                 @"topicUrl": today.topicURL,
                                                 @"todayJump": @1};
                        } else {
                            notify.alertBody = IMYString(@"看看今日建议吧！");
                        }
                    }
                    notify.repeatInterval = (NSCalendarUnit)0;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        case YYNotifyTypeTiWen: {
            for (YYLocalNotify *notify in item.localNotifies) {
                if (notify.active) {
                    notify.repeatInterval = NSCalendarUnitWeekOfYear;
                    [self preScheduleLocalNotificationWithNotify:notify];
                }
            }
            break;
        }
        default: {
            break;
        }
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:@"NotifyReload" object:nil];
}

- (double)resetLocalXDelay {
    return 5;
}

- (void)resetLocalNotifications {
    imy_throttle_on_queue(self.resetLocalXDelay, @"YYLocalNotifyManager.resetLocalNotifications", YYNotifySharedIOQueue(), ^{
        [self resetLocalNotifications_safe];
    });
}

- (void)resetLocalNotifications_safe {
    IMYSafeArray *array = self.items;
    [array bk_each:^(id sender) {
        [sender saveToDB];
    }];
    IMYSafeArray *array1 = self.notifies;
    [array1 bk_each:^(id sender) {
        [sender saveToDB];
    }];
    BOOL isInitRingName = [[IMYUserDefaults standardUserDefaults] boolForKey:@"IsInitRingName"];
    if (!isInitRingName) {
        [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:@"IsInitRingName"];
        [SYPublicFun setNotifyRingName:IMYString(@"系统默认")];
    }
    NSString *notifyRingName = IMYString(@"系统默认");
    NSString *ringName = [SYPublicFun notifyRingName];
    if ([ringName isEqualToString:IMYString(@"系统默认")]) {
        notifyRingName = UILocalNotificationDefaultSoundName;
    } else {
        notifyRingName = [NSString stringWithFormat:@"%@.m4a", [SYPublicFun notifyRingName]];
    }
    [self cancelLocalNotifications];
    
    NSMutableArray *allLocalNotifications = [[NSMutableArray alloc] init];
    NSMutableArray *sortedNotifies = [[NSMutableArray alloc] init];
    IMYSafeArray *activeItems = [IMYSafeArray new];
    [array bk_each:^(YYLocalNotifyItem *obj) {
        [activeItems addObjectsFromArray:obj.localNotifies];
    }];
    [activeItems bk_each:^(YYLocalNotify *localNotify) {
        for (NSDate *fireDate in localNotify.fireDates) {
            YYLocalNotify *notify = [[YYLocalNotify alloc] init];
            [NSObject imy_copyPropertiesFrom:localNotify to:notify ignoreNil:NO];
            if (notify.active) {
                notify.fireDate = fireDate;
                [sortedNotifies addObject:notify];
            }
        }
    }];

    [sortedNotifies sortUsingComparator:^NSComparisonResult(YYLocalNotify *obj1, YYLocalNotify *obj2) {
        return [obj1.fireDate compare:obj2.fireDate];
    }];
    [self checkTodayAdviceLocalNotify:sortedNotifies];
    NSMutableArray *localNotifications = [[NSMutableArray alloc] init];
    BOOL hasBeiyunNotify = NO;
    for (NSUInteger i = 0; i < sortedNotifies.count || localNotifications.count == 64; i++) {
        YYLocalNotify *notify = [sortedNotifies imy_objectAtIndex:i];
        if (notify.active) {
            if (notify.notifyType == YYJingQiEnd) {
                if (![IMYMensesDao getMensesWithDate:[NSDate imy_today]]) {
                    continue;
                }
            }
            if ([notify.fireDate compare:[NSDate date]] == NSOrderedAscending) {
                continue;
            }
            if (notify.notifyType == YYBeiYun) {
                //bug(用户反馈一个时间点收到26条相同的小提醒) https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001034721
                if (hasBeiyunNotify) {
                    continue;
                } else {
                    hasBeiyunNotify = YES;
                }
            }
            UILocalNotification *localNotification = [[UILocalNotification alloc] init];
            localNotification.soundName = notifyRingName;
            localNotification.fireDate = notify.fireDate;
            if (IOS9) {
                localNotification.alertTitle = imy_isBlankString([notify alertTitle]) ? nil : [notify alertTitle];
            }
            localNotification.alertBody = notify.alertBody.length > 0 ? notify.alertBody : notify.subtitle;
            localNotification.repeatInterval = notify.repeatInterval;
            NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
            [userInfo addEntriesFromDictionary:@{@"notifyType": @(notify.notifyType),
                                                 @"title": notify.alertTitle,
                                                 @"itemID": @(notify.itemID)}];
            if (notify.otherInfo) {
                [userInfo addEntriesFromDictionary:notify.otherInfo];
            }
            localNotification.userInfo = userInfo;
            [localNotifications addObject:localNotification];
        }
    }
    IMYMensesModel *model = [IMYMensesDao getMensesWithDate:[NSDate imy_today]];
    if (model && model.isForecast) {
        UILocalNotification *menseEndNotification = [UILocalNotification new];
        menseEndNotification.soundName = notifyRingName;
        NSString *endDate = [[model.mstartdate dateByAddingDays:13] imy_getOnlyDateString];
        endDate = [NSString stringWithFormat:@"%@ 08:00:00", endDate];
        menseEndNotification.fireDate = [endDate imy_getDateTime];
        menseEndNotification.alertBody = IMYString(@"您的大姨妈已经持续14天咯~如果已经走喽，请及时标记结束哦~");
        // 不加个key，会导致本地push 一直不被删除！
        menseEndNotification.userInfo = @{
            @"k" : @"v",
        };
        [localNotifications addObject:menseEndNotification];
    }
    [allLocalNotifications addObjectsFromArray:localNotifications];
    [allLocalNotifications sortUsingComparator:^NSComparisonResult(UILocalNotification *obj1, UILocalNotification *obj2) {
        return [obj1.fireDate compare:obj2.fireDate];
    }];
    if (allLocalNotifications.count) {
        //将通知批量进行替换
        __block NSInteger count = 0;
        NSMutableArray *notifications = [[NSMutableArray alloc] initWithCapacity:64];
        [allLocalNotifications enumerateObjectsUsingBlock:^(UILocalNotification *localNotification, NSUInteger idx, BOOL *stop) {
            localNotification.timeZone = [NSTimeZone localTimeZone];
            if ([localNotification.fireDate isLaterThanDate:[NSDate date]] || localNotification.repeatInterval != 0) {
                [notifications addObject:localNotification];
                count += 1;
                if (count >= 64) {
                    *stop = YES;
                }
            }
        }];
        imy_asyncMainExecuteBlock(^{
            [UIApplication sharedApplication].scheduledLocalNotifications = notifications;
            [[NSNotificationCenter defaultCenter] postNotificationName:@"LocalNotificationsDidChange" object:nil];
        });

    };
}

- (void)preScheduleLocalNotificationWithNotify:(YYLocalNotify *)notify {
    if (notify.active) {
        notify.fireDate = [notify.fireDate secondToZero];
        if (notify.notifyType == YYChiYao) {
            if (notify.totalDays > 0) {
                //dayDiff 是上次开启这个提醒的时间距离现在几天过去了，在设置fireDate的循环中 每次都是把上面处理过的fireDate+i天-过去了dayDiff天
                NSInteger dayDiff = [[[NSDate imy_getCNCalendar] components:NSCalendarUnitDay fromDate:notify.item.activeDate toDate:[NSDate date] options:0] day];
                NSMutableArray *fireDates = [[NSMutableArray alloc] init];
                for (int i = 0; i < notify.totalDays; i++) {
                    [fireDates addObject:[notify.fireDate dateByAddingDays:i - dayDiff]];
                }
                notify.fireDates = fireDates;
            }
        } else if (notify.notifyType == YYWSJ) {
            NSInteger dayDiff = 0;
            if ([IMYCalendarUserHelper sharedHelper].today_type == IMYTodayStatusPhysiological) {
                dayDiff = [[[NSDate imy_getCNCalendar] components:NSCalendarUnitDay fromDate:[IMYMensesDao getLastMenses].mstartdate toDate:[NSDate date] options:0] day];
            }
            NSMutableArray *fireDates = [[NSMutableArray alloc] init];
            for (int i = 0; i < MIN(5, notify.totalDays); i++) {
                [fireDates addObject:[notify.fireDate dateByAddingDays:i - dayDiff]];
            }
            notify.fireDates = fireDates;
        } else if (notify.notifyType == YYNotifyTypePlan) {
            NSMutableArray *fireDates = @[].mutableCopy;
            if (notify.repeatType == YYRepeatTypeNone) {
                NSDate *date = [notify.fireDate dateBySubtractingDays:notify.aheadOfDays];
                fireDates = @[date];
            } else {
                NSInteger addCount = 0;
                if (notify.repeatType == YYRepeatYear) {
                    addCount = 1;
                } else if (notify.repeatType == YYRepeatMonth) {
                    addCount = 2;
                } else if (notify.repeatType == YYRepeatWeek) {
                    addCount = 5;
                }
                
                void(^addAction)(NSInteger) = ^(NSInteger i){
                    NSDate *date = [IMYRecordEventHelper eventWillHappenForId:notify.customID count:i];
//                    [self eventWillHappen:notify.repeatType eventDate:notify.fireDate isLunar:notify.isLunar count:i];
                    NSDate *theDate = [date dateBySubtractingDays:notify.aheadOfDays];
                    if (date &&
                        [theDate isLaterOrEqualToDate:NSDate.date] &&
                        ([date.imy_getDateZeroTime isEarlierThanDate:notify.endDate.imy_getDateZeroTime] || notify.endDate == nil) &&
                        ![notify.skipDates containsObject:date.imy_getDateZeroTime]) {
                        
                        [fireDates addObject:theDate];
                        if (notify.isLunar) {//下个月是润月就加上
                            NSDate *lunarDate = [date lunar_dateAfterMonths:1];
                            if (lunarDate.isLeapMonth) {
                                lunarDate = [lunarDate dateBySubtractingDays:notify.aheadOfDays];
                                [fireDates addObject:lunarDate];
                            }
                        }
                    }
                };
                
                /// 【1】 -> 有提前几天提醒，导致数量不够；
                /// 【notify.skipDates.count】 -> 跳过导致数量不够
                for (NSInteger i = 1; i <= addCount + notify.skipDates.count + 1; i++) {
                    if(fireDates.count < addCount) {
                        addAction(i);
                    } else {
                        break;
                    }
                }
            }
            notify.fireDates = fireDates;
        } else if (notify.weekDays != kNone) {
            NSDate *now = [NSDate date];
            NSCalendar *calendar = [NSDate imy_getCNCalendar];
            NSUInteger unitFlags = NSCalendarUnitDay | NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfYear | NSCalendarUnitWeekday | NSCalendarUnitWeekdayOrdinal | NSCalendarUnitQuarter;
            NSDateComponents *comps = [calendar components:unitFlags fromDate:now];
            [comps setHour:notify.fireDate.YYHour];
            [comps setMinute:notify.fireDate.YYMinute];
            [comps setSecond:0];

            if (notify.weekDays == 254) {
                NSDate *newFireDate = [calendar dateFromComponents:comps];
                if ([newFireDate isEarlierThanDate:now]) {
                    newFireDate = [newFireDate dateByAddingDays:1];
                }
                notify.fireDate = newFireDate;
                notify.fireDates = @[notify.fireDate];
                notify.repeatInterval = NSCalendarUnitDay;
            } else {
                NSMutableArray *fireDates = [[NSMutableArray alloc] init];
                NSInteger weekDay = comps.weekday;
                for (NSInteger i = 1; i < 8; i++) {
                    if ((notify.weekDays & 1 << i) > 0) {
                        NSInteger temp = i - weekDay;
                        NSInteger day = temp >= 0 ? temp : temp + 7;
                        NSDate *newFireDate = [[calendar dateFromComponents:comps] dateByAddingDays:day];
                        if ([newFireDate isEarlierThanDate:now]) {
                            newFireDate = [newFireDate dateByAddingDays:7];
                        }
                        [fireDates addObject:newFireDate];
                    }
                }
                notify.fireDates = fireDates;
            }
        } else {
            notify.fireDates = @[notify.fireDate];
        }
        NSDateComponents *dateComponent = [[NSDate imy_getCNCalendar] components:NSCalendarUnitHour | NSCalendarUnitMinute fromDate:notify.fireDate];
        notify.fireTimeString = [NSString stringWithFormat:@"%02ld:%02ld", (long)dateComponent.hour, (long)dateComponent.minute];
    }
}

- (void)checkCustomNotify {
    NSArray *customNotifies = [self.notifies.array filter:^BOOL(YYLocalNotify *evaluatedObject) {
        if ([evaluatedObject isKindOfClass:[YYLocalNotify class]]) {
            YYLocalNotify *notify = (YYLocalNotify *)evaluatedObject;
            return notify.notifyType == YYCustom;
        }
        return NO;
    }];
    __block NSDate *now = [NSDate date];
    [customNotifies bk_each:^(YYLocalNotify *notify) {
        if (notify.weekDays == kNone) {
            if ([notify.fireDate isEarlierThanDate:now]) {
                [notify resetActive:NO];
            }
        }
    }];
    
    [self checkCalendarCustomEvent];
}

-(void)checkCalendarCustomEvent {
    NSArray *customNotifies = [self.notifies.array filter:^BOOL(YYLocalNotify *evaluatedObject) {
        if ([evaluatedObject isKindOfClass:[YYLocalNotify class]]) {
            YYLocalNotify *notify = (YYLocalNotify *)evaluatedObject;
            return notify.notifyType == YYNotifyTypePlan;
        }
        return NO;
    }];
    __block NSDate *now = [NSDate date];
    [customNotifies bk_each:^(YYLocalNotify *notify) {
        if (notify.repeatType == YYRepeatTypeNone) {
            if ([notify.fireDate isEarlierThanDate:now]) {
                [notify resetActive:NO];
            }
        }
    }];
}

- (void)checkChiYao {
    NSArray *chiyaos = [self.notifies.array filter:^BOOL(YYLocalNotify *evaluatedObject) {
        if ([evaluatedObject isKindOfClass:[YYLocalNotify class]]) {
            YYLocalNotify *notify = (YYLocalNotify *)evaluatedObject;
            return notify.notifyType == YYChiYao;
        }
        return NO;
    }];
    YYLocalNotify *localNotify = chiyaos.lastObject;
    __block BOOL needClose = YES;
    [chiyaos enumerateObjectsUsingBlock:^(YYLocalNotify *obj, NSUInteger idx, BOOL *stop) {
        if (obj.active) {
            needClose = NO;
            *stop = YES;
        }
    }];
    if (needClose) {
        localNotify.item.active = NO;
        return;
    }
    if (localNotify.item.active) {
        //如果创建时间是今天，并且持续天数只有一天，那么则判断最后一条是否超过当前时间
        if (localNotify.item.activeDate.isToday && localNotify.totalDays == 1) {
            return;
        }
        NSDate *lastFireDate = [localNotify.item.activeDate dateByAddingDays:localNotify.totalDays - 1];
        NSCalendar *calendar = [NSDate imy_getCNCalendar];
        NSUInteger unitFlags = NSCalendarUnitDay | NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond | NSCalendarUnitWeekOfYear | NSCalendarUnitWeekday | NSCalendarUnitWeekdayOrdinal | NSCalendarUnitQuarter;
        NSDateComponents *comps = [calendar components:unitFlags fromDate:lastFireDate];
        comps.hour = localNotify.fireDate.YYHour;
        comps.minute = localNotify.fireDate.YYMinute;
        comps.second = 0;
        lastFireDate = [calendar dateFromComponents:comps];
        if ([lastFireDate isEarlierThanDate:[NSDate date]]) {
            localNotify.item.active = NO;
            return;
        }
    }
}

- (void)save {
    // 2秒阈值，防止短时间内大量写入
    imy_throttle_on_queue(2.0, @"YYLocalNotifyManager.save", YYNotifySharedIOQueue(), ^{
        [self resetFireDates];
    });
}

- (void)saveWithNotify:(YYLocalNotify *)notify callback:(BKBlock)callback {
    [self saveWithItem:notify.item callback:callback];
}

- (void)saveWithItem:(YYLocalNotifyItem *)item callback:(BKBlock)callback {
    item.operateDate = NSDate.date;
    IMYSafeArray *items = _items;
    [items enumerateObjectsUsingBlock:^(YYLocalNotifyItem *obj, NSUInteger idx, BOOL *stop) {
        if (item.rowid == obj.rowid) {
            [NSObject imy_copyPropertiesFrom:item to:obj ignoreNil:NO];
            *stop = YES;
        }
    }];
    dispatch_async(YYNotifySharedIOQueue(), ^{
        [self checkChiYao];
        [self checkCustomNotify];
        [self resetFireDatesOfItem:item];
        dispatch_async(dispatch_get_main_queue(), ^{
            if (callback) {
                callback();
            }
        });
        [self testOthers];
    });
}

- (void)testOthers {
    [self resetOthers];
}

- (void)resetOthers {
    [self resetLocalNotifications];
}

- (int)secondsFromString:(NSString *)string {
    if ([string isKindOfClass:[NSString class]]) {
        NSArray *array = [string componentsSeparatedByString:@":"];
        if (array.count == 2) {
            int sum = [array[0] intValue] * 3600 + [array[1] intValue] * 60;
            return sum;
        }
    }
    return 0;
}

+ (void)allSave {
    [[YYLocalNotifyManager sharedYYLocalNotifyManager] save];
}

+ (void)saveWithNotify:(YYLocalNotify *)notify callback:(BKBlock)callback {
    [[YYLocalNotifyManager sharedYYLocalNotifyManager] saveWithNotify:notify callback:callback];
}

+ (void)saveWithItem:(YYLocalNotifyItem *)item callback:(BKBlock)callback {
    [[YYLocalNotifyManager sharedYYLocalNotifyManager] saveWithItem:item callback:callback];
}

+ (void)showNotifyInfoWithType:(NSInteger)type {
    [[LKAppManager sharedAppManager] runBlockInRootIsTabbar:^{
        if (type == YYJingQiKaishi || type == YYJingQiEnd) {
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = SYTabBarIndexTypeRecord;
        } else if (type == YYNotifyTypeToday) {

        } else if (type == YYNotifyTypeYMB) {

        } else if (type == YYNotifyTypeWeight) {
            //YYLocalNotifyManager+ResponseNotify里 -showNotifyInfo: 已做了跳转处理
        } else if (type == YYNotifyTypePlan) {
            
        } else {
            SYNotifyInfoVC_2 *notifyInfoVC = [[SYNotifyInfoVC_2 alloc] init];
            [[SYBaseTabBarController shareTabbarController] imy_present:notifyInfoVC animated:NO];
        }
    }];
}

#define LocalNotifyFilePath [LKDBUtils getPathForDocuments:@"localNotificationstest.plist" inDir:@"localNotify"]

+ (NSArray *)scheduledLocalNotifications {
    NSArray *array = [NSArray arrayWithContentsOfFile:LocalNotifyFilePath];
    NSMutableArray *notifyList = [NSMutableArray arrayWithCapacity:[array count]];
    for (NSDictionary *dict in array) {
        UILocalNotification *notification = [[UILocalNotification alloc] init];
        notification.fireDate = dict[@"fireDate"];
        NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
        userInfo[@"notifyType"] = dict[@"notifyType"];
        userInfo[@"title"] = dict[@"title"];
        if ([dict safeObjectForKeyNotNull:@"itemID"]) {
            userInfo[@"itemID"] = [dict safeObjectForKeyNotNull:@"itemID"];
        }
        notification.userInfo = userInfo;
        notification.alertBody = dict[@"alertBody"];
        [notifyList addObject:notification];
    }
    return notifyList;
}

+ (BOOL)saveScheduledLocalNotifications:(NSArray *)notifications {
    NSMutableArray *saveArray = [NSMutableArray arrayWithCapacity:[notifications count]];
    for (UILocalNotification *notification in notifications) {
        NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
        dict[@"notifyType"] = [notification.userInfo safeObjectForKeyNotNull:@"notifyType"];
        dict[@"title"] = [notification.userInfo safeObjectForKeyNotNull:@"title"];
        dict[@"alertBody"] = notification.alertBody;
        dict[@"fireDate"] = notification.fireDate;
        
        if ([notification.userInfo safeObjectForKeyNotNull:@"itemID"]) {
            dict[@"itemID"] = [notification.userInfo safeObjectForKeyNotNull:@"itemID"];
        }
        [saveArray addObject:dict];
    }
    return [saveArray writeToFile:LocalNotifyFilePath atomically:YES];
}

+ (void)saveNotifyInfoWithLocalNotifications:(NSArray *)localNotifications {
    SYLocalNotifyInfo *lastNotifyInfo = nil;
    NSDate *now = [NSDate date];
    for (UILocalNotification *notification in localNotifications) {
        NSDictionary *userInfo = notification.userInfo;
        const NSInteger type = [userInfo[@"notifyType"] integerValue];
        if (type == YYTask) {

        } else {
            //if(([notification.fireDate isEarlierThanDate:now] && [notification.fireDate isLaterThanDate:lastActive]) || ([notification.fireDate isEqualToDate:lastActive]))
            //if ([notification.fireDate compare:now] != NSOrderedDescending && [notification.fireDate isLaterThanDate:lastActive]) {
            //fireDate比当前时间早的，判断为已提醒过的本地推送
            if ([notification.fireDate compare:now] != NSOrderedDescending) {
                SYLocalNotifyInfo *localNotifyInfo = [SYLocalNotifyInfo localNotifyInfoWithLocalNotification:notification];
                localNotifyInfo.userid = [SYUserHelper sharedHelper].userid;
                if (localNotifyInfo.notifyType != YYTask) {
                    [localNotifyInfo saveToDB];
                }
                lastNotifyInfo = localNotifyInfo;
            }
        }
    }

    if (lastNotifyInfo) {
        imy_asyncMainBlock(^{
            IMYAppDelegate *appDelegate = (IMYAppDelegate *)[[UIApplication sharedApplication] delegate];
            SYPushNotiAppDelegate *pushAppDelegate = (SYPushNotiAppDelegate *)[appDelegate moduleInstance:[SYPushNotiAppDelegate class]];
            if (pushAppDelegate.isFromLocalNotification) {
                if (!pushAppDelegate.isFromLocalGCNotification) { //非产检推送。执行下面方法，产检推送，不跳转到推送提醒页面
                    [self showNotifyInfoWithType:lastNotifyInfo.notifyType];
                }
                pushAppDelegate.isFromLocalNotification = NO;
                pushAppDelegate.isFromLocalGCNotification = NO;
            }
            IMY_POST_NOTIFY(@"LocalNotifyInfoReload");
        });
    }
}

/**
 *   登录成功，直接重置数据
 */
- (void)loginSucced {
    self.needReset = YES;
    [self exeResetData];
}

/**
 *   模式变换等，用户ID没变，直接基于现有的数据进行处理
 */
- (void)resetData {
    [self exeResetData];
}

/**
 *   登录成功或者模式变换时，重置通知数据 ，因为通知会同时发出多个，所以用 delay 和 cancel来处理。
 */
- (void)exeResetData {
    imy_throttle_on_queue(0.1, @"YYLocalNotifyManager.exeResetData", YYNotifySharedIOQueue(), ^{
        [self safe_exeResetData];
    });
}

- (void)safe_exeResetData {
    if (_needReset) {
        self.needReset = NO;
        NSInteger count = [YYLocalNotifyItem rowCountWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID]];
        if (count == 0) {
            [YYLocalNotifyItem updateToDBWithSet:[NSString stringWithFormat:@"userid='%@'", SY_USERID] where:@"userid=''"];
            for (YYLocalNotify *notify in self.notifies.array) {
                if (notify.userid.length == 0) {
                    notify.userid = SY_USERID;
                    [notify saveToDB];
                }
            }
            self.items = nil;
            self.notifies = nil;
        } else {
            self.notifies = nil;
            [self notifies];
            self.items = [YYLocalNotifyItem searchWithWhere:[NSString stringWithFormat:@"userid = '%@'", SY_USERID] orderBy:@"rowid desc" offset:0 count:1000];
        }
        [[YYLocalNotifyManager sharedYYLocalNotifyManager] save];
    }
    [self pregnanChanged];
}

/**
 *   日历刷新的时候重置数据
 */
- (void)reshowCalendarView {
    [self resetData];
}

- (void)cancelLocalNotifications {
    imy_asyncMainExecuteBlock(^{
        const NSInteger notificationtag = 1314520;
        NSArray* const array = [[UIApplication sharedApplication] scheduledLocalNotifications];
        for (UILocalNotification *notify in array) {
            const NSInteger mytag = [notify.userInfo[@"unlogin7DaysNotOpen"] integerValue];
            if (notify.userInfo.count > 0 && mytag != notificationtag) {
                // 删除本地提醒通知
                BOOL isOpen = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"womens_health.remind_mens.finish_immediate_switch"];
                if (isOpen && [[notify.userInfo objectForKey:@"k"] isEqualToString:@"v"]) {
                    //【体验优化】小提醒经期结束提醒优化
                    //https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001194094
                    //命中开关，并且是经期结束提醒:“您的大姨妈已经持续14天咯~如果已经走喽，请及时标记结束哦~”
                    //不判断app的活动状态，直接取消本地提醒
                    [[UIApplication sharedApplication] cancelLocalNotification:notify];
                } else {
                    //原逻辑
                    if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
                        [[UIApplication sharedApplication] cancelLocalNotification:notify];
                    }
                }
            }
        }
    });
}

#pragma mar - BI

//YYJingQiKaishi = 1,  //经期开始
//YYWSJ,               //换卫生巾
//YYBeiYun,            //备孕
//YYBaBeiShui,         //八杯水
//YYFuMianMo,          //敷面膜
//YYChiYao,             //吃药
//YYTask,              //任务提醒
//YYCustom,            //自定义
//YYYeSuan,            //叶酸片
//YYGai,               //钙片
//YYJingQiEnd,        //经期结束
///// 排卵试纸提醒
//YYOvulate = 12,
//YYNotifyTypeToday = 30  //今日建议提醒

//4	叶酸片提醒
//5	换卫生巾提醒
//6	排卵试纸提醒
//7	排卵日提醒
//8	敷面膜提醒
//9	吃药提醒


- (void)postNotifyStatus:(IMYSafeArray *)items {
    if (![[IMYPublicAppHelper shareAppHelper].userid imy_isPureInt]) {
        return;
    }
    NSMutableString *string = [NSMutableString stringWithString:@""];
    [items enumerateObjectsUsingBlock:^(YYLocalNotifyItem *obj, NSUInteger idx, BOOL *stop) {
        if (obj.notifyType == YYJingQiKaishi && obj.active) {
            [string appendString:string.length ? @",1" : @"1"];
        }
        if (obj.notifyType == YYJingQiEnd && obj.active) {
            [string appendString:string.length ? @",2" : @"2"];
        }
        if (obj.notifyType == YYBaBeiShui && obj.active) {
            [string appendString:string.length ? @",3" : @"3"];
        }
        if (obj.notifyType == YYYeSuan && obj.active) {
            [string appendString:string.length ? @",4" : @"4"];
        }
        if (obj.notifyType == YYWSJ && obj.active) {
            [string appendString:string.length ? @",5" : @"5"];
        }
        if (obj.notifyType == YYOvulate && obj.active) {
            [string appendString:string.length ? @",6" : @"6"];
        }
        if (obj.notifyType == YYBeiYun && obj.active) {
            [string appendString:string.length ? @",7" : @"7"];
        }
        if (obj.notifyType == YYFuMianMo && obj.active) {
            [string appendString:string.length ? @",8" : @"8"];
        }
        if (obj.notifyType == YYChiYao && obj.active) {
            [string appendString:string.length ? @",9" : @"9"];
        }
        if (obj.notifyType == YYGai && obj.active) {
            [string appendString:string.length ? @",11" : @"11"];
        }
        if (obj.notifyType == YYNotifyTypeToday && obj.active) {
            [string appendString:string.length ? @",10" : @"10"];
        }
        if (obj.notifyType == YYNotifyTypeTiWen && obj.active) {
            [string appendString:string.length ? @",13" : @"13"];
        }
        if (obj.notifyType == YYNotifyTypeYMB && obj.active) {
            [string appendString:string.length ? @",17" : @"17"];
        }
    }];

    [IMYGAEventHelper postWithPath:@"bi_remind" params:@{@"remind": string} headers:nil completed:nil];
}

- (YYLocalNotifyItem *)notifyItemForType:(NSInteger)type {
    return [_items bk_match:^BOOL(YYLocalNotifyItem *obj) {
        return obj.notifyType == type;
    }];
}

-(NSDate *)eventWillHappen:(YYRepeatType)repeatType
                 eventDate:(NSDate *)eventDate
                   isLunar:(BOOL)isLunar
                     count:(NSInteger)count {
    if(repeatType == YYRepeatTypeNone || count <= 0) {
        return nil;
    }
    NSDate *date = NSDate.imy_today;
    NSCalendar *calendar = NSDate.imy_getCNCalendar;
    NSCalendarUnit unit = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay;
    if (isLunar) {//农历
        calendar = NSDate.imy_getLunarCalendar;
        if (eventDate.isLeapMonth && repeatType == YYRepeatYear && [date isLaterThanDate:eventDate]) {
            eventDate = [eventDate lunar_dateAfterMonths:-1];
        }
    }
    NSDateComponents *components = [calendar components:unit fromDate:eventDate toDate:date options:0];

    
    if (repeatType == YYRepeatYear) {
        if ([eventDate isLaterOrEqualToDate:date]) {
            count -= 1;
            components = nil;
        } else if(components.month == 0 && components.day == 0) {
            count -= 1;
        } else {
            if (isLunar) {//例如事项日期是以前的二月，不管现在二月还是润二月都要
                NSDate *date1 = [eventDate lunar_dateByAddingYears:components.year];
                NSDate *date2 = [date1 lunar_dateAfterMonths:1];
                //润月总是在后  二月、润二月
                if (date2.isLeapMonth && !date1.isLeapMonth && [date2 isLaterOrEqualToDate:date]) {
                    return date2;
                }
            }
        }
        NSDateComponents *componentsToAdd = [[NSDateComponents alloc] init];
        [componentsToAdd setYear:count + components.year];
        NSDate *theDate = [calendar dateByAddingComponents:componentsToAdd toDate:eventDate options:0];
        return theDate;
    } else if (repeatType == YYRepeatMonth) {
        if ([eventDate isLaterOrEqualToDate:date]) {
            count -= 1;
            components = nil;
        } else if (components.day == 0) {
            count -= 1;
        }
        NSDateComponents *componentsToAdd = [[NSDateComponents alloc] init];
        [componentsToAdd setMonth:count + components.month + components.year * 12];
        NSDate *theDate = [calendar dateByAddingComponents:componentsToAdd toDate:eventDate options:0];
        return theDate;
    } else if (repeatType == YYRepeatWeek) {
        NSDate *theDate;
        if ([eventDate isLaterOrEqualToDate:date]) {
            theDate = eventDate;
            count -= 1;
        } else {
            NSInteger diffDay = [eventDate getDayDiff:date];
            NSInteger week = diffDay/7;
            theDate = [eventDate dateByAddingDays:week * 7];
            if (diffDay % 7 == 0) {
                count -= 1;
            }
        }
        theDate = [theDate dateByAddingDays:7 * count];
        return theDate;
    }
    return nil;
}

@end
