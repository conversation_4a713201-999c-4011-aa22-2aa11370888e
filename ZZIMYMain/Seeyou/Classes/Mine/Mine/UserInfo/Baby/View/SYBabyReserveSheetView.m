//
//  SYBabyReserveSheetView.m
//  ZZIMYMain
//
//  Created by 黄少彬 on 2020/11/30.
//

#import "SYBabyReserveSheetView.h"
#import "NSDate+Baby.h"
#import <IMYBaseKit/IMYPublic.h>
#import <IMYBaseKit/UIFont+IMYViewKit.h>
#import <IMYBaseKit/IMYViewKit.h>
#import "IMYRecordBabyManager.h"

@interface SYBabyReserveModel : NSObject

@property (nonatomic, assign, getter=isSelected) BOOL selected;
@property (nonatomic, strong) IMYRecordBabyModel *babyModel;

@end

@implementation SYBabyReserveModel

@end

@interface SYBabyReserveCell : UITableViewCell

@property (nonatomic, strong) UIView *containView;
@property (nonatomic, strong) IMYAvatarImageView *avaterImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *newLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) IMYTouchEXButton *selectedButton;

@property (nonatomic, strong) SYBabyReserveModel *model;
@property (nonatomic, copy) void (^selectedBlock)(BOOL isSelected);

@end

@implementation SYBabyReserveCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self prepareUI];
    }
    return self;
}

- (void)configModel:(SYBabyReserveModel *)model avatar:(UIImage *)avatar isCreate:(BOOL)isCreate {
    _model = model;
    self.avaterImageView.imy_showViewSize = self.avaterImageView.imy_size;
    self.avaterImageView.imy_placeholderImage = [model.babyModel genderPlaceholdImage];
    [self.avaterImageView imy_setImageURL:model.babyModel.avatar];
    self.titleLabel.text = imy_isNotBlankString(model.babyModel.nickname) ? model.babyModel.nickname : IMYString(@"宝宝");
    if (isCreate) {
        self.newLabel.text = IMYString(@"(新添加的宝宝)");
        if (avatar) {
            self.avaterImageView.image = avatar;
        }
    } else {
        self.newLabel.text = @"";
    }
    NSString *birthday = [[NSDateFormatter imy_getCN_DateFormater] stringFromDate:model.babyModel.birthday.imy_getOnlyDate];
    self.descLabel.text = [NSString stringWithFormat:IMYString(@"出生日：%@"),birthday];
    [self.selectedButton imy_setImage:model.isSelected ? @"ertai_icon_yixuan" : @"ertai_icon_weixuan"];
}

#pragma mark - action
- (void)selectedAction:(IMYTouchEXButton *)sender {
    self.model.selected = !self.model.isSelected;
    [self.selectedButton imy_setImage:self.model.isSelected ? @"ertai_icon_yixuan" : @"ertai_icon_weixuan"];
    if (self.selectedBlock) {
        self.selectedBlock(self.model.isSelected);
    }
}

#pragma mark - private
- (void)prepareUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = UIColor.clearColor;
    self.contentView.backgroundColor = UIColor.clearColor;
    
    [self.contentView addSubview:self.containView];
    [self.containView addSubview:self.avaterImageView];
    [self.containView addSubview:self.titleLabel];
    [self.containView addSubview:self.newLabel];
    [self.containView addSubview:self.descLabel];
    [self.containView addSubview:self.selectedButton];
    
    CGFloat margin = 16.0f;
    [self.containView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.contentView);
        make.left.mas_equalTo(margin);
        make.right.mas_equalTo(-margin);
    }];
    CGFloat avaterImageViewWH = 48.0;
    [self.avaterImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(avaterImageViewWH);
        make.left.mas_equalTo(margin);
        make.centerY.equalTo(self.containView);
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.avaterImageView);
        make.left.mas_equalTo(self.avaterImageView.mas_right).mas_offset(margin);
    }];
    [self.newLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.left.mas_equalTo(self.titleLabel.mas_right);
        make.right.mas_equalTo(self.selectedButton.mas_left).mas_offset(-margin);
    }];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh
    forAxis:UILayoutConstraintAxisHorizontal];
    [self.newLabel setContentCompressionResistancePriority:(UILayoutPriorityDefaultHigh) forAxis:(UILayoutConstraintAxisHorizontal)];
    [self.titleLabel setContentCompressionResistancePriority:(UILayoutPriorityDefaultLow) forAxis:(UILayoutConstraintAxisHorizontal)];
    
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.avaterImageView);
        make.left.equalTo(self.titleLabel);
        make.right.equalTo(self.newLabel);
    }];
    CGFloat selectedButtonWH = 20.0;
    [self.selectedButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(selectedButtonWH);
        make.right.mas_equalTo(-margin);
        make.centerY.equalTo(self.containView);
    }];
}

#pragma mark - setter or getter
- (UIView *)containView {
    if (!_containView) {
        _containView = [UIView new];
        _containView.backgroundColor = [[UIColor imy_colorForKey:kCK_Red_A] colorWithAlphaComponent:0.04];
        _containView.layer.cornerRadius = 8;
    }
    return _containView;
}

- (IMYAvatarImageView *)avaterImageView {
    if (!_avaterImageView) {
        _avaterImageView = [[IMYAvatarImageView alloc] init];
        _avaterImageView.userInteractionEnabled = YES;
        _avaterImageView.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _avaterImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont systemFontOfSize:17.0];
        [_titleLabel imy_setTextColor:kCK_Black_A];
    }
    return _titleLabel;
}

- (UILabel *)newLabel {
    if (!_newLabel) {
        _newLabel = [UILabel new];
        _newLabel.font = [UIFont systemFontOfSize:17.0];
        [_newLabel imy_setTextColor:kCK_Black_A];
    }
    return _newLabel;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [UILabel new];
        _descLabel.font = [UIFont systemFontOfSize:14.0];
        [_descLabel imy_setTextColor:kCK_Black_B];
    }
    return _descLabel;
}

- (IMYTouchEXButton *)selectedButton {
    if (!_selectedButton) {
        _selectedButton = [IMYTouchEXButton new];
        [_selectedButton addTarget:self action:@selector(selectedAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectedButton;
}

@end

@interface SYBabyReserveSheetView ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIButton *coverButton;
@property (nonatomic, strong) UIView *containView;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *confirmButton;

//新添加的宝宝位置
@property (nonatomic, assign) NSInteger creatingIndex;
//新添加的宝宝本地头像
@property (nonatomic, strong) UIImage *avatar;
//宝宝列表
@property (nonatomic, strong) NSMutableArray *babyList;
@property (nonatomic, copy) void (^completeBlock)(BOOL needAdd, NSArray *babyList);

@end

@implementation SYBabyReserveSheetView

+ (void)showSheetViewWithBabyModel:(IMYRecordBabyModel *)babyModel avatar:(UIImage *)avatar sameBaby:(BOOL)sameBaby changeMode:(BOOL)changeMode completeBlock:(void (^)(BOOL needAdd, NSArray *babyList))completeBlock {
    CGRect frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    NSString *title = IMYString(@"请选择要保留的宝宝");
    NSString *contentText = nil;
    //把要创建的宝宝插入到第一位
    NSMutableArray *babyList = [NSMutableArray arrayWithObject:babyModel];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    NSString *birthdayStr = [formatter stringFromDate:babyModel.birthday.imy_getOnlyDate];
    if (sameBaby) {
//        NSArray *sameBabyList = [IMYRecordBabyModel searchWithWhere:[NSString stringWithFormat:@"birthday = '%@' and is_deleted = 0", babyModel.birthday]];
        NSArray *sameBabyList = [IMYRecordBabyManager.sharedInstance myBabyListSearchWithBirthday:babyModel.birthday];
        [babyList addObjectsFromArray:sameBabyList];
        if (changeMode) {
            contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（出生日%@）与已有宝宝生日相同，请选择你要保留的宝宝。\n选择后，系统将按对应宝宝开始育儿模式，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复。（未保留“新添加的宝宝”，将删除大肚照以外的照片）。"),birthdayStr];
        } else {
            contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（出生日%@）与已有宝宝生日相同，请选择你要保留的宝宝。\n选择后，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复。"),birthdayStr];
        }
    } else {
        [babyList addObjectsFromArray:[self babyListBetween4And154:babyModel.birthday]];
        contentText = [NSString stringWithFormat:IMYString(@"新添加的宝宝（出生日%@）与已有宝宝生日间隔在4-154天内，存在冲突，请选择你要保留的宝宝。\n选择后，未保留的宝宝，其宝宝记录及亲友关系将被删除且不可恢复。"),birthdayStr];
    }

    if (babyList.count > 0) {
        SYBabyReserveSheetView *sheetView = [[SYBabyReserveSheetView alloc] initWithFrame:frame title:title contentTitle:contentText items:babyList completeBlock:completeBlock];
        //本地头像
        if (avatar) {
            sheetView.avatar = avatar;
            [sheetView.tableView reloadData];
        }
        [[UIWindow imy_getShowTopWindow] addSubview:sheetView];
        [sheetView show];
    }
}


+ (void)showSheetViewWithBabyModel:(IMYRecordBabyModel *)babyModel babyList:(NSArray *)list content:(NSString *)content completeBlock:(void (^)(BOOL needAdd, NSArray *babyList))completeBlock{
    CGRect frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    NSString *title = IMYString(@"请选择要保留的宝宝");
    if (!list || ![list isKindOfClass:[NSArray class]]) {
        list = @[];
    }
    NSMutableArray *babyList = [NSMutableArray arrayWithArray:list];
    //把要创建的宝宝插入到第一位
    [babyList insertObject:babyModel atIndex:0];
    if (babyList.count > 0) {
        SYBabyReserveSheetView *sheetView = [[SYBabyReserveSheetView alloc] initWithFrame:frame title:title contentTitle:content items:babyList completeBlock:completeBlock];
        [[UIWindow imy_getShowTopWindow] addSubview:sheetView];
        [sheetView show];
    }
}

- (instancetype)initWithFrame:(CGRect)frame title:(NSString *)title contentTitle:(NSString *)contentTitle items:(NSArray *)items completeBlock:(void (^)(BOOL needAdd, NSArray *babyList))completeBlock {
    if (self = [super initWithFrame:frame]) {
        //新添加的宝宝默认放在第一位
        self.creatingIndex = 0;
        [self prepareUI];
        self.completeBlock = completeBlock;
        self.titleLabel.text = title;
        self.contentLabel.text = contentTitle;
        self.babyList = [NSMutableArray array];
        for(IMYRecordBabyModel *model in items) {
            SYBabyReserveModel *reserveModel = [SYBabyReserveModel new];
            reserveModel.babyModel = model;
            reserveModel.selected = NO;
            [self.babyList addObject:reserveModel];
        }
        CGFloat tableViewH = 92.0*MIN(self.babyList.count, 3.5);
        [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(tableViewH);
        }];
        [self addMaskLayer];
    }
    return self;
}

- (void)show {
    [self.containView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self);
    }];
    
    [UIView animateWithDuration:0.15 animations:^{
        [self layoutIfNeeded];
    }];
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event": @"yy_yrmsszy_xzblbb", @"action": @(1)} headers:nil completed:nil];

}

#pragma mark - action
- (void)cancelAction {
    [self.containView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.mas_bottom);
    }];
    [UIView animateWithDuration:0.15 animations:^{
        self.coverButton.alpha = 0;
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)confirmAction {
    [self cancelAction];
    BOOL needAdd = NO;
    NSMutableArray *babyList = [NSMutableArray array];
    NSInteger selectCount = 0;
    for (int i = 0; i < self.babyList.count; i ++) {
        SYBabyReserveModel *reserveModel = [self.babyList imy_objectAtIndex:i];
        if (reserveModel.isSelected && i == self.creatingIndex) {
            needAdd = YES;
        } else if (!reserveModel.isSelected && i != 0) {
            [babyList addObject:reserveModel.babyModel];
        }
        if (reserveModel.selected) {
            selectCount ++ ;
        }
    }
    [IMYGAEventHelper postWithPath:@"event" 
                            params:@{@"event": @"yy_yrmsszy_xzblbb",
                                     @"action": @(2),
                                     @"public_type": @(needAdd ? 1 : 0),
                                     @"public_info":[NSString stringWithFormat:@"%ld-%ld",self.babyList.count, selectCount]}
                           headers:nil
                         completed:nil];

    if (self.completeBlock) {
        self.completeBlock(needAdd, babyList);
    }
}

#pragma mark - private
- (void)prepareUI {
    [self addSubview:self.coverButton];
    [self addSubview:self.containView];
    [self.containView addSubview:self.cancelButton];
    [self.containView addSubview:self.titleLabel];
    [self.containView addSubview:self.contentLabel];
    [self.containView addSubview:self.tableView];
    [self.containView addSubview:self.confirmButton];
    
    CGFloat margin = 16.0f;
    [self.coverButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.containView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.mas_bottom);
    }];
    
    CGFloat cancelButtonWH = margin;
    [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-margin);
        make.top.mas_equalTo(margin);
        make.width.height.mas_equalTo(cancelButtonWH);
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.cancelButton.mas_bottom);
        make.left.mas_equalTo(margin);
        make.right.mas_equalTo(-margin);
    }];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(margin);
        make.left.right.equalTo(self.titleLabel);
    }];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentLabel.mas_bottom).mas_offset(margin);
        make.left.right.equalTo(self.containView);
        make.height.mas_equalTo(0);
    }];
    
    CGFloat confirmButtonH = 48.0f;
    CGFloat confirmButtonT = margin*0.5;
    CGFloat confirmButtonB = SCREEN_TABBAR_SAFEBOTTOM_MARGIN + confirmButtonT;
    [self.confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.tableView.mas_bottom).mas_offset(confirmButtonT);
        make.height.mas_equalTo(confirmButtonH);
        make.left.mas_equalTo(margin);
        make.right.mas_equalTo(-margin);
        make.bottom.mas_equalTo(-confirmButtonB);
    }];
    self.confirmButton.layer.cornerRadius = confirmButtonH*0.5;
    self.confirmButton.layer.masksToBounds = YES;
}

- (void)addMaskLayer {
    [self layoutIfNeeded];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.containView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(12, 12)];
    CAShapeLayer *shapeLayer = [[CAShapeLayer alloc] init];
    shapeLayer.frame = self.containView.bounds;
    shapeLayer.path = maskPath.CGPath;
    self.containView.layer.mask = shapeLayer;
}

- (void)updateConfirmButtonStatus {
    BOOL enable = NO;
    for (SYBabyReserveModel *reserveModel in self.babyList) {
        if (reserveModel.isSelected) {
            enable = YES;
            break;
        }
    }
    self.confirmButton.enabled = enable;
    if (enable) {
        self.confirmButton.backgroundColor = [UIColor imy_colorForKey:kCK_Red_A];
    } else {
        self.confirmButton.backgroundColor = [UIColor imy_colorForKey:kCK_Black_K];
    }
}

#pragma mark - UITableViewDelegate,UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.babyList.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellId = @"SYBabyReserveCellId";
    SYBabyReserveCell *cell = [tableView dequeueReusableCellWithIdentifier:cellId];
    if (!cell) {
        cell = [[SYBabyReserveCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellId];
    }
    [cell configModel:[self.babyList imy_objectAtIndex:indexPath.section] avatar:self.avatar isCreate:self.creatingIndex == indexPath.section];
    @weakify(self);
    cell.selectedBlock = ^(BOOL isSelected) {
        @strongify(self);
        [self updateConfirmButtonStatus];
    };
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80.0f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.01;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 12.0f;
}

#pragma mark - setter or getter
- (UIButton *)coverButton {
    if (!_coverButton) {
        _coverButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _coverButton.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
        [_coverButton addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _coverButton;
}

- (UIView *)containView {
    if (!_containView) {
        _containView = [UIView new];
        [_containView imy_setBackgroundColor:kCK_White_ANP];
    }
    return _containView;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelButton imy_setImage:@"all_popup_icon_close"];
        [_cancelButton addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.font = [UIFont imy_MediumFontWith:18.0];
    }
    return _titleLabel;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [UILabel new];
        [_contentLabel imy_setTextColor:kCK_Black_A];
        _contentLabel.font = [UIFont imy_FontWith:14.0];
        _contentLabel.numberOfLines = 0;
    }
    return _contentLabel;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        [_tableView imy_setBackgroundColor:kCK_White_ANP];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;
}

- (UIButton *)confirmButton {
    if (!_confirmButton) {
        _confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_confirmButton imy_setTitle:IMYString(@"确定")];
        [_confirmButton imy_setTitleColor:kCK_White_A];
        _confirmButton.enabled = NO;
        _confirmButton.titleLabel.font = [UIFont systemFontOfSize:18.0f];
        [_confirmButton imy_setBackgroundColor:kCK_Black_K];
        [_confirmButton addTarget:self action:@selector(confirmAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _confirmButton;
}

@end

@implementation SYBabyReserveSheetView (Baby)

///已有宝宝生日间隔在4-154天内
+ (BOOL)containBabyBetween4And154:(NSString *)babyBirthday {
    NSArray *babyList = [self babyListBetween4And154:babyBirthday];
    if (babyList.count > 0) {
        return YES;
    }
    return NO;
}

+ (NSArray *)babyListBetween4And154:(NSString *)babyBirthday {
    //已有宝宝生日间隔在4-154天内
    NSDate *babyDate = [babyBirthday imy_getOnlyDate];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dateComponents = [NSDateComponents new];
    //往前偏移
    dateComponents.day = -4;
    NSDate *dateM4 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    dateComponents.day = -154;
    NSDate *dateM154 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    //往后偏移
    dateComponents.day = 4;
    NSDate *date4 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    dateComponents.day = 154;
    NSDate *date154 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    
    // 按照生日排序选出宝宝列表2
    NSString *whereSql = [NSString stringWithFormat:@"is_owner=1 and is_deleted=0 and ((birthday<'%@' and birthday>'%@') or (birthday<'%@' and birthday>'%@'))", [dateM4 imy_getOnlyDateString], [dateM154 imy_getOnlyDateString], [date154 imy_getOnlyDateString], [date4 imy_getOnlyDateString]];
    NSArray *babyList = [IMYRecordBabyModel searchWithWhere:whereSql orderBy:@"birthday desc,created_at desc" offset:0 count:0];
    return babyList;
}

///已有宝宝生日相同
+ (BOOL)containBabySameBirthday:(NSString *)babyBirthday {
    NSArray *babyList = [self babyListSameBirthday:babyBirthday];
    if (babyList.count > 0) {
        return YES;
    }
    return NO;
}


/// 查找相同生日 的宝宝
/// @param babyBirthday 生日
+ (NSArray *)babyListSameBirthday:(NSString *)babyBirthday {
    // 按照生日排序选出宝宝列表
//    NSString *whereSql = [NSString stringWithFormat:@"is_deleted=0 and birthday='%@'", babyBirthday];
    
//    NSArray *babyList = [IMYRecordBabyModel searchWithWhere:whereSql orderBy:@"birthday desc,created_at desc" offset:0 count:0];
    NSArray *babyList = [IMYRecordBabyManager.sharedInstance myBabyListSearchWithBirthday:babyBirthday];

    return babyList;
}


/// 查找前后4天内的宝宝[-4, -1],[1, 4]
/// @param birthdayString 生日
+ (BOOL)hasSimilarBabyBetween0And4:(NSString *)birthdayString {
    return !![self findBabyBetween0And4:birthdayString];
}

+ (IMYRecordBabyModel * )findBabyBetween0And4:(NSString *)birthdayString {
    //已有宝宝生日间隔在1-4天内
    NSDate *babyDate = [birthdayString imy_getOnlyDate];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *dateComponents = [NSDateComponents new];
    //往前偏移
    dateComponents.day = -1;
    NSDate *dateM1 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    dateComponents.day = -4;
    NSDate *dateM4 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    //往后偏移
    dateComponents.day = 1;
    NSDate *date1 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    dateComponents.day = 4;
    NSDate *date4 = [calendar dateByAddingComponents:dateComponents toDate:babyDate options:0];
    
    // 按照生日排序选出宝宝列表
    NSString *whereSql = [NSString stringWithFormat:@"is_owner=1 and is_deleted=0 and ((birthday<='%@' and birthday>='%@') or (birthday<='%@' and birthday>='%@'))", [dateM1 imy_getOnlyDateString], [dateM4 imy_getOnlyDateString], [date4 imy_getOnlyDateString], [date1 imy_getOnlyDateString]];
    NSArray *babyList = [IMYRecordBabyModel searchWithWhere:whereSql orderBy:@"birthday desc,created_at desc" offset:0 count:1];
    return babyList.firstObject;
}

/// 显示相近宝宝弹窗
/// @param birthdayString 当前要新建的宝宝生日
/// @param completionBlk 完成回调,是否需要新增
+ (void)showSimilarBabyAlertWithBirthday:(NSString *)birthdayString completion:(void(^)(BOOL addNew))completionBlk{
    IMYRecordBabyModel *baby = [self findBabyBetween0And4:birthdayString];
    if (!baby || imy_isEmptyString(birthdayString)) {
        !completionBlk?:completionBlk(YES);
        return ;
    }
    NSDate *date = [baby.birthday imy_getOnlyDate];
    NSString *message = [NSString stringWithFormat:IMYString(@"昵称：%@\n性别：%@\n生日：%ld年%ld月%ld日"), baby.nickname, baby.genderString, date.year, date.month, date.day];
    
    [IMYActionMessageBox showBoxWithTitle:IMYString(@"有生日相近的宝宝")
                                  message:IMYString(message)
                                    style:IMYMessageBoxStyleFlat
                        isShowCloseButton:NO
                            textAlignment:NSTextAlignmentCenter
                        cancelButtonTitle:IMYString(@"取消")
                         otherButtonTitle:IMYString(@"继续添加")
                                   action:^(IMYRoundButton *sender, IMYActionMessageBox *messageBox) {
        !completionBlk?:completionBlk(sender == messageBox.rightButton);
        [messageBox dismiss];
    }];
}


@end
