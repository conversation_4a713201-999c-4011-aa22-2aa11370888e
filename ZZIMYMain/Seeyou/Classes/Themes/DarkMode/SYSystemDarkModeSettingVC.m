//
//  SYSystemDarkModeSettingVC.m
//  ZZIMYMain
//
//  Created by ljh on 2024/7/22.
//

#import "SYSystemDarkModeSettingVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "SYSystemDarkMode.h"
#import "SYSettingModel.h"
#import "IMYSettingCardCell.h"

@interface SYSystemDarkModeSettingVC () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, copy) NSArray<NSArray *> *items;
@end

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [[IMYURIManager sharedInstance] addForPath:@"setting/darkmode"
                               withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        SYSystemDarkModeSettingVC *vc = [SYSystemDarkModeSettingVC new];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
}

@implementation SYSystemDarkModeSettingVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"深色模式";
    [self setupData];
    [self setupTableView];
}

- (void)imy_themeChanged {
    [super imy_themeChanged];
    [self.tableView reloadData];
}

- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.rowHeight = UITableViewAutomaticDimension;
    self.tableView.estimatedRowHeight = 48;
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_offset(UIEdgeInsetsZero);
    }];
    [self.tableView registerClass:IMYSettingCardCell.class forCellReuseIdentifier:@"IMYSettingCardCell"];
}

- (void)setupData {
    
    NSMutableArray<SYSettingModel *> *array = [NSMutableArray array];
    
    BOOL hasShowManual = YES;
    
    // 增加跟随系统选项
    if (@available(iOS 13.0, *)) {
        SYSettingModel *model = [SYSettingModel new];
        model.type = SYSettingModelTypeCardNormal;
        model.cellType = SYSettingCellTypeDarkModeFollowSystem;
        model.title = IMYString(@"跟随系统");
        model.remindValue = @"开启后跟随系统打开或关闭深色模式";
        
        [array addObject:model];
    }
    
    // 增加手动选项
    if (hasShowManual) {
        SYSettingModel *lightModel = [SYSettingModel new];
        lightModel.type = SYSettingModelTypeCardNormal;
        lightModel.cellType = SYSettingCellTypeDarkModeOpenLight;
        lightModel.title = IMYString(@"浅色模式");
        lightModel.userInfo = @{ @"header" : @"手动选择" };
        
        SYSettingModel *darkModel = [SYSettingModel new];
        darkModel.type = SYSettingModelTypeCardNormal;
        darkModel.cellType = SYSettingCellTypeDarkModeOpenDark;
        darkModel.title = IMYString(@"深色模式");
        
        [array addObject:lightModel];
        [array addObject:darkModel];
    }
    
    self.items = @[array];
}

- (void)reloadAllData {
    [self setupData];
    [self.tableView reloadData];
}

#pragma mark - tv delegate && datasource

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *sectionData = self.items[indexPath.section];
    SYSettingModel *model = sectionData[indexPath.row];
    if (model.cellType == SYSettingCellTypeDarkModeFollowSystem) {
        return 68;
    }
    return 48;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.items.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *sectionData = self.items[section];
    return sectionData.count;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    NSArray *sectionData = self.items[section];
    SYSettingModel *model = sectionData.firstObject;
    NSString *headerText = model.userInfo[@"header"];
    if (headerText.length > 0) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 34 + 8)];
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(24, 8, SCREEN_WIDTH - 48, 34)];
        label.text = headerText;
        label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        label.numberOfLines = 1;
        [label imy_setTextColor:kCK_Black_B];
        [view addSubview:label];
        
        return view;
    }
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    NSArray *sectionData = self.items[section];
    SYSettingModel *model = sectionData.firstObject;
    NSString *headerText = model.userInfo[@"header"];
    if (headerText.length > 0) {
        return 34 + 8;
    }
    return 8;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 获取复用Cell
    IMYSettingCardCell *cell = [tableView dequeueReusableCellWithIdentifier:@"IMYSettingCardCell" forIndexPath:indexPath];
    
    NSArray *sectionData = self.items[indexPath.section];
    SYSettingModel *model = sectionData[indexPath.row];
    BOOL isSelected = NO;
    if (model.cellType == SYSettingCellTypeDarkModeFollowSystem) {
        isSelected = SYSystemDarkMode.isOpenFollowSystem;
        cell.model = model;
    } else {
        cell.model = model;
        if (model.cellType == SYSettingCellTypeDarkModeOpenDark) {
            isSelected = !SYSystemDarkMode.isOpenFollowSystem && SYSystemDarkMode.isOpenDarkMode;
        } else if (model.cellType == SYSettingCellTypeDarkModeOpenLight) {
            isSelected = !SYSystemDarkMode.isOpenFollowSystem && !SYSystemDarkMode.isOpenDarkMode;
        }
    }
    
    // 打钩标签
    UIImageView *selectIconView = [cell.arrowImageView.superview viewWithTag:11];
    if (!selectIconView) {
        selectIconView = [[UIImageView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 30, 12, 20, 20)];
        selectIconView.image = [UIImage imy_imageForKey:@"all_icon_chose"];
        selectIconView.tag = 11;
        [cell.arrowImageView.superview addSubview:selectIconView];
        [selectIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@16);
            make.height.equalTo(@16);
            make.center.equalTo(cell.arrowImageView);
        }];
    }
    cell.arrowImageView.hidden = YES;
    selectIconView.hidden = (isSelected ? NO : YES);
    
    [cell drawCardStyle:indexPath rowCount:sectionData.count];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSArray *sectionData = self.items[indexPath.section];
    SYSettingModel *model = sectionData[indexPath.row];
    
    if (model.cellType == SYSettingCellTypeDarkModeOpenDark) {
        SYSystemDarkMode.isOpenFollowSystem = NO;
        SYSystemDarkMode.isOpenDarkMode = YES;
        SYSystemDarkMode.isManualSetting = YES;
        [self reloadAllData];
    } else if (model.cellType == SYSettingCellTypeDarkModeOpenLight) {
        SYSystemDarkMode.isOpenFollowSystem = NO;
        SYSystemDarkMode.isOpenDarkMode = NO;
        SYSystemDarkMode.isManualSetting = YES;
        [self reloadAllData];
    } else if (model.cellType == SYSettingCellTypeDarkModeFollowSystem) {
        // 修改跟随系统开关，关闭时保持当前深色模式
        if (!SYSystemDarkMode.isOpenFollowSystem) {
            SYSystemDarkMode.isOpenFollowSystem = YES;
            // 刷新UI
            [self reloadAllData];
        }
    }
}

@end
