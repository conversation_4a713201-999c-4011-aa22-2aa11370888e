//
//  SYQuickStartEDCStep2VC.m
//  ZZIMYMain
//
//  Created by 黄少彬 on 2021/5/12.
//

#import "SYQuickStartEDCStep2VC.h"
#import <IMYBaseKit/IMY_ViewKit.h>
#import <IMYRecord/IMYRecordUGTopView.h>
#import <IMYRecord/IMYAnalyzeDefine.h>
#import "SYQuickStartEDCResultVC.h"
#import "SYQuickStartPickerView.h"
#import "SYABTestManager.h"

@interface SYQuickStartEDCStep2VC ()

@property (nonatomic, strong) IMYRecordUGTopNavView *navView;
@property (nonatomic, strong) IMYRecordUGTopContentView *topContentView;
@property (nonatomic, strong) SYQuickStartPickerView *pickerView;
@property (nonatomic, strong) IMYCapsuleButton *nextButton;
@property (nonatomic, strong) IMYButton *helpButton;


@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *scrollContentView;
@end

@implementation SYQuickStartEDCStep2VC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self prepareUI];
}

#pragma mark - action
/// 计算预产期
- (void)nextAction {
    [self imyut_cleanExposuredIds];
    //埋点
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event": [self bi_event], @"action": @(2)} headers:nil completed:nil];
    //跳转
    SYQuickStartEDCResultVC *vc = [SYQuickStartEDCResultVC new];
    vc.isFromWelcome = self.isFromWelcome;
    vc.lastMenseDate = self.lastMenseDate;
    vc.calculateInterval = [[self.pickerView fetchPickerViewRetString] integerValue];
    vc.finishedBlock = self.finishedBlock;
    vc.finishedSelectedDueBlock = self.finishedSelectedDueBlock;
    [self imy_push:vc];
}

/// 计算预产期说明
- (void)helpAction {
    [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": [NSString stringWithFormat:@"%@/help/yuchanqi.html", view_seeyouima_com]} info:nil];
}

#pragma mark - private
- (void)prepareUI {
    //隐藏导航栏，添加自定义头部
    self.navigationBarHidden = YES;
    [self.view addSubview:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(-SCREEN_STATUSBAR_HEIGHT);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(SCREEN_STATUSBAR_HEIGHT + SCREEN_NAVIGATIONBAR_HEIGHT);
    }];
    
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.navView.mas_bottom);
        make.left.right.bottom.mas_equalTo(0);
    }];
    
    [self.scrollView addSubview:self.scrollContentView];
    [self.scrollContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.scrollView);
        make.width.mas_equalTo(self.scrollView);
        make.height.mas_equalTo(self.scrollView);
        make.centerX.mas_equalTo(self.scrollView);
    }];
    
    [self.scrollContentView addSubview:self.topContentView];
    [self.topContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.right.equalTo(self.scrollContentView);
    }];
    
    [self.scrollContentView addSubview:self.pickerView];
    [self.view addSubview:self.nextButton];
    

    
    CGFloat pickerViewT = 20;
    if (iPhone5) {
        pickerViewT = SCREEN_By375(pickerViewT);
    }
    CGFloat pickerViewM = 12;
    CGFloat pickerViewH = 338;
    if (iPhone5) {
        pickerViewH = SCREEN_By375(pickerViewH);
    }
    [self.pickerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.topContentView.mas_bottom).offset(pickerViewT);
        make.left.mas_equalTo(self.view.mas_left).mas_offset(pickerViewM);
        make.right.mas_equalTo(self.view.mas_right).mas_offset(-pickerViewM);
        make.height.mas_equalTo(pickerViewH);
    }];
    
    CGFloat startButtonM = 20;
    CGFloat startButtonH = 48;
    if (iPhone5) {
        startButtonH = SCREEN_By375(startButtonH);
    }
    CGFloat startButtonB = 24 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN + SCREEN_STATUSBAR_HEIGHT;
    [self.nextButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(startButtonM);
        make.right.mas_equalTo(-startButtonM);
        make.height.mas_equalTo(startButtonH);
        make.bottom.mas_equalTo(-startButtonB);
    }];
}

#pragma mark - setter or getter
- (IMYRecordUGTopNavView *)navView{
    if (!_navView) {
        IMYRecordUGTopNavView *navView = [IMYRecordUGTopNavView topViewWithNavTitle:IMYString(@"计算预产期（2/2）")];
        _navView = navView;
    }
    return _navView;
}

- (IMYRecordUGTopContentView *)topContentView{
    if (!_topContentView) {
        IMYRecordUGTopContentView *topContentView = [IMYRecordUGTopContentView topViewWithTitle:IMYString(@"请确认月经大概多久来一次") subTitle:IMYString(@"将通过最近一次经期为你推算预产期")];
        [topContentView addSubview:self.helpButton];
        [topContentView removeSubTitleLabelRLO];
        CGFloat helpButtonW = 18;
        CGFloat helpButtonH = 16;
        [self.helpButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(helpButtonH);
            make.width.mas_equalTo(helpButtonW);
            make.left.equalTo(topContentView.subTitleLabel.mas_right).mas_offset(4);
            make.centerY.equalTo(topContentView.subTitleLabel);
        }];
        _topContentView = topContentView;
    }
    return _topContentView;
}

- (IMYButton *)helpButton {
    if (!_helpButton) {
        _helpButton = [IMYButton new];
        [_helpButton imy_setImage:@"sfxz_icon_wenhao"];
        [_helpButton addTarget:self action:@selector(helpAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _helpButton;
}

- (SYQuickStartPickerView *)pickerView {
    if (!_pickerView) {
        // 周期天数
        NSMutableArray *array = [[NSMutableArray alloc] init];
        NSInteger min = MAX(IMYRecordMenseCycleLengthMin, IMYPublicAppHelper.shareAppHelper.localMensesDay + 4);
        NSInteger max = IMYRecordMenseCycleLengthMax;
        for (int j = min; j <= max; j++) {
            [array addObject:[NSString stringWithFormat:IMYString(@"%d天"), j]];
        }
        _pickerView = [[SYQuickStartPickerView alloc] initWithPickerType:SYQuickStartPickerType_Gender data:@[array]];
        // 默认选中为28天
        [_pickerView setSelectRowsWithString:IMYString(@"28天")];
    }
    return _pickerView;
}

- (IMYCapsuleButton *)nextButton {
    if (!_nextButton) {
        _nextButton = [IMYCapsuleButton new];
        _nextButton.type = IMYButtonTypeFillRed;
        CGFloat size = 17;
        if (iPhone5) {
            size = SCREEN_By375(size);
        }
        _nextButton.titleLabel.font = [UIFont systemFontOfSize:size];
        [_nextButton imy_setTitle:IMYString(@"计算预产期")];
        [_nextButton addTarget:self action:@selector(nextAction) forControlEvents:UIControlEventTouchUpInside];
        
        _nextButton.imyut_eventInfo.showRadius = 1.0;
        _nextButton.imyut_eventInfo.eventName = @"SYQuickStartEDCStep2VC-Button";
        _nextButton.imyut_eventInfo.ableToClean = YES;
        @weakify(self);
        _nextButton.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *LKDBQueryParams) {
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event" params:@{@"event": [self bi_event], @"action": @(1)} headers:nil completed:nil];
        };
    }
    return _nextButton;
}

- (NSString *)bi_event {
    if (self.isFromWelcome) {
        return  @"sfqh_ycqjs_jsycq";
    } else {
        return @"zcdl_ycqjs_jsycq";
    }
}

- (NSString *)ga_pageName {
    if (self.isFromWelcome) {
        return [NSString stringWithFormat:@"%@FromWelcome", NSStringFromClass(self.class)];
    } else {
        return [NSString stringWithFormat:@"%@NewUser", NSStringFromClass(self.class)];
    }
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectZero];
        [_scrollView setBounces:YES];
        [_scrollView setAlwaysBounceVertical:YES];
        _scrollView.backgroundColor = UIColor.clearColor;
    }
    return _scrollView;
}

- (UIView *)scrollContentView {
    if (!_scrollContentView) {
        _scrollContentView = [UIView new];
        _scrollContentView.backgroundColor = UIColor.clearColor;
    }
    return _scrollContentView;
}

@end
