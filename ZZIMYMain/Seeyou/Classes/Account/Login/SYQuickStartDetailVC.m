//
//  SYQuickStartDetailVC.m
//  Seeyou
//
//  Created by 林云峰 on 16/9/20.
//  Copyright © 2016年 linggan. All rights reserved.
//

#import "SYQuickStartDetailVC.h"
#import "SYModeSelectModel.h"
#import "SYUseModeTableViewCell.h"
#import "IMYAccountCheckPrivateView.h"
#import <IMYCellModel.h>
#import <IMYRecord/IMYRecordDefines.h>
#import <IMYRecord/IMYRecordBabyManager.h>
#import <IMYRecord/IMYRecordABTestManager.h>
#import <IMYAccount/IMYAccountPrivacyUsageStatementView.h>

@interface SYQuickStartDetailVC () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *sectionTitles;
@property (nonatomic, strong) NSArray *items;
@property (nonatomic, strong) IMYPickerView *pickerView;
@property (nonatomic, assign) BOOL isExpended;
//是否使用计算孕产期
@property (nonatomic, assign) BOOL isUseCalculate;
@property (nonatomic, strong) UIView *introduceView;
@property (nonatomic, strong) IMYAccountPrivacyUsageStatementView *privacyView;
@property (nonatomic, strong) IMYAccountCheckPrivateView *checkPrivateView;

@end

@implementation SYQuickStartDetailVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initTitles];
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.privacyView];
    // Do any additional setup after loading the view.
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (self.imy_isPop) {
        NSString *mode = (@"经期");
        if (self.model.mode == SYUserModelTypeForPregnant) {
            mode = (@"备孕");
        } else if (self.model.mode == SYUserModelTypePregnancy) {
            mode = (@"怀孕");
        } else if (self.model.mode == SYUserModelTypeLama) {
            mode = (@"辣妈");
        }
        [IMYEventHelper event:@"sfsz-fh" attributes:@{@"身份": mode}];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)dealloc {
    [self releasePickerView];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)imy_topLeftButtonTouchupInside {
    if (self.model.mode == SYUserModelTypePregnancy) {
        [IMYEventHelper event:@"ycqsz-fh"];
    } else if (self.model.mode == SYUserModelTypeLama) {
        [IMYEventHelper event:@"lmsz-fh"];
    } else {
        [IMYEventHelper event:@"jqsz-fh"];
    }
    [self imy_pop:YES];
}

#pragma mark - Picker

- (void)releasePickerView {
    if (self.pickerView) {
        [self.pickerView freePicker];
        self.pickerView = nil;
    }
}

- (void)addPickerTouchNotPickerSpaceAction {
    if (self.pickerView) {
        @weakify(self);
        [self.pickerView setTouchNotPickerSpace:^(UIView *backCoverView, IMYPickerView *picker, YYPickerView *yyPicker, IMYPickerViewModel *viewModel) {
            @strongify(self);
            [self resetOffset];
        }];
    }
}

- (void)showPicker:(IMYSimpleCellModel *)model {
    [self releasePickerView];
    if (model.type == SYQuickStartTypeLastMense) {
        [self showLastMensePicker];
    } else if (model.type == SYQuickStartTypeMenseDay) {
        [self showMenseDayPicker];
    } else if (model.type == SYQuickStartTypeMenseInterval) {
        [self showMenseIntervalPicker];
    } else if (model.type == SYQuickStartTypeDueDate) {
        [self showDueDatePicker];
    } else if (model.type == SYQuickStartTypeSex) {
        [self showBabySexPicker];
    } else if (model.type == SYQuickStartTypeBirthday) {
        [self showBirthdayPicker];
    } else if (model.type == SYQuickStartTypeTmpMenseInterval) {
        [self showMenseIntervalPicker];
    } else if (model.type == SYQuickStartTypeTmpLastMense) {
        [self showCalculatePicker];
    } else if (model.type == SYQuickStartTypeUserBirthdayDate) {
        [self showUserBirthdayDatePicker];
    } else if (model.type == SYQuickStartTypeUserBirthdayYear) {
        [self showUserBirthdayOnlyAgePicker];
    }

    [self addPickerTouchNotPickerSpaceAction];
}

- (void)showLastMensePicker {
    [IMYEventHelper event:@"jqsz-xm" attributes:@{@"项目": @"最后一次月经"}];
    @weakify(self);
    NSArray *data = @[IMYPickerViewDefaultDate, [NSDate imy_today]];
    self.pickerView = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:data
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:self.model.mode == SYUserModelTypeLama ? @"lmsz-qd" : @"jqsz-qd" attributes:@{@"项目": @"最后一次月经"}];
            if ([self checkDatePicker:result.resultDate] == NO) {
                return;
            }
            self.model.lastMenseDate = result.resultDate;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeLastMense];
            cellModel.content = [self.model.lastMenseDate getCNDateString];
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    self.pickerView.title = IMYString(@"最后一次经期开始日");
    self.pickerView.autoReleaseSelf = YES;
    NSDate *selectDate = self.model.lastMenseDate ? self.model.lastMenseDate : [NSDate imy_today];
    [self.pickerView setSelectWithDate:selectDate];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeLastMense];
}

- (void)showMenseDayPicker {
    [IMYEventHelper event:@"jqsz-xm" attributes:@{@"项目": @"经期长度"}];
    NSMutableArray *array = [[NSMutableArray alloc] init];
    
    NSInteger cylengthLength = self.model.pars_interval ?: 28;
    if (self.model.mode == SYUserModelTypePregnancy) {
        cylengthLength = self.model.calculateInterval ?: 28;
    }
    NSInteger minDay = IMYRecordMenseLengthMin;
    NSInteger maxDay = MIN(IMYRecordMenseLengthMax, cylengthLength - 4);
    for (int i = minDay; i <= maxDay; i++) {
        [array addObject:[NSString stringWithFormat:IMYString(@"%d天"), i]];
    }
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[array]
        pickerViewTpe:IMYPickerViewTypeCustom
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:self.model.mode == SYUserModelTypeLama ? @"lmsz-qd" : @"jqsz-qd" attributes:@{@"项目": @"经期长度"}];
            NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
            self.model.pars_menses_day = day.integerValue;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeMenseDay];
            cellModel.content = self.model.menseDayString;
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.title = IMYString(@"选择经期天数");
    self.pickerView = picker;
    picker.autoReleaseSelf = YES;
    NSInteger length = self.model.pars_menses_day ?: 5;
    [self.pickerView setSelectWithString:[NSString stringWithFormat:IMYString(@"%ld天"), length]];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeMenseDay];
}

- (void)showMenseIntervalPicker {
    [IMYEventHelper event:@"jqsz-xm" attributes:@{@"项目": @"周期长度"}];
    NSMutableArray *array = [[NSMutableArray alloc] init];
    NSInteger mensesLength = self.model.pars_menses_day ?: 5;
    NSInteger min = MAX(IMYRecordMenseCycleLengthMin, mensesLength + 4);
    NSInteger max = IMYRecordMenseCycleLengthMax;
    for (int j = min; j <= max; j++) {
        [array addObject:[NSString stringWithFormat:IMYString(@"%d天"), j]];
    }
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[array]
        pickerViewTpe:IMYPickerViewTypeCustom
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:self.model.mode == SYUserModelTypeLama ? @"lmsz-qd" : @"jqsz-qd" attributes:@{@"项目": @"周期长度"}];
            NSString *day = [result.resultString substringToIndex:result.resultString.length - 1];
            if (self.model.mode == SYUserModelTypePregnancy) {
                self.model.calculateInterval = day.integerValue;
                IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeTmpMenseInterval];
                cellModel.content = self.model.tmpMenseIntervalString;
                if (self.model.calculateDate) {
                    cellModel = [self cellModelForType:SYQuickStartTypeDueDate];
                    cellModel.content = [self dueDateStringWithCalculate:YES];
                    [self showCalculateResulte];
                }
            } else {
                self.model.pars_interval = day.integerValue;
                IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeMenseInterval];
                cellModel.content = self.model.menseIntervalString;
            }
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.title = IMYString(@"选择周期天数");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSInteger length = self.model.pars_interval ?: 28;
    if (self.model.mode == SYUserModelTypePregnancy) {
        length = self.model.calculateInterval ?: 28;
    }
    [self.pickerView setSelectWithString:[NSString stringWithFormat:IMYString(@"%ld天"), length]];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeTmpMenseInterval];
}

- (void)showDueDatePicker {
    [IMYEventHelper event:@"ycqsz-xm" attributes:@{@"项目": @"预产期设置"}];
    @weakify(self);
    NSArray *data = @[[[NSDate imy_today] dateByAddingDays:-13], [[NSDate imy_today] dateByAddingDays:280]];
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:data
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:@"ycqsz-qd"
                       attributes:@{@"项目": @"设置预产期"}];
            self.model.date = result.resultDate;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeDueDate];
            cellModel.content = [self dueDateStringWithCalculate:NO];
            [self.tableView reloadData];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    [picker setSelectedRowDidChangedBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray){

    }];
    picker.title = IMYString(@"选择预产期");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSDate *selectDate = self.model.date ? self.model.date : [[NSDate imy_today] dateByAddingDays:250];
    [self.pickerView setSelectWithDate:selectDate];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeDueDate];
}

- (void)showBabySexPicker {
    [IMYEventHelper event:@"lmsz-xm" attributes:@{@"项目": @"宝宝性别"}];
    NSMutableArray *array = [[NSMutableArray alloc] init];
    [array addObject:IMYString(@"小公主")];
    [array addObject:IMYString(@"小王子")];
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[array]
        pickerViewTpe:IMYPickerViewTypeCustom
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:@"lmsz-qd" attributes:@{@"项目": @"宝宝性别"}];
            NSUInteger index = [resultArray[0] unsignedIntegerValue];
            self.model.babySex = index == 0 ? 2 : 1;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeSex];
            cellModel.content = self.model.babySexString;
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.title = IMYString(@"选择宝宝性别");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSInteger babySex = self.model.babySex ?: 2;
    [self.pickerView setSelectWithString:babySex == 1 ? IMYString(@"小王子") : IMYString(@"小公主")];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeSex];
}

- (void)showBirthdayPicker {
    [IMYEventHelper event:@"lmsz-xm" attributes:@{@"项目": @"宝宝出生日"}];
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[[@"2000-1-1" imy_getOnlyDate], [NSDate imy_today]]
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:@"lmsz-qd"
                       attributes:@{@"项目": @"宝宝生日"}];
            self.model.babyBirthday = result.resultDate;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeBirthday];
            cellModel.content = [self.model.babyBirthday getCNDateString];
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.title = IMYString(@"选择宝宝出生日");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSDate *selectDate = self.model.babyBirthday ? self.model.babyBirthday : [NSDate imy_today];
    [self.pickerView setSelectWithDate:selectDate];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeBirthday];
}

- (void)showCalculatePicker {
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[[[NSDate imy_today] dateByAddingDays:-280], [NSDate imy_today]]
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [IMYEventHelper event:@"ycqsz-qd"
                       attributes:@{@"项目": @"计算预产期"}];
            self.model.calculateDate = result.resultDate;

            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeTmpLastMense];
            cellModel.content = [self.model.calculateDate getCNDateString];
            if (self.model.calculateInterval) {
                cellModel = [self cellModelForType:SYQuickStartTypeDueDate];
                cellModel.content = [self dueDateStringWithCalculate:YES];
                [self showCalculateResulte];
            }

            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];

    picker.title = IMYString(@"最后一次经期开始日");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSDate *selectDate = self.model.calculateDate ? self.model.calculateDate : [NSDate imy_today];
    [self.pickerView setSelectWithDate:selectDate];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeTmpLastMense];
}

- (void)showUserBirthdayDatePicker {

    BOOL newPicker = NO;
    IMYABTestExperiment *experiment = [self periodSetAgeExperiment];
    if (experiment) {
        newPicker = [experiment.vars boolForKey:@"new_picker"];
    }
    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:@[[@"1900-1-1" imy_getOnlyDate], [NSDate imy_today]]
        pickerViewTpe:IMYPickerViewTypeDate
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [self ga_birthday_ok];
            NSDateComponents *component = [[NSDate imy_getCNCalendar] components:NSCalendarUnitDay | NSCalendarUnitMonth | NSCalendarUnitYear fromDate:result.resultDate toDate:[NSDate date] options:NSCalendarWrapComponents];
            if (newPicker && component.year < 8) {//小于8岁
                [UIWindow imy_showTextHUD:IMYString(@"年龄须大于8岁")];
            } else {
                [self.pickerView hide];
                self.model.birthdayDate = result.resultDate;
                IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeUserBirthdayDate];
                cellModel.content = [self.model.birthdayDate getCNDateString];
                [self.tableView reloadData];
                [self checkInput];;
            }
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.outDismissControl = YES;
    picker.title = IMYString(@"选择出生日期");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSDate *selectDate = self.model.birthdayDate ? self.model.birthdayDate : [newPicker?@"2020-01-01":@"2000-06-15" imy_getOnlyDate];
    [self.pickerView setSelectWithDate:selectDate];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeUserBirthdayDate];
}

- (void)showUserBirthdayOnlyAgePicker {

    NSDate *today = [NSDate imy_today];
    NSInteger startYear = 1900;
    NSInteger endYear = today.year;
    NSMutableArray *innerDataArray = [[NSMutableArray alloc] init];
    for (NSInteger i = startYear; i <= endYear; i++) {
        NSString *string = [NSString stringWithFormat:IMYString(@"%ld年"), i];
        [innerDataArray addObject:string];
    }
    NSArray *dataArray = @[innerDataArray];

    @weakify(self);
    IMYPickerView *picker = [IMYPickerView pickerViewAtKeyWindowWithEnableCover:YES
        dataArray:dataArray
        pickerViewTpe:IMYPickerViewTypeCustom
        confirmBlock:^(IMYPickerViewResultModel *result, NSArray *resultArray) {
            @strongify(self);
            [self ga_birthday_ok];
            NSInteger index = [resultArray.firstObject integerValue];
            self.model.birth_year = startYear + index;
            IMYSimpleCellModel *cellModel = [self cellModelForType:SYQuickStartTypeUserBirthdayYear];
            cellModel.content = result.resultString;
            [self.tableView reloadData];
            [self checkInput];
        }
        cancelBlock:^{
            @strongify(self);
            [self resetOffset];
        }];
    picker.title = IMYString(@"选择出生年份");
    picker.autoReleaseSelf = YES;
    self.pickerView = picker;
    NSString *selectString = (self.model.birth_year > 0) ? [NSString stringWithFormat:IMYString(@"%ld年"), self.model.birth_year] : IMYString(@"2000年");
    [self.pickerView setSelectWithString:selectString];
    [self.pickerView show];

    [self avoidCoverType:SYQuickStartTypeUserBirthdayYear];
}

- (void)showCalculateResulte {
    //    根据推算你的预产期时间为：xxxx年xx月xx日
    NSString *result = [NSString stringWithFormat:IMYString(@"根据推算你的预产期时间为：%@"), [self.model.date getCNDateString]];
    [self imy_showTextHUD:result];
}

#pragma mark - check

- (IMYSimpleCellModel *)cellModelForType:(NSInteger)type {
    return [self.items match:^BOOL(IMYSimpleCellModel *_Nonnull element) {
        if ([element isKindOfClass:[IMYSimpleCellModel class]]) {
            return element.type == type;
        }
        return NO;
    }];
}

- (NSIndexPath *)indexPathForType:(NSInteger)type {
    __block NSInteger row = NSNotFound;
    [self.items enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if ([obj isKindOfClass:[IMYSimpleCellModel class]]) {
            IMYSimpleCellModel *cellModel = (IMYSimpleCellModel *)obj;
            if (cellModel.type == type) {
                row = idx;
                *stop = YES;
            }
        }
    }];

    if (row != NSNotFound) {
        return [NSIndexPath indexPathForRow:row inSection:0];
    }
    return nil;
}

//检查数据是否完整
- (BOOL)checkDataOk {
    if (self.model.mode < SYUserModelTypePregnancy) {
        if (self.model.pars_menses_day == 0) {
            [UIWindow imy_showTextHUD:IMYString(@"经期长度还没填哦~")];
            return NO;
        }
        if (self.model.pars_interval == 0) {
            [UIWindow imy_showTextHUD:IMYString(@"周期长度还没填哦~")];
            return NO;
        }
        if (!self.model.lastMenseDate) {
            [UIWindow imy_showTextHUD:IMYString(@"最近一次月经还没填哦~")];
            return NO;
        }
    } else if (self.model.mode == SYUserModelTypePregnancy) {
        if (!self.model.date) {
            [UIWindow imy_showTextHUD:IMYString(@"请先设置预产期哦~")];
            return NO;
        }
    } else {
        if (self.model.babySex == 0) {
            [UIWindow imy_showTextHUD:IMYString(@"宝宝的性别还没填哦~")];
            return NO;
        } else if (!self.model.babyBirthday) {
            [UIWindow imy_showTextHUD:IMYString(@"宝宝出生日期还没填哦~")];
            return NO;
        }
    }
    return YES;
}
//检查预产期是否合法
- (BOOL)checkDatePicker:(NSDate *)date {
    if (self.model.mode == SYUserModelTypePregnancy) {
        if ([date getDayDiff:[NSDate imy_today]] > 14) {
            [UIWindow imy_showTextHUD:IMYString(@"美柚不支持输入历史的怀孕记录哦~")];
            return NO;
        }
        if ([[NSDate imy_today] getDayDiff:date] > 280) {
            [UIWindow imy_showTextHUD:IMYString(@"你输入的预产期距离今天超过280天，不能输入未来的怀孕记录~")];
            return NO;
        }
    }
    return YES;
}
//检查是否输入完整,自动弹出
- (void)checkInput {
    if (self.model.mode == SYUserModelTypeNormal) {
        if (self.model.pars_menses_day == 0) {
            [self showMenseDayPicker];
        } else if (self.model.pars_interval == 0) {
            [self showMenseIntervalPicker];
        } else if (!self.model.lastMenseDate) {
            [self showLastMensePicker];
        } else {
            [self resetOffset];
        }
    } else if (self.model.mode == SYUserModelTypeForPregnant) {
        if (self.model.pars_menses_day == 0) {
            [self showMenseDayPicker];
        } else if (self.model.pars_interval == 0) {
            [self showMenseIntervalPicker];
        } else if (!self.model.lastMenseDate) {
            [self showLastMensePicker];
        } else {
            [self resetOffset];
        }
    } else if (self.model.mode == SYUserModelTypePregnancy) {
        if (!self.model.calculateDate) {
            [self showCalculatePicker];
        } else if (!self.model.calculateInterval) {
            [self showMenseIntervalPicker];
        } else {
            [self resetOffset];
        }
    } else {
        if (self.model.babySex == 0) {
            [self showBabySexPicker];
        } else if (!self.model.babyBirthday) {
            [self showBirthdayPicker];
        } else {
            [self resetOffset];
        }
    }

    [self addPickerTouchNotPickerSpaceAction];
}

- (void)finish {
    if ([self checkDataOk] == NO) {
        return;
    }
    if (!self.checkPrivateView.checkSeleted) {
        [self.checkPrivateView shake];
        [UIWindow imy_showTextHUD:IMYString(@"请勾选同意服务协议和隐私政策")];
        return;
    }
    NSString *stringPregnancy = nil;
    if (self.model.mode == SYUserModelTypeNormal || self.model.mode == SYUserModelTypeForPregnant) {
        [SYUserHelper sharedHelper].pars_menses_day = self.model.pars_menses_day;
        [SYUserHelper sharedHelper].pars_interval = self.model.pars_interval;
        NSDate *date = self.model.lastMenseDate;
        IMYDayRecordModel *daymodel = [IMYDayRecordDao searchDayRecordWithDate:date];
        if (!daymodel) {
            daymodel = [IMYDayRecordModel new];
            daymodel.date = date;
        }
        daymodel.isEnd = NO;
        daymodel.isBegin = YES;
        [IMYDayRecordDao mensesInsertToDB:daymodel];
    } else if (self.model.mode == SYUserModelTypePregnancy) {
        NSDate *dueDate = self.model.date;
        if (self.isUseCalculate) {
            [SYUserHelper sharedHelper].pars_interval = self.model.calculateInterval;
        }
        NSDate *startDate = [self.model.date dateByAddingDays:-280];
        if ([startDate compare:[NSDate imy_today]] == NSOrderedDescending) {
            startDate = [NSDate imy_today];
        }
        [IMYDayRecordModel setPregnancyDueDate:dueDate startDate:startDate]; //开始 孕期  预产期时间 和 怀孕日

        IMYDayRecordModel *daymodel = [IMYDayRecordDao searchDayRecordWithDate:startDate];
        if (!daymodel) {
            daymodel = [IMYDayRecordModel new];
            daymodel.date = startDate;
        }
        daymodel.isEnd = NO;
        daymodel.isBegin = YES;
        [IMYDayRecordDao mensesInsertToDB:daymodel];
    } else if (self.model.mode == SYUserModelTypeLama) {
        //TODO Review 8.84.0 移除 client_id
        
//        IMYRecordBabyModel *baby = [[IMYRecordBabyModel alloc] init];
//        [baby addPropertyChangeListener];
//        baby.gender = self.model.babySex;
//        baby.birthday = [self.model.babyBirthday imy_getOnlyDateString];
//        [baby saveToDB];
//        [[IMYRecordBabyManager sharedInstance] selectBaby:baby.client_id];
        
        //记录下用户手动切换到辣妈身份的时间
        NSTimeInterval time = [[NSDate imy_today] timeIntervalSince1970];
        NSString *key = [NSString stringWithFormat:@"userChangeToLamaModeTimestamp-%@", [IMYPublicAppHelper shareAppHelper].userid];
        [[IMYUserDefaults standardUserDefaults] setDouble:time forKey:key];
    }
    
    //广告V7.7.0辣妈身份banner广告需求（0-6个月和6个月后）：//lamaModeAdType辣妈身份下banner广告类型：1.新注册辣妈用户; 2.切换辣妈身份用户; 3.其他
    if (self.model.mode == SYUserModelTypeLama && self.isLaunchRegister && ![[[IMYUserDefaults standardUserDefaults] objectForKey:@"SYLamaModelAdBannerType"] integerValue]) {
        [IMYPublicAppHelper shareAppHelper].userNewState = 1;
        [[IMYUserDefaults standardUserDefaults] setInteger:1 forKey:@"SYLamaModelAdBannerType"];
    } else {
        [[IMYUserDefaults standardUserDefaults] setInteger:3 forKey:@"SYLamaModelAdBannerType"];
    }
    /// 首次注册怀孕模式为1，否则为3
    if (self.model.mode == SYUserModelTypePregnancy && self.isLaunchRegister && ![[IMYUserDefaults standardUserDefaults] integerForKey:@"IMYVKUserModePregnancyType"]) {
        [IMYPublicAppHelper shareAppHelper].userNewState = 1;
        [[IMYUserDefaults standardUserDefaults] setInteger:1 forKey:@"IMYVKUserModePregnancyType"];
    } else {
        [[IMYUserDefaults standardUserDefaults] setInteger:3 forKey:@"IMYVKUserModePregnancyType"];
    }
    // 首次注册备孕模式为1，否则为3
    if (self.model.mode == SYUserModelTypeForPregnant && self.isLaunchRegister && ![[IMYUserDefaults standardUserDefaults] integerForKey:@"IMYVKUserModeForPregnantType"]) {
        [IMYPublicAppHelper shareAppHelper].userNewState = 1;
        [[IMYUserDefaults standardUserDefaults] setInteger:1 forKey:@"IMYVKUserModeForPregnantType"];
    } else {
        [[IMYUserDefaults standardUserDefaults] setInteger:3 forKey:@"IMYVKUserModeForPregnantType"];
    }
    // 首次注册经期模式为1，否则为3
    if (self.model.mode == SYUserModelTypeNormal && self.isLaunchRegister && ![[IMYUserDefaults standardUserDefaults] integerForKey:@"IMYVKUserModeForNormalType"]) {
        [IMYPublicAppHelper shareAppHelper].userNewState = 1;
        [[IMYUserDefaults standardUserDefaults] setInteger:1 forKey:@"IMYVKUserModeForNormalType"];
    } else {
        [[IMYUserDefaults standardUserDefaults] setInteger:3 forKey:@"IMYVKUserModeForNormalType"];
    }
    
    [SYUserHelper sharedHelper].userModelType = self.model.mode;
    [SYUserHelper sharedHelper].birthday = [self.model.birthdayDate imy_getOnlyDateString];
    [[SYUserHelper sharedHelper] saveToDB];
    
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeNormal) {
//        //上传“青少年模式”开关值
//        BOOL isYouthMode = !(imy_isEmptyString([IMYPublicAppHelper shareAppHelper].birthday) || [IMYPublicAppHelper shareAppHelper].userAge >= 18);
//        [SYPublicFun setIsYouthModeEnable:isYouthMode];
//        [[SYUserHelper sharedHelper] uploadUserSettings:NULL];
    }
    
    [[IMYCalendarUserHelper sharedHelper] recalculateUserInfo];
    IMY_POST_NOTIFY(ChangedUserModeNotification);
    [SYCCommon sharedSYCCommon].isRejectSync = NO;
    if (_callback) {
        _callback(stringPregnancy);
    }
    if (self.finishedBlock) {
        self.finishedBlock(self);
    }
    
    if (self.model.mode == SYUserModelTypeLama) {
        [self handleFirstLamaMode];
    }
}

- (void)handleFirstLamaMode{

    ///跳转到智能识别
    NSMutableDictionary *params = [[NSMutableDictionary alloc]  init];
    [params imy_setNonNilObject:[NSDate new] forKey:@"dueDate"];
    [params imy_setNonNilObject:@(-1) forKey:@"baby_id"];
    [params imy_setNonNilObject:@(9) forKey:@"position"];
    NSString *uriString = @"seeyoubaby/photo/show_leadpage_v2";
    IMYURI *uri = [IMYURI uriWithPath:uriString params:params info:nil];
    IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
    actionObject.implCallbackBlock = ^(id result, NSError *error, NSString *eventName) {
        BOOL downloadFailed = [[result objectForKey:@"downloadFailed"] boolValue];
        if (downloadFailed) {
            
        }
    };
    [[IMYURIManager shareURIManager] runActionWithActionObject:actionObject completed:nil];
}

#pragma mark - Data

- (NSArray *)items {
    if (!_items) {
        NSMutableArray *array = [NSMutableArray array];

        if (self.model.mode < SYUserModelTypePregnancy) {

            [array addObject:self.sectionTitles[0]];
            IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_jingqi";
            cellModel.title = IMYString(@"经期长度");
            cellModel.content = self.model.pars_menses_day ? self.model.menseDayString : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeMenseDay;
            [array addObject:cellModel];

            [array addObject:self.sectionTitles[1]];
            cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_zhouqi";
            cellModel.title = IMYString(@"周期长度");
            cellModel.content = self.model.pars_interval ? self.model.menseIntervalString : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeMenseInterval;
            [array addObject:cellModel];

            [array addObject:self.sectionTitles[2]];
            cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_star";
            cellModel.title = IMYString(@"最近一次月经");
            cellModel.content = self.model.lastMenseDate ? [self.model.lastMenseDate getCNDateString] : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeLastMense;
            [array addObject:cellModel];


            if (self.model.mode <= SYUserModelTypeNormal) {
                cellModel = [IMYSimpleCellModel new];
                cellModel.icon = @"data_icon_birthday";
                cellModel.title = IMYString(@"你的出生日期");
                cellModel.content = self.model.birthdayDate ? [self.model.birthdayDate getCNDateString] : IMYString(@"未选择");
                cellModel.type = SYQuickStartTypeUserBirthdayDate;
                
                BOOL isFirst = NO;
                IMYABTestExperiment *experiment = [self periodSetAgeExperiment];
                if (experiment) {
                    isFirst = [experiment.vars boolForKey:@"is_first"];
                }
                if (isFirst) {
                    [array insertObject:cellModel atIndex:0];
                    [array insertObject:self.sectionTitles[3] atIndex:0];
                } else {
                    [array addObject:self.sectionTitles[3]];
                    [array addObject:cellModel];
                }
            }
        } else if (self.model.mode == SYUserModelTypePregnancy) {
            [array addObject:self.sectionTitles[0]];
            IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_yuchanqi.png";
            cellModel.title = IMYString(@"设置预产期");
            cellModel.content = self.model.date ? [self dueDateStringWithCalculate:NO] : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeDueDate;
            [array addObject:cellModel];
            //插入一个空格
            [array addObject:[NSNull null]];

            cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"mine_icon_jisuan.png";
            cellModel.title = IMYString(@"计算预产期");
            cellModel.type = SYQuickStartTypeExpand;
            [array addObject:cellModel];

        } else {
            [array addObject:self.sectionTitles[0]];
            IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_sex";
            cellModel.title = IMYString(@"宝宝性别");
            cellModel.content = self.model.babySex ? self.model.babySexString : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeSex;
            [array addObject:cellModel];

            [array addObject:self.sectionTitles[1]];
            cellModel = [IMYSimpleCellModel new];
            cellModel.icon = @"data_icon_baby";
            cellModel.title = IMYString(@"宝宝出生日");
            cellModel.content = self.model.babyBirthday ? [self.model.babyBirthday getCNDateString] : IMYString(@"未选择");
            cellModel.type = SYQuickStartTypeBirthday;
            [array addObject:cellModel];
        }

        _items = [NSMutableArray arrayWithArray:array];
    }
    return _items;
}

- (NSArray *)sectionTitles {
    if (!_sectionTitles) {
        if (self.model.mode < SYUserModelTypePregnancy) {
            NSString *ageTitle = IMYString(@"了解你的年龄可以作出更准确的经期预测（选填）");
            IMYABTestExperiment *experiment = [self periodSetAgeExperiment];
            if (experiment) {
                NSString *instruction = [experiment.vars stringForKey:@"instruction"];
                if (instruction.length > 0 && ![instruction isEqualToString:@"0"]) {
                    ageTitle = instruction;
                }
            }
            _sectionTitles = @[IMYString(@"你的月经大概持续几天？"), IMYString(@"两次月经开始日大概间隔多久？"), IMYString(@"最近一次月经大概是哪天来的？"), ageTitle];
        } else if (self.model.mode == SYUserModelTypePregnancy) {
            _sectionTitles = @[IMYString(@"预产期是哪天？")];
        } else {
            _sectionTitles = @[IMYString(@"选择宝宝性别"), IMYString(@"宝宝是在哪天出生的？")];
        }
    }
    return _sectionTitles;
}

- (IMYABTestExperiment *)periodSetAgeExperiment {
    IMYABTestExperiment *experiment = [[IMYABTestManager sharedInstance] experimentForKey:@"period_set_age"];
    if (experiment.status == IMYABTestExpStatusModuleIsolated) {
        return nil;
    }
    return experiment;
}

- (void)reloadPregnacyItem {
    NSMutableArray *array = (NSMutableArray *)self.items;
    SYUseModeTableViewCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:3 inSection:0]];
    if (self.isExpended) {
        IMYSimpleCellModel *cellModel = [IMYSimpleCellModel new];
        cellModel.title = IMYString(@"最后一次经期开始日");
        cellModel.content = self.model.calculateDate ? [self.model.calculateDate getCNDateString] : IMYString(@"未选择");
        cellModel.type = SYQuickStartTypeTmpLastMense;
        [array addObject:cellModel];

        cellModel = [IMYSimpleCellModel new];
        cellModel.title = IMYString(@"周期长度");
        cellModel.content = self.model.calculateInterval ? [NSString stringWithFormat:IMYString(@"%ld天"), self.model.calculateInterval] : IMYString(@"未选择");
        cellModel.type = SYQuickStartTypeTmpMenseInterval;
        [array addObject:cellModel];
        cell.arrow.transform = CGAffineTransformRotate(CGAffineTransformIdentity, M_PI_2);
    } else {
        IMYSimpleCellModel *cellModel = array.lastObject;
        if (cellModel.type == SYQuickStartTypeTmpMenseInterval) {
            [array removeLastObject];
            [array removeLastObject];
            cell.arrow.transform = CGAffineTransformIdentity;
        }
    }
    [self.tableView reloadData];
}

- (void)helperView {
    [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": [NSString stringWithFormat:@"%@/help/yuchanqi.html", view_seeyouima_com]} info:nil];
}

- (NSString *)dueDateStringWithCalculate:(BOOL)isCalculate {
    NSInteger week = 0;
    NSInteger day = 1;
    self.isUseCalculate = isCalculate;
    NSDate *startDate = [self.model.date dateByAddingDays:-280];
    NSInteger daydiff = [startDate getDayDiff:[NSDate imy_today]];
    week = daydiff / 7;
    day = daydiff % 7;
    if (daydiff == 0) {
        day = 1;
    }
    if (day == 0) {
        return [NSString stringWithFormat:IMYString(@"%@（孕%d周）"), [self.model.date getCNDateString], week];
    }
    return [NSString stringWithFormat:IMYString(@"%@（孕%d周%d天）"), [self.model.date getCNDateString], week, day];
}

#pragma mark - TableView delegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.items.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    id model = self.items[indexPath.row];
    if ([model isKindOfClass:[NSString class]]) {
        CGSize size = [(NSString *)model boundingRectWithSize:CGSizeMake(SCREEN_WIDTH - 30, MAXFLOAT) options:NSStringDrawingTruncatesLastVisibleLine | NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:14]} context:nil].size;
        return MAX(52, size.height + 16);
    }
    if ([model isKindOfClass:[NSNull class]]) {
        return 24;
    }
    IMYSimpleCellModel *cellModel = model;
    if (cellModel.type == SYQuickStartTypeDueDate) {
        if (![cellModel.content isEqualToString:IMYString(@"未选择")]) {
            return 70;
        }
    }
    return 48;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    id obj = self.items[indexPath.row];
    if ([obj isKindOfClass:[NSString class]]) {
        static NSString *textCellIndentifier = @"textCellIndentifier";
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:textCellIndentifier];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:textCellIndentifier];
            [cell imy_setupClearBackground];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            UILabel *label = [UILabel new];
            label.font = [UIFont systemFontOfSize:(iPhone5 ? 13 : 14)];
            [label imy_setTextColorForKey:kCK_Black_B];
            label.tag = 101;
            label.adjustsFontSizeToFitWidth = YES;
            label.numberOfLines = 0;
            [cell.contentView addSubview:label];
            
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(cell.contentView.mas_left).offset(15);
                make.right.mas_equalTo(cell.contentView.mas_right).offset(-15);
                make.bottom.mas_equalTo(cell.contentView.mas_bottom).offset(-8);
            }];
        }
        UILabel *label = (id)[cell.contentView viewWithTag:101];
        label.text = obj;
        return cell;
    } else if ([obj isKindOfClass:[NSNull class]]) {
        static NSString *emtpyCell = @"emptyCell";
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:emtpyCell];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:emtpyCell];
            [cell imy_setupClearBackground];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }
        return cell;
    } else {
        SYUseModeTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SYUseModeTableViewCell" forIndexPath:indexPath];
        IMYSimpleCellModel *model = obj;
        BOOL isTowRow = NO;
        if (model.type == SYQuickStartTypeDueDate && ![model.content isEqualToString:IMYString(@"未选择")]) {
            isTowRow = YES;
        }
        [cell setCellTypeTwoRow:isTowRow];
        [cell.icon imy_setImageForKey:model.icon];
        cell.title.imy_left = model.icon ? 55 : 15;
        cell.title.text = model.title;
        if (iPhone5) {
            cell.detailLabel.font = [UIFont systemFontOfSize:13];
        }
        cell.detailLabel.text = model.content;
        if (model.type == SYQuickStartTypeExpand) {
            [cell updateArrowMarginIsLeft:YES];
        } else {
            [cell updateArrowMarginIsLeft:NO];
        }
        IMYLineView *line = [cell.contentView imy_lineViewWithDirection:IMYDirectionUp show:YES margin:15];
        if (model.type == SYQuickStartTypeTmpLastMense || model.type == SYQuickStartTypeTmpMenseInterval) {
            line.hidden = NO;
        } else {
            line.hidden = YES;
        }
        return cell;
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    id obj = self.items[indexPath.row];
    if ([obj isKindOfClass:[IMYSimpleCellModel class]]) {
        IMYSimpleCellModel *item = (IMYSimpleCellModel *)obj;
        if (item.type == SYQuickStartTypeExpand) {
            self.isExpended = !self.isExpended;
            [self reloadPregnacyItem];
        } else {
            [self showPicker:item];
        }
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (self.model.mode == SYUserModelTypePregnancy) {
        return 24;
    }
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (self.model.mode == SYUserModelTypePregnancy) {
        return self.introduceView;
    }
    return nil;
}

#pragma mark - UI

- (void)initTitles {
    if (self.model.mode < SYUserModelTypePregnancy) {
        self.navigationItem.title = IMYString(@"经期设置");
    } else if (self.model.mode == SYUserModelTypePregnancy) {
        self.navigationItem.title = IMYString(@"预产期设置");
    } else {
        self.navigationItem.title = IMYString(@"辣妈设置");
    }
}

- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
        [tableView imy_makeTransparent];
        tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        tableView.showsVerticalScrollIndicator = NO;
        tableView.delegate = self;
        tableView.dataSource = self;
        tableView.tableFooterView = [self footerView];
        [tableView registerClass:[SYUseModeTableViewCell class] forCellReuseIdentifier:@"SYUseModeTableViewCell"];
        _tableView = tableView;
    }
    return _tableView;
}

- (IMYAccountPrivacyUsageStatementView *)privacyView {
    if (!_privacyView) {
        IMYVKUserMode userMode = IMYVKUserModeNormal;
        switch (self.model.mode) {
            case SYUserModelTypeLama:
                userMode = IMYVKUserModeLama;
                break;
            case SYUserModelTypePregnancy:
                userMode = IMYVKUserModePregnancy;
                break;
            case SYUserModelTypeForPregnant:
                userMode = IMYVKUserModeForPregnant;
                break;
            default:
                userMode = IMYVKUserModeNormal;
                break;
        }
        _privacyView = [IMYAccountPrivacyUsageStatementView getQuickStarPrivacyUsageStatementViewWithMode:userMode];
        _privacyView.imy_centerX = SCREEN_WIDTH / 2.0;
        CGFloat gap = (IMYSystem.screenHeight <= 568) ? 8 : 20;
        _privacyView.imy_bottom = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_TABBAR_SAFEBOTTOM_MARGIN - gap;
    }
    return _privacyView;
}

- (UIView *)footerView {
    UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.imy_width, 24 * 2 + 48 + 16 + 17)];
    if (iPhone5) {
        footerView.imy_height += 140;
    }
    UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 24, SCREEN_WIDTH, 48)];
    [button imy_setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_White_AN) andSize:CGSizeMake(10, 10)]];
    [button setBackgroundImage:[UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_H) andSize:CGSizeMake(10, 10)] forState:UIControlStateHighlighted];
    [button imy_setTitle:IMYString(@"开始使用")];
    button.titleLabel.font = [UIFont systemFontOfSize:16];
    [button imy_setTitleColor:kCK_Black_AT];
    [footerView addSubview:button];
    @weakify(self);
    [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self);
        [[SYCCommon sharedSYCCommon] requestNewUserGuide];
        if (self.model.mode == SYUserModelTypePregnancy) {
            [IMYEventHelper event:@"ycqsz-kssy"];
        } else if (self.model.mode == SYUserModelTypeLama) {
            [IMYEventHelper event:@"lmsz-kssy"];
        } else {
            [IMYEventHelper event:@"jqsz-kssy"];
        }
        [self finish];
    }];
    
    self.checkPrivateView = [[IMYAccountCheckPrivateView alloc] initWithFrame:CGRectMake(0, button.imy_bottom + 16, SCREEN_WIDTH, 17)];
    self.checkPrivateView.checkSeleted = NO;//self.didCheckPrivate;
    [self.checkPrivateView setDidChangedCheckState:^(BOOL seleted) {
        @strongify(self);
        self.didCheckPrivate = seleted;
    }];
    [footerView addSubview:self.checkPrivateView];
    return footerView;
}

- (UIView *)introduceView {
    if (!_introduceView) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.imy_width, 30)];
        IMYButton *button = [[IMYButton alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
        button.titleLabel.font = [UIFont systemFontOfSize:14];
        [button imy_setTitleColorForKey:kCK_Red_B andState:UIControlStateNormal];
        [button imy_setTitle:IMYString(@"预产期计算说明")];
        [button sizeToFit];
        [button setExtendTouchInsets:UIEdgeInsetsMake(10, 0, 10, 0)];
        [view addSubview:button];
        button.imy_right = view.imy_right - 30;
        button.imy_bottom = view.imy_bottom;
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [self helperView];
        }];
        _introduceView = view;
    }
    return _introduceView;
}

- (void)avoidCoverType:(SYQuickStartType)type {
    NSIndexPath *indexPath = [self indexPathForType:type];
    if (indexPath) {
        UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
        [self avoidCover:cell];
    }
}

- (void)avoidCover:(UITableViewCell *)cell {
    CGRect rect = [self.tableView convertRect:cell.frame toView:self.view];
    //
    CGFloat distance = rect.origin.y + rect.size.height - (self.view.frame.size.height - self.pickerView.imy_height);
    if (distance > 0) {
        CGPoint point = self.tableView.contentOffset;
        point.y += distance;
        [self.tableView setContentOffset:point animated:YES];
    }
}

- (void)resetOffset {
    [self resetOffset:YES];
}

- (void)resetOffset:(BOOL)animated {
    CGFloat contentSizeHeight = MAX(self.tableView.contentSize.height, self.tableView.imy_height);
    if (self.tableView.contentOffset.y + self.tableView.imy_height > contentSizeHeight) {
        CGPoint point = CGPointMake(0, contentSizeHeight - self.tableView.imy_height);
        [self.tableView setContentOffset:point animated:animated];
    }
}

#pragma mark - Ga
- (NSString *)ga_pageName {
    if (self.model.mode == SYUserModelTypeNormal) {
        return @"SYQuickStartDetailVC_1";
    } else if (self.model.mode == SYUserModelTypeForPregnant) {
        return @"SYQuickStartDetailVC_2";
    } else if (self.model.mode == SYUserModelTypePregnancy) {
        return @"SYQuickStartDetailVC_3";
    } else if (self.model.mode == SYUserModelTypeLama) {
        return @"SYQuickStartDetailVC_4";
    }
    return @"SYQuickStartDetailVC";
}

- (void)ga_birthday_ok {
    [IMYGAEventHelper postWithPath:@"event"
                            params:@{@"event": @"jqszsr_qd",
                                     @"action": @2}
                           headers:nil
                         completed:nil];
}
@end
