# MARK: converted automatically by spec.py. @hgy

Pod::Spec.new do |s|
	s.name = 'IMYAccount'
	s.version = '8.91.005'
	s.license = 'MIT'
	s.summary = 'IMYAccount'
	s.homepage = 'https://github.com/meiyoudev/IMYAccount'
	s.authors = { 'xyz' => '<EMAIL>' }
	s.source = { :git => '*********************:iOS/IMYAccount.git', :branch => 'release-jingqi-8.91.0' }
	s.requires_arc = true
	s.ios.deployment_target = '11.0'

	s.source_files = 'Source/Classes/**/*.{h,m,c}'
	s.resources = 'Source/Classes/**/*.{json,png,jpg,gif,xib,bundle,html}',
				  'Source/Resources/**/*.{json,png,jpg,gif,xib,bundle,html}',
				  'Source/Bundles/*.{bundle,xcassets}'


	s.weak_frameworks = 'AuthenticationServices'

	s.dependency 'TTTAttributedLabel','2.0.0'
	s.dependency 'IMYBaseKit'
    s.dependency 'IMYDynamicFrameworks'
    s.dependency 'HWPanModal','0.8.9'

	# 本来打算分成两个模块，一个业务接口模块，一个通用页面模块
	# 但是，业务接口模块依赖了通用页面模块，导致无法真的完全解构
	# s.default_subspec = ['CoreService']

	# s.subspec "CoreService" do |ss|
	# 	ss.source_files = 'CoreService/**/*.{h,m}'
	# 	ss.exclude_files = 'CoreService/Vendor/**/*.h'
	# 	ss.vendored_frameworks =  'CoreService/Vendor/*.framework'
	# 	ss.resources = 'CoreService/Vendor/*.bundle'
	# 	ss.dependency 'IMYPublic'
	# end

	# s.subspec "CommonUI" do |ss|
	# 	ss.dependency 'CoreService'
	# 	ss.dependency 'IMY_ViewKit'
	# 	ss.dependency 'TTTAttributedLabel','2.0.0'
	# end
end
