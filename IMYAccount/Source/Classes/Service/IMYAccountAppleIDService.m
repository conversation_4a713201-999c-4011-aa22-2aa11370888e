//
//  IMYAccountAppleIDService.m
//  IMYAccount
//
//  Created by ponyo on 2020/2/20.
//

#import "IMYAccountAuthService.h"
#import "IMYAccountAppleIDService.h"
#import "IMYAccountAuthService.h"
#import <IMYBaseKit/IMYPublic.h>
#import <AuthenticationServices/AuthenticationServices.h>

@interface IMYAccountAppleIDService () <ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding>

@property (nonatomic, copy) void(^completioinHandler)(NSDictionary *credentialParams, NSError *error);
//@property (nonatomic, assign) BOOL isInFetchingMockData;
//@property (nonatomic, assign) BOOL isInMocking;// 正在走mock数据流程
//@property (nonatomic, copy) NSDictionary  *mockDic;// 保存mock数据

@end

@implementation IMYAccountAppleIDService

+ (instancetype)sharedInstance {
    static IMYAccountAppleIDService *single = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        single = [[self alloc] init];
    });
    return single;
}

+ (void)loginWithSessionConfig:(IMYAccountSessionConfigService *)sessionConfig {
    [[self sharedInstance] requestAuthorizationUIWithCompletionHandler:^(NSDictionary *credentialParams, NSError *error) {
        if (error.code == ASAuthorizationErrorCanceled) {// 点击取消
            !sessionConfig.failedHandler ?: sessionConfig.failedHandler(error);
            return;
        }
        if (error.code == ASAuthorizationErrorUnknown) {// 点击home
            return;
        }
        if (!error) {
            [IMYAccountAuthService loginWithAppleIDWithParams:credentialParams sessionConfig:sessionConfig];
        } else {
            [UIWindow imy_showTextHUDWithDelay:2 text:error.localizedDescription];
            
            // 上报 SDK 回调错误
            NSMutableDictionary *detail = @{
                @"domain" : error.domain ?: @"",
                @"code" : @(error.code),
                @"reason" : error.localizedFailureReason ?: error.localizedDescription,
            };
            [IMYErrorTraces postWithType:IMYErrorTraceTypePageFails pageName:IMYMeetyouHTTPHooks.currentPageName category:IMYErrorTraceCategoryUser message:@"appleid login sdk fail!" detail:detail.imy_jsonString];
        }
    }];
}

+ (NSString *)fullNameWithUser:(NSString *)user {
    NSString *key = [NSString stringWithFormat:@"APPLEID_FULLNAME_%@", user];
    return [[NSUserDefaults standardUserDefaults] objectForKey:key];
}


+ (void)setFullName:(NSString *)fullName user:(NSString *)user {
    NSString *key = [NSString stringWithFormat:@"APPLEID_FULLNAME_%@", user];
    [[NSUserDefaults standardUserDefaults] setObject:fullName forKey:key];
}

- (void)requestAuthorizationUIWithCompletionHandler:(void(^)(NSDictionary *credentialParams, NSError *error))completionHandler API_AVAILABLE(ios(13.0)) {
    if (@available(iOS 13.0, *)) { } else {
        // 防止闪退
        [UIWindow imy_showTextHUD:@"仅支持iOS13及以上系统"];
        return;
    }
    
    self.completioinHandler = [completionHandler copy];
//    if (self.isInMocking && self.mockDic) {
//       if (self.completioinHandler) {// 使用mock数据直接回调
//          self.completioinHandler(self.mockDic, nil);
//        }
//    } else {
        ASAuthorizationAppleIDProvider *appleIDProvider = [[ASAuthorizationAppleIDProvider alloc] init];
        ASAuthorizationAppleIDRequest *request = appleIDProvider.createRequest;
        request.requestedScopes = @[ASAuthorizationScopeFullName, ASAuthorizationScopeEmail];
        ASAuthorizationController *vc = [[ASAuthorizationController alloc] initWithAuthorizationRequests:@[request]];
        vc.delegate = self;
        //        vc.presentationContextProvider = self;
        [vc performRequests];
//    }
}

- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithAuthorization:(ASAuthorization *)authorization  API_AVAILABLE(ios(13.0)) {
    if ([authorization.credential isKindOfClass:ASAuthorizationAppleIDCredential.class]) {
        ASAuthorizationAppleIDCredential *credential = authorization.credential;
        // 授权的时候，返回email和fullname
        // 之后再用appleId登录，返回的email和fullname是空的
        NSString *fullName = [NSString stringWithFormat:@"%@%@", credential.fullName.familyName?:@"", credential.fullName.givenName?:@""];
        if (imy_isEmptyString(fullName)) {
            fullName = [self.class fullNameWithUser:credential.user];
        } else {
            [self.class setFullName:fullName user:credential.user];
        }
        NSDictionary *credentialDic = @{
            @"account" : credential.user ?: @"",
            @"auth_code" : [credential.authorizationCode responseString] ?: @"",
            @"id_token" : [credential.identityToken responseString] ?: @"",
            @"account_info" : @{
                    @"email" : credential.email ?: @"",
                    @"fullname" : fullName ?: @"",
                    @"real_user" : @(credential.realUserStatus)
            }
        };
//        if (self.isInFetchingMockData) {// 这次是为了拿到数据，用于下次mock
//            self.mockDic = [credentialDic copy];
//        } else {
            if (self.completioinHandler) {
               self.completioinHandler(credentialDic, nil);
            }
//        }
       
    }
    
}

- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithError:(NSError *)error  API_AVAILABLE(ios(13.0)) {
    if (self.completioinHandler) {
        self.completioinHandler(nil, error);
    }
}

//- (ASPresentationAnchor)presentationAnchorForAuthorizationController:(ASAuthorizationController *)controller  API_AVAILABLE(ios(13.0)) {
//    return nil;
//}

@end
